# 🔌 慢慢回收小程序 API接口文档

## 📋 接口概述

### 基础信息
- **Base URL**: `https://api.manmanrecycle.com/api`
- **协议**: HTTPS
- **数据格式**: JSON
- **字符编码**: UTF-8
- **认证方式**: JWT Bearer Token

### 通用响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": {},
  "timestamp": 1642234567890
}
```

### 状态码说明
| 状态码 | 说明 | 示例场景 |
|--------|------|----------|
| 200 | 成功 | 正常请求成功 |
| 201 | 创建成功 | 新增数据成功 |
| 400 | 请求参数错误 | 参数验证失败 |
| 401 | 未授权 | Token无效或过期 |
| 403 | 禁止访问 | 权限不足 |
| 404 | 资源不存在 | 数据不存在 |
| 409 | 请求冲突 | 数据重复 |
| 429 | 请求过于频繁 | 触发限流 |
| 500 | 服务器内部错误 | 系统异常 |

---

## 🔐 1. 认证模块 (Auth)

### 1.1 微信登录
**接口**: `POST /auth/wechat-login`

**请求参数**:
```json
{
  "code": "微信授权码",
  "userInfo": {
    "nickname": "用户昵称",
    "avatar": "头像URL",
    "gender": 1,
    "city": "城市",
    "province": "省份",
    "country": "国家"
  }
}
```

**响应数据**:
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "JWT_ACCESS_TOKEN",
    "refreshToken": "JWT_REFRESH_TOKEN",
    "user": {
      "id": 1,
      "openid": "wx_openid_001",
      "nickname": "环保达人",
      "avatar": "https://img.com/avatar.jpg",
      "phone": "138****8888",
      "city": "北京市",
      "createdAt": "2024-01-15T10:30:00.000Z"
    },
    "isNewUser": false
  }
}
```

### 1.2 刷新Token
**接口**: `POST /auth/refresh-token`

**请求参数**:
```json
{
  "refreshToken": "JWT_REFRESH_TOKEN"
}
```

**响应数据**:
```json
{
  "code": 200,
  "message": "Token刷新成功",
  "data": {
    "token": "NEW_JWT_ACCESS_TOKEN",
    "refreshToken": "NEW_JWT_REFRESH_TOKEN",
    "expiresIn": "7d"
  }
}
```

### 1.3 登出
**接口**: `POST /auth/logout`
**认证**: 需要Token

**响应数据**:
```json
{
  "code": 200,
  "message": "登出成功",
  "data": null
}
```

### 1.4 验证Token
**接口**: `GET /auth/verify`
**认证**: 需要Token

**响应数据**:
```json
{
  "code": 200,
  "message": "Token验证成功",
  "data": {
    "user": {
      "id": 1,
      "nickname": "环保达人",
      "avatar": "https://img.com/avatar.jpg"
    },
    "valid": true
  }
}
```

---

## 👤 2. 用户模块 (User)

### 2.1 获取用户信息
**接口**: `GET /user/profile`
**认证**: 需要Token

**响应数据**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "id": 1,
    "nickname": "环保达人",
    "avatar": "https://img.com/avatar.jpg",
    "phone": "138****8888",
    "city": "北京市",
    "joinDate": "2024-01-01",
    "stats": {
      "recycleCount": 15,
      "totalEarnings": 328.50,
      "carbonReduction": 12.5
    }
  }
}
```

### 2.2 更新用户信息
**接口**: `PUT /user/profile`
**认证**: 需要Token

**请求参数**:
```json
{
  "nickname": "新昵称",
  "avatar": "https://img.com/new_avatar.jpg",
  "phone": "13800138001",
  "city": "上海市"
}
```

### 2.3 获取用户地址
**接口**: `GET /user/addresses`
**认证**: 需要Token

**响应数据**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": 1,
      "name": "张三",
      "phone": "13800138001",
      "address": "朝阳区建国门外大街1号",
      "lng": 116.407400,
      "lat": 39.904200,
      "isDefault": true
    }
  ]
}
```

### 2.4 添加用户地址
**接口**: `POST /user/addresses`
**认证**: 需要Token

**请求参数**:
```json
{
  "name": "张三",
  "phone": "13800138001",
  "address": "朝阳区建国门外大街1号",
  "lng": 116.407400,
  "lat": 39.904200,
  "isDefault": false
}
```

### 2.5 获取用户收藏
**接口**: `GET /user/favorites`
**认证**: 需要Token

**查询参数**:
- `type`: 收藏类型 (1-站点 2-产品)
- `page`: 页码 (默认1)
- `limit`: 每页数量 (默认20)

**响应数据**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": 1,
      "type": 1,
      "targetId": 1,
      "createdAt": "2024-01-15T10:30:00.000Z",
      "target": {
        "id": 1,
        "name": "绿色回收站",
        "image": "https://img.com/station.jpg",
        "rating": 4.8
      }
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 5,
    "pages": 1
  }
}
```

### 2.6 添加收藏
**接口**: `POST /user/favorites`
**认证**: 需要Token

**请求参数**:
```json
{
  "type": 1,
  "targetId": 1
}
```

### 2.7 取消收藏
**接口**: `DELETE /user/favorites/{id}`
**认证**: 需要Token

---

## 🏪 3. 站点模块 (Stations)

### 3.1 获取附近站点
**接口**: `GET /stations/nearby`

**查询参数**:
- `lng`: 经度 (必填)
- `lat`: 纬度 (必填)
- `radius`: 搜索半径(km) (默认5)
- `category`: 分类ID (可选)
- `status`: 站点状态 (可选)
- `page`: 页码 (默认1)
- `limit`: 每页数量 (默认20)

**响应数据**:
```json
{
  "code": 200,
  "message": "获取附近站点成功",
  "data": [
    {
      "id": 1,
      "name": "绿色回收站",
      "phone": "010-12345678",
      "address": "朝阳区建国门外大街100号",
      "lng": 116.407400,
      "lat": 39.904200,
      "city": "北京市",
      "hours": "08:00-18:00",
      "image": "https://img.com/station1.jpg",
      "rating": 4.8,
      "reviewCount": 156,
      "status": 1,
      "distance": 0.5,
      "services": [
        {
          "categoryName": "电脑配件",
          "priceRange": "50-1500元/台"
        }
      ]
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 10,
    "pages": 1
  }
}
```

### 3.2 搜索站点
**接口**: `GET /stations/search`

**查询参数**:
- `keyword`: 搜索关键词 (必填)
- `city`: 城市 (可选)
- `category`: 分类ID (可选)
- `page`: 页码 (默认1)
- `limit`: 每页数量 (默认20)

### 3.3 获取站点详情
**接口**: `GET /stations/{id}`

**响应数据**:
```json
{
  "code": 200,
  "message": "获取站点详情成功",
  "data": {
    "id": 1,
    "name": "绿色回收站",
    "phone": "010-12345678",
    "address": "朝阳区建国门外大街100号",
    "lng": 116.407400,
    "lat": 39.904200,
    "city": "北京市",
    "hours": "08:00-18:00",
    "image": "https://img.com/station1.jpg",
    "rating": 4.8,
    "reviewCount": 156,
    "status": 1,
    "services": [
      {
        "categoryId": 5,
        "categoryName": "电脑配件",
        "categoryIcon": "💻",
        "minPrice": 50.00,
        "maxPrice": 1500.00,
        "unit": "台"
      }
    ],
    "reviews": [
      {
        "id": 1,
        "rating": 5,
        "content": "服务很好，价格公道！",
        "images": ["https://img.com/review1.jpg"],
        "createdAt": "2024-01-15T10:30:00.000Z",
        "user": {
          "nickname": "环保达人",
          "avatar": "https://img.com/avatar.jpg"
        }
      }
    ],
    "isFavorited": false,
    "favoriteCount": 25
  }
}
```

### 3.4 提交站点评价
**接口**: `POST /stations/{id}/reviews`
**认证**: 需要Token

**请求参数**:
```json
{
  "rating": 5,
  "content": "服务很好，价格公道！",
  "images": ["https://img.com/review1.jpg"]
}
```

### 3.5 获取站点评价
**接口**: `GET /stations/{id}/reviews`

**查询参数**:
- `page`: 页码 (默认1)
- `limit`: 每页数量 (默认20)
- `rating`: 评分筛选 (可选)

---

## 💰 4. 产品模块 (Products)

### 4.1 获取产品分类
**接口**: `GET /categories`

**响应数据**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": 1,
      "name": "电子产品",
      "icon": "💻",
      "description": "电脑、手机、数码产品等",
      "children": [
        {
          "id": 5,
          "name": "电脑配件",
          "icon": "💻",
          "description": "CPU、显卡、主板等"
        }
      ]
    }
  ]
}
```

### 4.2 搜索产品
**接口**: `GET /products/search`

**查询参数**:
- `keyword`: 搜索关键词 (必填)
- `category`: 分类ID (可选)
- `page`: 页码 (默认1)
- `limit`: 每页数量 (默认20)

**响应数据**:
```json
{
  "code": 200,
  "message": "搜索成功",
  "data": [
    {
      "id": 4,
      "name": "iPhone手机",
      "brand": "Apple",
      "model": "iPhone 13 Pro",
      "image": "https://img.com/iphone.jpg",
      "categoryName": "手机数码",
      "priceRange": "3000-4500元/台",
      "avgPrice": 3750.00
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 10,
    "pages": 1
  }
}
```

### 4.3 获取产品详情
**接口**: `GET /products/{id}`

### 4.4 获取产品价格
**接口**: `GET /products/{id}/prices`

**查询参数**:
- `stationId`: 站点ID (可选)
- `days`: 天数 (默认7天)

**响应数据**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "productInfo": {
      "id": 4,
      "name": "iPhone手机",
      "brand": "Apple",
      "model": "iPhone 13 Pro"
    },
    "currentPrice": {
      "minPrice": 3000.00,
      "maxPrice": 4500.00,
      "avgPrice": 3750.00,
      "unit": "台",
      "updateTime": "2024-01-15T10:00:00.000Z"
    },
    "priceHistory": [
      {
        "date": "2024-01-15",
        "avgPrice": 3750.00
      }
    ],
    "stationPrices": [
      {
        "stationId": 1,
        "stationName": "绿色回收站",
        "minPrice": 3000.00,
        "maxPrice": 4500.00,
        "avgPrice": 3750.00
      }
    ]
  }
}
```

---

## 📈 5. 价格行情模块 (Market)

### 5.1 获取价格行情
**接口**: `GET /market/trends`

**查询参数**:
- `category`: 分类ID (可选)
- `city`: 城市 (可选)
- `days`: 天数 (默认30)

**响应数据**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "hotProducts": [
      {
        "productId": 4,
        "productName": "iPhone手机",
        "priceChange": 5.2,
        "changeType": "up",
        "currentPrice": 3750.00
      }
    ],
    "categoryTrends": [
      {
        "categoryId": 6,
        "categoryName": "手机数码",
        "avgPrice": 2500.00,
        "priceChange": 3.5,
        "changeType": "up"
      }
    ]
  }
}
```

---

## 🔍 6. 搜索模块 (Search)

### 6.1 综合搜索
**接口**: `GET /search`

**查询参数**:
- `keyword`: 搜索关键词 (必填)
- `type`: 搜索类型 (all/product/station)
- `city`: 城市 (可选)
- `page`: 页码 (默认1)
- `limit`: 每页数量 (默认20)

### 6.2 获取搜索建议
**接口**: `GET /search/suggestions`

**查询参数**:
- `keyword`: 关键词前缀 (必填)
- `limit`: 建议数量 (默认10)

### 6.3 获取热门搜索
**接口**: `GET /search/hot-keywords`

**响应数据**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    "iPhone",
    "笔记本电脑",
    "废铜价格",
    "显卡回收",
    "冰箱"
  ]
}
```

---

## 🛠️ 7. 工具模块 (Tools)

### 7.1 地址解析
**接口**: `GET /tools/geocoding`

**查询参数**:
- `address`: 地址 (必填)

### 7.2 距离计算
**接口**: `GET /tools/distance`

**查询参数**:
- `fromLng`: 起点经度
- `fromLat`: 起点纬度
- `toLng`: 终点经度
- `toLat`: 终点纬度

---

## 📊 8. 统计模块 (Statistics)

### 8.1 获取用户统计
**接口**: `GET /user/statistics`
**认证**: 需要Token

**查询参数**:
- `period`: 统计周期 (week/month/year)

---

## 🔧 通用说明

### 认证方式
```
Authorization: Bearer {JWT_TOKEN}
```

### 分页参数
- `page`: 页码，从1开始
- `limit`: 每页数量，最大100

### 错误响应格式
```json
{
  "code": 400,
  "message": "参数验证失败",
  "data": [
    {
      "field": "phone",
      "message": "手机号格式不正确",
      "value": "123456"
    }
  ],
  "timestamp": 1642234567890
}
```

### 限流规则
- 每个IP每15分钟最多100个请求
- 登录接口每个IP每小时最多10次
- 搜索接口每个用户每分钟最多30次

---

**📝 此API文档为慢慢回收小程序的完整接口规范，支持前端开发和第三方集成。**
