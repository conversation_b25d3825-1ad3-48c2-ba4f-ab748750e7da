.radioRankingItem {
    width: 48%;
    display: flex;
    border-bottom:1px solid #999 ;
    align-items: center;
    padding: 20px 0;
}
.radioRankingItem .radioRankingImg {
    width: 120px;
    height: 120px;
}
.radioRankingItem .radioRankingInfo {
    margin-left: 20px;
}

.radioRankingItem .radioRankingInfo .radioRankingItemTitle {
    font-size: 18px;
    cursor: pointer;
}
.radioRankingItem .radioRankingInfo .radioRankingItemTitle:hover {
    text-decoration: underline;
}
.radioRankingItem .radioRankingInfo .radioRankingItemNickName {
    margin-top: 15px;
    font-size: 12px;
    color: #000;
    cursor: pointer;
}
.radioRankingItem .radioRankingInfo .radioRankingItemNickName:hover {
    text-decoration: underline;
}

.radioRankingItem .radioRankingInfo .radioRankingItemNickName .radioRankingItemIcon {
    display: inline-block;
    position: relative;
    top: 2px;
    width: 14px;
    height: 15px;
    margin-right: 2px;
    background-image: url(../../static/images/sprite_icon2.png);
    background-position: -50px -300px;
}


.radioRankingItem .radioRankingInfo .radioRankingCount {
    color: #666;
    margin-top: 5px;
    font-size: 12px;
}
.radioRankingItem .radioRankingInfo .radioRankingCount .radioRankingSubscribe {
    margin-left: 10px;
}