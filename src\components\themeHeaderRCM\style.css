.themeHeaderBox{
  display: flex;
  justify-content: space-between;
  height: 33px;
  padding: 0 10px 4px 34px;
  border-bottom: 2px solid #c10d0c;
  background: url(../../static/images/sprite_02.png) no-repeat center;
  background-position: -225px -156px;
  line-height: 33px;
}

.themeHeaderBoxnotShow {
  padding-left: 2px;
  background: none;
}

.themeHeaderLeft {
  display: flex;
}
.themeHeaderLeft .themeHeaderTitle {
  display: flex;
  font-size: 20px;
  margin-bottom: 5px;
}
.themeHeaderLeft .themeHeaderTitle em {
  color: #333333;
}
.themeHeaderLeft .themeHeaderTitle em:hover {
  cursor: pointer;
}

.themeHeaderLeft .themeHeaderKeywords {
  display: flex;
  margin-left: 20px;
  color: #ccc;
  line-height: 35px;
}
.themeHeaderLeft .themeHeaderKeywords .themeHeaderKeywordsItem em{
  color: #666;
}
.themeHeaderLeft .themeHeaderKeywords .themeHeaderKeywordsItem em:hover{
  cursor: pointer;
}

.themeHeaderLeft .themeHeaderKeywords .themeHeaderKeywordsItem .themeHeaderKeywordsLine {
  margin: 0 15px;
}
.themeHeaderLeft .themeHeaderKeywords .themeHeaderKeywordsItem:last-child .themeHeaderKeywordsLine{
  display: none;
}

.themeHeaderRight span {
  cursor: pointer;
}
.themeHeaderRight .themeHeaderIcon {
  display: inline-block;
  width: 12px;
  height: 12px;
  margin-left: 4px;
  vertical-align: baseline;
  background: transparent url(../../static/images/sprite_02.png) no-repeat center;
  background-position: 0 -240px;
}

