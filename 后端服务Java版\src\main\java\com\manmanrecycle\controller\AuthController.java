package com.manmanrecycle.controller;

import com.manmanrecycle.common.result.Result;
import com.manmanrecycle.dto.RefreshTokenDTO;
import com.manmanrecycle.dto.WechatLoginDTO;
import com.manmanrecycle.service.AuthService;
import com.manmanrecycle.vo.LoginVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

/**
 * 认证控制器
 * 
 * <AUTHOR> Team
 * @since 2024-01-15
 */
@Slf4j
@RestController
@RequestMapping("/auth")
@RequiredArgsConstructor
@Api(tags = "认证模块")
public class AuthController {

    private final AuthService authService;

    /**
     * 微信小程序登录
     */
    @PostMapping("/wechat-login")
    @ApiOperation("微信小程序登录")
    public Result<LoginVO> wechatLogin(@Valid @RequestBody WechatLoginDTO loginDTO) {
        log.info("微信登录请求，code: {}", loginDTO.getCode());
        
        LoginVO loginVO = authService.wechatLogin(loginDTO);
        
        return Result.success("登录成功", loginVO);
    }

    /**
     * 刷新Token
     */
    @PostMapping("/refresh-token")
    @ApiOperation("刷新访问令牌")
    public Result<LoginVO> refreshToken(@Valid @RequestBody RefreshTokenDTO refreshTokenDTO) {
        log.info("刷新Token请求");
        
        LoginVO loginVO = authService.refreshToken(refreshTokenDTO.getRefreshToken());
        
        return Result.success("Token刷新成功", loginVO);
    }

    /**
     * 用户登出
     */
    @PostMapping("/logout")
    @ApiOperation("用户登出")
    public Result<Void> logout(HttpServletRequest request) {
        Long userId = (Long) request.getAttribute("userId");
        log.info("用户登出请求，userId: {}", userId);
        
        authService.logout(userId);
        
        return Result.success("登出成功");
    }

    /**
     * 验证Token
     */
    @GetMapping("/verify")
    @ApiOperation("验证Token有效性")
    public Result<Object> verifyToken(HttpServletRequest request) {
        Long userId = (Long) request.getAttribute("userId");
        
        return Result.success("Token验证成功", new Object() {
            public final Long userId = (Long) request.getAttribute("userId");
            public final boolean valid = true;
        });
    }
}
