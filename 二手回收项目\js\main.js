// 全局变量
let currentPage = 'home';
let isAnimating = false;

// 页面切换功能
function showPage(pageId) {
    if (isAnimating) return;
    
    isAnimating = true;
    
    // 隐藏所有页面
    const pages = document.querySelectorAll('.page');
    const currentPageElement = document.querySelector('.page.active');
    
    if (currentPageElement) {
        currentPageElement.style.opacity = '0';
        currentPageElement.style.transform = 'translateX(-50px)';
        
        setTimeout(() => {
            currentPageElement.classList.remove('active');
            
            // 显示目标页面
            const targetPage = document.getElementById(pageId);
            if (targetPage) {
                targetPage.classList.add('active');
                targetPage.style.opacity = '0';
                targetPage.style.transform = 'translateX(50px)';
                
                setTimeout(() => {
                    targetPage.style.opacity = '1';
                    targetPage.style.transform = 'translateX(0)';
                    isAnimating = false;
                }, 50);
            }
        }, 300);
    } else {
        // 首次加载
        const targetPage = document.getElementById(pageId);
        if (targetPage) {
            targetPage.classList.add('active');
            isAnimating = false;
        }
    }
    
    // 更新导航按钮状态
    updateNavButtons(pageId);
    updateBottomNav(pageId);
    
    // 更新当前页面
    currentPage = pageId;
    
    // 显示页面切换提示
    showToast(`切换到${getPageName(pageId)}`);
}

// 更新顶部导航按钮状态
function updateNavButtons(activePageId) {
    const navBtns = document.querySelectorAll('.nav-btn');
    navBtns.forEach(btn => {
        btn.classList.remove('active');
        if (btn.getAttribute('data-page') === activePageId) {
            btn.classList.add('active');
        }
    });
}

// 更新底部导航状态
function updateBottomNav(activePageId) {
    const bottomNavs = document.querySelectorAll('.bottom-nav');
    bottomNavs.forEach(nav => {
        const navItems = nav.querySelectorAll('.nav-item');
        navItems.forEach(item => {
            item.classList.remove('active');
        });
        
        // 根据页面ID激活对应的导航项
        const pageIndex = ['home', 'category', 'market', 'profile'].indexOf(activePageId);
        if (pageIndex !== -1 && navItems[pageIndex]) {
            navItems[pageIndex].classList.add('active');
        }
    });
}

// 获取页面名称
function getPageName(pageId) {
    const pageNames = {
        'home': '首页地图',
        'category': '分类查询',
        'ai-route': 'AI路线规划',
        'market': '价格行情',
        'profile': '个人中心'
    };
    return pageNames[pageId] || '未知页面';
}

// 显示提示消息
function showToast(message, type = 'info') {
    // 移除已存在的toast
    const existingToast = document.querySelector('.toast');
    if (existingToast) {
        existingToast.remove();
    }
    
    // 创建新的toast
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.textContent = message;
    
    // 设置样式
    Object.assign(toast.style, {
        position: 'fixed',
        top: '20px',
        left: '50%',
        transform: 'translateX(-50%)',
        background: type === 'success' ? '#52C41A' : 
                   type === 'error' ? '#ff4d4f' : 
                   type === 'warning' ? '#faad14' : '#1890ff',
        color: 'white',
        padding: '12px 24px',
        borderRadius: '25px',
        fontSize: '14px',
        fontWeight: '500',
        zIndex: '10000',
        boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
        animation: 'toastSlideIn 0.3s ease-out'
    });
    
    // 添加动画样式
    if (!document.querySelector('#toast-styles')) {
        const style = document.createElement('style');
        style.id = 'toast-styles';
        style.textContent = `
            @keyframes toastSlideIn {
                from {
                    opacity: 0;
                    transform: translateX(-50%) translateY(-20px);
                }
                to {
                    opacity: 1;
                    transform: translateX(-50%) translateY(0);
                }
            }
            @keyframes toastSlideOut {
                from {
                    opacity: 1;
                    transform: translateX(-50%) translateY(0);
                }
                to {
                    opacity: 0;
                    transform: translateX(-50%) translateY(-20px);
                }
            }
        `;
        document.head.appendChild(style);
    }
    
    // 添加到页面
    document.body.appendChild(toast);
    
    // 3秒后移除
    setTimeout(() => {
        toast.style.animation = 'toastSlideOut 0.3s ease-in';
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }, 3000);
}

// AI路线规划相关功能
function toggleSetting(element) {
    // 移除同级元素的active状态
    const siblings = element.parentNode.querySelectorAll('.setting-card');
    siblings.forEach(sibling => {
        if (sibling !== element) {
            sibling.classList.remove('active');
        }
    });
    
    // 切换当前元素状态
    element.classList.toggle('active');
    
    const settingText = element.querySelector('.setting-text').textContent;
    showToast(`已选择: ${settingText}`, 'success');
}

function editRoute() {
    showToast('打开路线编辑器...', 'info');
}

function replanRoute() {
    showToast('AI正在重新规划路线...', 'info');
    
    // 模拟重新规划过程
    setTimeout(() => {
        showToast('路线规划完成！', 'success');
    }, 2000);
}

function startNavigation() {
    showToast('开始导航，祝您回收愉快！', 'success');
}

// 站点相关功能
function showStationDetail(stationName) {
    showToast(`查看${stationName}详情`, 'info');
}

function showStepDetail(stepName) {
    showToast(`查看${stepName}路线详情`, 'info');
}

// 分类相关功能
function showCategoryDetail(categoryName) {
    showToast(`查看${categoryName}详情`, 'info');
}

// 行情相关功能
function changeLocation() {
    showToast('切换地区功能', 'info');
}

function showTrendDetail(productName) {
    showToast(`查看${productName}价格趋势`, 'info');
}

function showPriceDetail(itemName) {
    showToast(`查看${itemName}详细行情`, 'info');
}

// 个人中心相关功能
function openSettings() {
    showToast('打开设置页面', 'info');
}

function openMessages() {
    showToast('打开消息中心', 'info');
}

function openMyFavorites() {
    showToast('查看我的收藏', 'info');
}

function openRecycleHistory() {
    showToast('查看回收记录', 'info');
}

function openSellGoods() {
    showToast('发布回收需求', 'info');
}

function openDataStats() {
    showToast('查看数据统计', 'info');
}

function openInviteFriends() {
    showToast('邀请好友功能', 'info');
}

function openRecycleGuide() {
    showToast('打开回收指南', 'info');
}

function openCalculator() {
    showToast('打开价值计算器', 'info');
}

function openCustomerService() {
    showToast('连接在线客服', 'info');
}

function openFAQ() {
    showToast('查看常见问题', 'info');
}

function openAbout() {
    showToast('关于慢慢回收', 'info');
}

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    // 初始化页面切换动画
    initPageTransitions();
    
    // 初始化卡片交互效果
    initCardInteractions();
    
    // 初始化搜索框效果
    initSearchEffects();
    
    // 初始化地图标记动画
    initMapMarkers();
    
    // 初始化键盘导航
    initKeyboardNavigation();
    
    // 初始化触摸手势
    initTouchGestures();
    
    // 显示欢迎消息
    setTimeout(() => {
        showToast('欢迎使用慢慢回收！', 'success');
    }, 1000);
});

// 初始化页面切换动画
function initPageTransitions() {
    const pages = document.querySelectorAll('.page');
    pages.forEach(page => {
        page.style.transition = 'all 0.3s ease';
    });
}

// 初始化卡片交互效果
function initCardInteractions() {
    const cards = document.querySelectorAll('.action-card, .category-card, .station-card, .setting-card, .trend-card, .price-item, .service-item, .tool-item');
    
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = this.style.transform.replace('scale(1)', '') + ' scale(1.02)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = this.style.transform.replace('scale(1.02)', '');
        });
        
        card.addEventListener('click', function() {
            // 点击动画
            this.style.transform = this.style.transform + ' scale(0.98)';
            setTimeout(() => {
                this.style.transform = this.style.transform.replace('scale(0.98)', '');
            }, 150);
        });
    });
}

// 初始化搜索框效果
function initSearchEffects() {
    const searchInputs = document.querySelectorAll('.search-input');
    
    searchInputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.style.transform = 'scale(1.02)';
        });
        
        input.addEventListener('blur', function() {
            this.parentElement.style.transform = '';
        });
        
        input.addEventListener('input', function() {
            if (this.value.length > 0) {
                showToast(`搜索: ${this.value}`, 'info');
            }
        });
    });
}

// 初始化地图标记动画
function initMapMarkers() {
    const markers = document.querySelectorAll('.map-marker');
    
    markers.forEach((marker, index) => {
        marker.addEventListener('click', function() {
            showToast(`回收站点 ${index + 1}`, 'info');
            
            // 添加点击动画
            this.style.transform = 'scale(1.5)';
            setTimeout(() => {
                this.style.transform = '';
            }, 300);
        });
        
        // 随机延迟动画
        marker.style.animationDelay = `${index * 0.5}s`;
    });
}

// 初始化键盘导航
function initKeyboardNavigation() {
    document.addEventListener('keydown', function(event) {
        const pages = ['home', 'category', 'ai-route', 'market', 'profile'];
        const currentIndex = pages.indexOf(currentPage);
        
        if (event.key === 'ArrowLeft' && currentIndex > 0) {
            showPage(pages[currentIndex - 1]);
        } else if (event.key === 'ArrowRight' && currentIndex < pages.length - 1) {
            showPage(pages[currentIndex + 1]);
        } else if (event.key === 'Escape') {
            if (currentPage !== 'home') {
                showPage('home');
            }
        }
    });
}

// 初始化触摸手势
function initTouchGestures() {
    let touchStartX = 0;
    let touchEndX = 0;
    
    document.addEventListener('touchstart', function(event) {
        touchStartX = event.changedTouches[0].screenX;
    });
    
    document.addEventListener('touchend', function(event) {
        touchEndX = event.changedTouches[0].screenX;
        handleSwipe();
    });
    
    function handleSwipe() {
        const swipeThreshold = 50;
        const diff = touchStartX - touchEndX;
        
        if (Math.abs(diff) > swipeThreshold) {
            const pages = ['home', 'category', 'ai-route', 'market', 'profile'];
            const currentIndex = pages.indexOf(currentPage);
            
            if (diff > 0 && currentIndex < pages.length - 1) {
                // 向左滑动，显示下一页
                showPage(pages[currentIndex + 1]);
            } else if (diff < 0 && currentIndex > 0) {
                // 向右滑动，显示上一页
                showPage(pages[currentIndex - 1]);
            }
        }
    }
}

// 模拟数据加载动画
function simulateDataLoading() {
    const loadingElements = document.querySelectorAll('.station-card, .category-card, .trend-card');
    
    loadingElements.forEach((element, index) => {
        element.style.opacity = '0';
        element.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            element.style.transition = 'all 0.5s ease';
            element.style.opacity = '1';
            element.style.transform = 'translateY(0)';
        }, index * 100);
    });
}

// 页面加载完成后执行
window.addEventListener('load', function() {
    // 执行数据加载动画
    setTimeout(() => {
        simulateDataLoading();
    }, 500);
    
    // 添加页面加载动画
    const phoneContainer = document.querySelector('.phone-mockup');
    if (phoneContainer) {
        phoneContainer.style.transform = 'scale(0.8) translateY(50px)';
        phoneContainer.style.opacity = '0';
        
        setTimeout(() => {
            phoneContainer.style.transition = 'all 1s ease';
            phoneContainer.style.transform = 'scale(0.9) translateY(0)';
            phoneContainer.style.opacity = '1';
        }, 200);
    }
});

// 统计数字动画
function animateNumbers() {
    const statNumbers = document.querySelectorAll('.stat-number');
    
    statNumbers.forEach(stat => {
        const finalValue = stat.textContent;
        const numericValue = parseInt(finalValue);
        
        if (!isNaN(numericValue)) {
            let currentValue = 0;
            const increment = numericValue / 50;
            const timer = setInterval(() => {
                currentValue += increment;
                if (currentValue >= numericValue) {
                    stat.textContent = finalValue;
                    clearInterval(timer);
                } else {
                    stat.textContent = Math.floor(currentValue);
                }
            }, 30);
        }
    });
}

// 在页面加载时执行数字动画
setTimeout(() => {
    animateNumbers();
}, 1500);
