package com.c2brecycle.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.c2brecycle.entity.Category;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 分类Mapper接口
 * 
 * <AUTHOR> Recycle Team
 * @since 2024-01-15
 */
@Mapper
public interface CategoryMapper extends BaseMapper<Category> {

    /**
     * 获取所有启用的分类
     * 
     * @return 分类列表
     */
    @Select("SELECT * FROM categories WHERE status = 1 AND deleted = 0 ORDER BY sort ASC, id ASC")
    List<Category> selectAllEnabled();
}
