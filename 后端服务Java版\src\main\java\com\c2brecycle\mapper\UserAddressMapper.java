package com.c2brecycle.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.c2brecycle.entity.UserAddress;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

/**
 * 用户地址Mapper接口
 * 
 * <AUTHOR> Recycle Team
 * @since 2024-01-15
 */
@Mapper
public interface UserAddressMapper extends BaseMapper<UserAddress> {

    /**
     * 清除用户的默认地址标记
     * 
     * @param userId 用户ID
     * @return 影响行数
     */
    @Update("UPDATE user_addresses SET is_default = 0 WHERE user_id = #{userId} AND deleted = 0")
    int clearDefaultAddress(@Param("userId") Long userId);
}
