{"info": {"name": "慢慢回收API接口测试", "description": "慢慢回收小程序后端API接口的完整测试集合", "version": "1.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://localhost:3000/api", "type": "string"}, {"key": "token", "value": "", "type": "string"}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "item": [{"name": "🔐 认证模块", "item": [{"name": "微信登录", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.data && response.data.token) {", "        pm.collectionVariables.set('token', response.data.token);", "        console.log('Token已保存:', response.data.token);", "    }", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"code\": \"test_code_123\",\n  \"userInfo\": {\n    \"nickname\": \"测试用户\",\n    \"avatar\": \"https://img.com/avatar.jpg\",\n    \"gender\": 1,\n    \"city\": \"北京市\",\n    \"province\": \"北京市\",\n    \"country\": \"中国\"\n  }\n}"}, "url": {"raw": "{{baseUrl}}/auth/wechat-login", "host": ["{{baseUrl}}"], "path": ["auth", "wechat-login"]}, "description": "微信小程序登录接口测试"}}, {"name": "刷新Token", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"refreshToken\": \"your_refresh_token_here\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/refresh-token", "host": ["{{baseUrl}}"], "path": ["auth", "refresh-token"]}}}, {"name": "验证Token", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/auth/verify", "host": ["{{baseUrl}}"], "path": ["auth", "verify"]}}}, {"name": "用户登出", "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/auth/logout", "host": ["{{baseUrl}}"], "path": ["auth", "logout"]}}}]}, {"name": "👤 用户模块", "item": [{"name": "获取用户信息", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/user/profile", "host": ["{{baseUrl}}"], "path": ["user", "profile"]}}}, {"name": "更新用户信息", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"nickname\": \"新昵称\",\n  \"phone\": \"13800138001\",\n  \"city\": \"上海市\"\n}"}, "url": {"raw": "{{baseUrl}}/user/profile", "host": ["{{baseUrl}}"], "path": ["user", "profile"]}}}, {"name": "获取用户地址", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/user/addresses", "host": ["{{baseUrl}}"], "path": ["user", "addresses"]}}}, {"name": "添加用户地址", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"张三\",\n  \"phone\": \"13800138001\",\n  \"address\": \"朝阳区建国门外大街1号\",\n  \"lng\": 116.407400,\n  \"lat\": 39.904200,\n  \"isDefault\": true\n}"}, "url": {"raw": "{{baseUrl}}/user/addresses", "host": ["{{baseUrl}}"], "path": ["user", "addresses"]}}}, {"name": "获取用户收藏", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/user/favorites?type=1&page=1&limit=20", "host": ["{{baseUrl}}"], "path": ["user", "favorites"], "query": [{"key": "type", "value": "1", "description": "收藏类型: 1-站点 2-产品"}, {"key": "page", "value": "1"}, {"key": "limit", "value": "20"}]}}}, {"name": "添加收藏", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"type\": 1,\n  \"targetId\": 1\n}"}, "url": {"raw": "{{baseUrl}}/user/favorites", "host": ["{{baseUrl}}"], "path": ["user", "favorites"]}}}]}, {"name": "🏪 站点模块", "item": [{"name": "获取附近站点", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/stations/nearby?lng=116.4074&lat=39.9042&radius=5&page=1&limit=20", "host": ["{{baseUrl}}"], "path": ["stations", "nearby"], "query": [{"key": "lng", "value": "116.4074", "description": "经度"}, {"key": "lat", "value": "39.9042", "description": "纬度"}, {"key": "radius", "value": "5", "description": "搜索半径(km)"}, {"key": "page", "value": "1"}, {"key": "limit", "value": "20"}]}}}, {"name": "搜索站点", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/stations/search?keyword=绿色回收&city=北京市&page=1&limit=20", "host": ["{{baseUrl}}"], "path": ["stations", "search"], "query": [{"key": "keyword", "value": "绿色回收", "description": "搜索关键词"}, {"key": "city", "value": "北京市", "description": "城市筛选"}, {"key": "page", "value": "1"}, {"key": "limit", "value": "20"}]}}}, {"name": "获取站点详情", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/stations/1", "host": ["{{baseUrl}}"], "path": ["stations", "1"]}}}, {"name": "提交站点评价", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"rating\": 5,\n  \"content\": \"服务很好，价格公道！\",\n  \"images\": [\"https://img.com/review1.jpg\"]\n}"}, "url": {"raw": "{{baseUrl}}/stations/1/reviews", "host": ["{{baseUrl}}"], "path": ["stations", "1", "reviews"]}}}, {"name": "获取站点评价", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/stations/1/reviews?page=1&limit=20", "host": ["{{baseUrl}}"], "path": ["stations", "1", "reviews"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "20"}]}}}]}, {"name": "💰 产品模块", "item": [{"name": "获取产品分类", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/categories", "host": ["{{baseUrl}}"], "path": ["categories"]}}}, {"name": "搜索产品", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/products/search?keyword=iPhone&category=6&page=1&limit=20", "host": ["{{baseUrl}}"], "path": ["products", "search"], "query": [{"key": "keyword", "value": "iPhone", "description": "搜索关键词"}, {"key": "category", "value": "6", "description": "分类ID"}, {"key": "page", "value": "1"}, {"key": "limit", "value": "20"}]}}}, {"name": "获取产品详情", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/products/4", "host": ["{{baseUrl}}"], "path": ["products", "4"]}}}, {"name": "获取产品价格", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/products/4/prices?stationId=1&days=7", "host": ["{{baseUrl}}"], "path": ["products", "4", "prices"], "query": [{"key": "stationId", "value": "1", "description": "站点ID(可选)"}, {"key": "days", "value": "7", "description": "天数"}]}}}]}, {"name": "📈 价格行情模块", "item": [{"name": "获取价格行情", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/market/trends?category=6&city=北京市&days=30", "host": ["{{baseUrl}}"], "path": ["market", "trends"], "query": [{"key": "category", "value": "6", "description": "分类ID"}, {"key": "city", "value": "北京市", "description": "城市"}, {"key": "days", "value": "30", "description": "天数"}]}}}]}, {"name": "🔍 搜索模块", "item": [{"name": "综合搜索", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/search?keyword=iPhone&type=all&city=北京市&page=1&limit=20", "host": ["{{baseUrl}}"], "path": ["search"], "query": [{"key": "keyword", "value": "iPhone", "description": "搜索关键词"}, {"key": "type", "value": "all", "description": "搜索类型: all/product/station"}, {"key": "city", "value": "北京市", "description": "城市"}, {"key": "page", "value": "1"}, {"key": "limit", "value": "20"}]}}}, {"name": "获取搜索建议", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/search/suggestions?keyword=iP&limit=10", "host": ["{{baseUrl}}"], "path": ["search", "suggestions"], "query": [{"key": "keyword", "value": "iP", "description": "关键词前缀"}, {"key": "limit", "value": "10", "description": "建议数量"}]}}}, {"name": "获取热门搜索", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/search/hot-keywords", "host": ["{{baseUrl}}"], "path": ["search", "hot-keywords"]}}}]}, {"name": "🛠️ 工具模块", "item": [{"name": "地址解析", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/tools/geocoding?address=北京市朝阳区建国门外大街100号", "host": ["{{baseUrl}}"], "path": ["tools", "geocoding"], "query": [{"key": "address", "value": "北京市朝阳区建国门外大街100号", "description": "地址"}]}}}, {"name": "距离计算", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/tools/distance?fromLng=116.4074&fromLat=39.9042&toLng=116.3112&toLat=39.9991", "host": ["{{baseUrl}}"], "path": ["tools", "distance"], "query": [{"key": "fromLng", "value": "116.4074", "description": "起点经度"}, {"key": "fromLat", "value": "39.9042", "description": "起点纬度"}, {"key": "toLng", "value": "116.3112", "description": "终点经度"}, {"key": "toLat", "value": "39.9991", "description": "终点纬度"}]}}}]}, {"name": "🔧 系统接口", "item": [{"name": "健康检查", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3000/health", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["health"]}}}]}]}