.artistCategoryItemBox {
    width: 160px;
    color: #333;
    height: 29px;
    line-height: 29px;
    margin-bottom: 2px;
    cursor: pointer;
}

.artistCategoryItemBox span {
    display: inline-block;
    width: 160px;
    padding-left: 27px;
    background: url(../../../../static/images/singer_sprite.png) no-repeat 0 -30px;
    cursor: pointer;
}

.artistCategoryItemBox span:hover {
    text-decoration: underline;
}

.artistCategoryItemBox1 .active span {
    color: red;
    background-position: 0 0;
}

.artistCategoryBox {
    width: 180px;
    padding: 50px 10px 40px;
    border: 1px solid #d3d3d3;
    border-width: 0 1px;
    background-color: #fafafa;
}

.artistCategoryBox  .section {
    border-bottom: 1px solid #d3d3d3;
    padding: 10px 0;
}
.artistCategoryBox:last-of-type {
    border-bottom: none;
}
.artistCategoryBox  .section .title {
    height: 25px;
    padding-left: 14px;
    font-size: 16px;
    margin-bottom: 5px;
    font-family: "Microsoft Yahei";
}



