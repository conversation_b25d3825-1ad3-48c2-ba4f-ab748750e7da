package com.c2crestore;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * C2C Restore应用启动类
 * 
 * <AUTHOR> Restore Team
 * @since 2024-01-15
 */
@SpringBootApplication
@MapperScan("com.c2crestore.mapper")
@EnableTransactionManagement
@EnableCaching
@EnableAsync
public class C2cRestoreApplication {

    public static void main(String[] args) {
        SpringApplication.run(C2cRestoreApplication.class, args);
        System.out.println("\n" +
                "   ____ ____   ____   ____           _                  \n" +
                "  / ___|___ \\ / ___| |  _ \\ ___  ___| |_ ___  _ __ ___  \n" +
                " | |     __) | |     | |_) / _ \\/ __| __/ _ \\| '__/ _ \\ \n" +
                " | |___ / __/| |___  |  _ <  __/\\__ \\ || (_) | | |  __/ \n" +
                "  \\____|_____|\\____| |_| \\_\\___||___/\\__\\___/|_|  \\___| \n" +
                "                                                       \n" +
                "\n🔄 C2C Restore API服务启动成功！\n" +
                "📡 接口文档: http://localhost:8080/api/swagger-ui/\n" +
                "📊 监控面板: http://localhost:8080/api/druid/\n" +
                "🔍 健康检查: http://localhost:8080/api/actuator/health\n");
    }
}
