import * as productApi from '../../api/product';

// Action Types
export const FETCH_CATEGORIES_REQUEST = 'FETCH_CATEGORIES_REQUEST';
export const FETCH_CATEGORIES_SUCCESS = 'FETCH_CATEGORIES_SUCCESS';
export const FETCH_CATEGORIES_FAILURE = 'FETCH_CATEGORIES_FAILURE';

export const SEARCH_PRODUCTS_REQUEST = 'SEARCH_PRODUCTS_REQUEST';
export const SEARCH_PRODUCTS_SUCCESS = 'SEARCH_PRODUCTS_SUCCESS';
export const SEARCH_PRODUCTS_FAILURE = 'SEARCH_PRODUCTS_FAILURE';

export const FETCH_PRODUCT_DETAIL_REQUEST = 'FETCH_PRODUCT_DETAIL_REQUEST';
export const FETCH_PRODUCT_DETAIL_SUCCESS = 'FETCH_PRODUCT_DETAIL_SUCCESS';
export const FETCH_PRODUCT_DETAIL_FAILURE = 'FETCH_PRODUCT_DETAIL_FAILURE';

export const FETCH_HOT_PRODUCTS_REQUEST = 'FETCH_HOT_PRODUCTS_REQUEST';
export const FETCH_HOT_PRODUCTS_SUCCESS = 'FETCH_HOT_PRODUCTS_SUCCESS';
export const FETCH_HOT_PRODUCTS_FAILURE = 'FETCH_HOT_PRODUCTS_FAILURE';

export const FETCH_PRODUCT_PRICES_REQUEST = 'FETCH_PRODUCT_PRICES_REQUEST';
export const FETCH_PRODUCT_PRICES_SUCCESS = 'FETCH_PRODUCT_PRICES_SUCCESS';
export const FETCH_PRODUCT_PRICES_FAILURE = 'FETCH_PRODUCT_PRICES_FAILURE';

export const CLEAR_PRODUCT_DETAIL = 'CLEAR_PRODUCT_DETAIL';
export const CLEAR_SEARCH_RESULTS = 'CLEAR_SEARCH_RESULTS';

// Action Creators

// 获取产品分类
export const fetchCategories = () => {
  return async (dispatch) => {
    dispatch({ type: FETCH_CATEGORIES_REQUEST });
    
    try {
      const response = await productApi.getCategories();
      
      dispatch({
        type: FETCH_CATEGORIES_SUCCESS,
        payload: response
      });
      
      return response;
    } catch (error) {
      dispatch({
        type: FETCH_CATEGORIES_FAILURE,
        payload: error.message
      });
      throw error;
    }
  };
};

// 搜索产品
export const searchProducts = (searchParams) => {
  return async (dispatch) => {
    dispatch({ type: SEARCH_PRODUCTS_REQUEST });
    
    try {
      const response = await productApi.searchProducts(searchParams);
      
      dispatch({
        type: SEARCH_PRODUCTS_SUCCESS,
        payload: {
          products: response.records || response,
          total: response.total || 0,
          current: response.current || 1,
          size: response.size || 10,
          searchParams
        }
      });
      
      return response;
    } catch (error) {
      dispatch({
        type: SEARCH_PRODUCTS_FAILURE,
        payload: error.message
      });
      throw error;
    }
  };
};

// 获取产品详情
export const fetchProductDetail = (productId) => {
  return async (dispatch) => {
    dispatch({ type: FETCH_PRODUCT_DETAIL_REQUEST });
    
    try {
      const response = await productApi.getProductDetail(productId);
      
      dispatch({
        type: FETCH_PRODUCT_DETAIL_SUCCESS,
        payload: response
      });
      
      return response;
    } catch (error) {
      dispatch({
        type: FETCH_PRODUCT_DETAIL_FAILURE,
        payload: error.message
      });
      throw error;
    }
  };
};

// 获取热门产品
export const fetchHotProducts = (params = {}) => {
  return async (dispatch) => {
    dispatch({ type: FETCH_HOT_PRODUCTS_REQUEST });
    
    try {
      const response = await productApi.getHotProducts(params);
      
      dispatch({
        type: FETCH_HOT_PRODUCTS_SUCCESS,
        payload: response
      });
      
      return response;
    } catch (error) {
      dispatch({
        type: FETCH_HOT_PRODUCTS_FAILURE,
        payload: error.message
      });
      throw error;
    }
  };
};

// 获取产品价格信息
export const fetchProductPrices = (productId, params = {}) => {
  return async (dispatch) => {
    dispatch({ type: FETCH_PRODUCT_PRICES_REQUEST });
    
    try {
      const response = await productApi.getProductPrices(productId, params);
      
      dispatch({
        type: FETCH_PRODUCT_PRICES_SUCCESS,
        payload: response
      });
      
      return response;
    } catch (error) {
      dispatch({
        type: FETCH_PRODUCT_PRICES_FAILURE,
        payload: error.message
      });
      throw error;
    }
  };
};

// 清除产品详情
export const clearProductDetail = () => ({
  type: CLEAR_PRODUCT_DETAIL
});

// 清除搜索结果
export const clearSearchResults = () => ({
  type: CLEAR_SEARCH_RESULTS
});
