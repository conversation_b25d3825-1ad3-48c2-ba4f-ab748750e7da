import React, { memo } from 'react'
import propType from 'prop-types'
import './style.css'
/**
 * 登录ICON
 */
function LoginIcon(props: any) {
  const {position, description, onClick} = props
  return (   
    <a className='themeloginIconBox' onClick={onClick}>
      <i className="themeloginLogo" style={{ backgroundPosition: position}}></i>
      <em className="themeloginDescription">{description}</em>
    </a>
  )
}

LoginIcon.propType = {
  position: propType.string.isRequired,
  description: propType.string.isRequired,
  onClick: propType.func
}

LoginIcon.defaultProptype = {
  position: '-150px -670px',
  description: 'default'
}

export default memo(LoginIcon)