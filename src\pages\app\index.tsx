import React, { memo, Suspense, useEffect, useState } from 'react'
import { useDispatch } from 'react-redux'
import { Skeleton } from 'antd'
import ThemeDialog from '../../components/themeDialog/index'
import initLoginInfo from '../../config/token'
import { setLoginInfo, getLoginInfo } from '../../utils/loginUtils'
import { getLoginProfileInfo } from '../../components/themeLogin/store/actionCreators'
import { addPlaylistId, getCurrentSongIndex, getPlaylistId, initCurrentSongIndex } from '../../utils/localstorage'
import { SONG_PLAYLIST_ID as songplaylistId } from '../../common/constants'
import { getSongDetailArrayAction } from '../../components/playerBar/store'
import RouterConfig from '../../router'

export default memo(function APP() {
  const [isShow, setIsShow] = useState(false)
  const dispatch = useDispatch()

  const initLogin = () => {
    // 存在登录信息
    if (localStorage.getItem('loginInfo') != null) {
      const { username, password } = getLoginInfo('loginInfo')
      username && password
        ? dispatch(getLoginProfileInfo(username, password, true))
        : console.log('当前登录的默认信息')
    }
    // 不存在登录信息
    else {
      setLoginInfo('loginInfo', initLoginInfo)
    }
  }
  initLogin()

  // 添加默认歌曲ID(本地存储默认歌曲id)
  useEffect(() => {
    addPlaylistId(songplaylistId)
    initCurrentSongIndex()
  }, [])


  // 本地存储读取歌曲列表ID
  useEffect(() => {
      const index = getCurrentSongIndex()
      dispatch(getSongDetailArrayAction(getPlaylistId(), index))
  }, [dispatch])

  // other function
  const handleOK = () => {
    setIsShow(false)
  }

  const handleCancel = () => {
    setIsShow(false)
  }

  return (
    <>
      <Suspense fallback={<Skeleton active />}><RouterConfig /></Suspense>
      <ThemeDialog
        controlShow={isShow}
        title="上传音乐"
        handleOK={handleOK}
        handleCancel={handleCancel}
      >
      </ThemeDialog>
    </>
  )
})
