/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
    color: #333;
    line-height: 1.6;
}

/* 主容器 */
.demo-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* 项目头部 */
.project-header {
    background: linear-gradient(135deg, #52C41A, #73D13D);
    color: white;
    border-radius: 20px;
    padding: 40px;
    margin-bottom: 30px;
    box-shadow: 0 8px 32px rgba(82, 196, 26, 0.3);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 30px;
}

.logo-section {
    display: flex;
    align-items: center;
    gap: 20px;
}

.logo {
    font-size: 4em;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.brand-info h1 {
    font-size: 2.5em;
    font-weight: 700;
    margin-bottom: 10px;
}

.brand-info p {
    font-size: 1.2em;
    opacity: 0.9;
}

.project-stats {
    display: flex;
    gap: 30px;
}

.stat-item {
    text-align: center;
    background: rgba(255, 255, 255, 0.1);
    padding: 20px;
    border-radius: 15px;
    backdrop-filter: blur(10px);
}

.stat-number {
    display: block;
    font-size: 2.5em;
    font-weight: 700;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 0.9em;
    opacity: 0.9;
}

/* 导航菜单 */
.demo-nav {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-bottom: 30px;
    flex-wrap: wrap;
    background: white;
    padding: 20px;
    border-radius: 15px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.nav-btn {
    padding: 15px 25px;
    border: 2px solid #52C41A;
    background: white;
    color: #52C41A;
    border-radius: 25px;
    cursor: pointer;
    font-size: 16px;
    font-weight: 600;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.nav-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #52C41A, #73D13D);
    transition: left 0.3s ease;
    z-index: -1;
}

.nav-btn:hover::before,
.nav-btn.active::before {
    left: 0;
}

.nav-btn:hover,
.nav-btn.active {
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(82, 196, 26, 0.3);
}

/* 页面容器 */
.pages-container {
    display: flex;
    justify-content: center;
    min-height: 600px;
}

.page {
    display: none;
    width: 100%;
    justify-content: center;
    align-items: flex-start;
}

.page.active {
    display: flex;
}

/* 手机模拟器 */
.phone-mockup {
    width: 375px;
    height: 812px;
    background: white;
    border-radius: 30px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
    overflow: hidden;
    position: relative;
    border: 8px solid #333;
    transform: scale(0.9);
    transition: all 0.3s ease;
}

.phone-mockup:hover {
    transform: scale(0.92);
    box-shadow: 0 25px 80px rgba(0, 0, 0, 0.25);
}

/* 页面头部样式 */
.phone-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: white;
    border-bottom: 1px solid #f0f0f0;
    font-size: 14px;
    position: sticky;
    top: 0;
    z-index: 100;
}

.location {
    font-weight: 600;
    color: #333;
}

.header-actions {
    display: flex;
    gap: 15px;
}

.search-btn, .filter-btn {
    color: #52C41A;
    cursor: pointer;
    padding: 5px 10px;
    border-radius: 15px;
    transition: all 0.3s ease;
}

.search-btn:hover, .filter-btn:hover {
    background: #f6ffed;
}

.page-header {
    padding: 15px 20px;
    background: linear-gradient(135deg, #52C41A, #73D13D);
    color: white;
    display: flex;
    align-items: center;
    gap: 15px;
    position: sticky;
    top: 0;
    z-index: 100;
}

.page-header h2 {
    font-size: 18px;
    font-weight: 600;
    flex: 1;
    text-align: center;
}

.back-btn {
    color: white;
    cursor: pointer;
    font-size: 16px;
    padding: 5px 10px;
    border-radius: 15px;
    transition: all 0.3s ease;
}

.back-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

.date {
    font-size: 14px;
    opacity: 0.9;
}

/* 地图容器 */
.map-container {
    height: 300px;
    position: relative;
    background: linear-gradient(45deg, #e8f5e8 25%, #f0f9f0 25%, #f0f9f0 50%, #e8f5e8 50%, #e8f5e8 75%, #f0f9f0 75%);
    background-size: 20px 20px;
    animation: mapMove 20s linear infinite;
}

@keyframes mapMove {
    0% { background-position: 0 0; }
    100% { background-position: 20px 20px; }
}

.map-placeholder {
    width: 100%;
    height: 100%;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.map-bg {
    font-size: 20px;
    color: #52C41A;
    font-weight: 600;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.map-marker {
    position: absolute;
    font-size: 24px;
    animation: bounce 2s infinite;
    cursor: pointer;
    transition: all 0.3s ease;
}

.map-marker:hover {
    transform: scale(1.2);
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
}

.map-label {
    position: absolute;
    top: 35%;
    left: 25%;
    background: white;
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 500;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border: 2px solid #52C41A;
    color: #52C41A;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-15px); }
    60% { transform: translateY(-8px); }
}

/* 快捷操作区 */
.quick-actions {
    display: flex;
    padding: 20px;
    gap: 15px;
    background: #fafafa;
}

.action-card {
    flex: 1;
    background: white;
    border-radius: 15px;
    padding: 25px 15px;
    text-align: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    border: 2px solid transparent;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.action-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(82, 196, 26, 0.1), rgba(115, 209, 61, 0.1));
    transition: left 0.3s ease;
    z-index: 0;
}

.action-card:hover::before {
    left: 0;
}

.action-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border-color: #52C41A;
}

.action-card.highlight {
    background: linear-gradient(135deg, #52C41A, #73D13D);
    color: white;
    border: none;
    box-shadow: 0 8px 25px rgba(82, 196, 26, 0.4);
}

.action-card.highlight:hover {
    transform: translateY(-5px) scale(1.05);
}

.action-icon {
    font-size: 28px;
    margin-bottom: 10px;
    position: relative;
    z-index: 1;
}

.action-text {
    font-size: 14px;
    font-weight: 600;
    position: relative;
    z-index: 1;
}

/* 站点列表 */
.station-list {
    padding: 20px;
    padding-bottom: 100px;
    background: white;
}

.station-list h3 {
    margin-bottom: 20px;
    font-size: 18px;
    color: #333;
    font-weight: 600;
}

.station-card {
    background: white;
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 15px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    border: 2px solid #f0f0f0;
    cursor: pointer;
    transition: all 0.3s ease;
}

.station-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
    border-color: #52C41A;
}

.station-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.station-name {
    font-weight: 700;
    font-size: 16px;
    color: #333;
}

.station-meta {
    display: flex;
    gap: 12px;
    font-size: 14px;
}

.rating {
    color: #faad14;
    font-weight: 600;
}

.distance {
    color: #52C41A;
    font-weight: 600;
}

.station-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 12px;
    font-size: 14px;
    color: #666;
}

.status.open {
    color: #52C41A;
    font-weight: 600;
}

.station-services {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.service-tag {
    background: linear-gradient(135deg, #f6ffed, #e6f7ff);
    color: #52C41A;
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 500;
    border: 1px solid #b7eb8f;
}

/* 底部导航 */
.bottom-nav {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: white;
    border-top: 1px solid #f0f0f0;
    display: flex;
    padding: 10px 0;
    backdrop-filter: blur(10px);
}

.nav-item {
    flex: 1;
    text-align: center;
    padding: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    border-radius: 15px;
    margin: 0 5px;
}

.nav-item:hover {
    background: #f6ffed;
}

.nav-item.active {
    color: #52C41A;
    background: #f6ffed;
}

.nav-icon {
    font-size: 22px;
    margin-bottom: 4px;
}

.nav-label {
    font-size: 12px;
    font-weight: 500;
}

/* 搜索框 */
.search-box {
    padding: 20px;
    background: #fafafa;
}

.search-input {
    width: 100%;
    padding: 15px 20px;
    border: 2px solid #e8e8e8;
    border-radius: 25px;
    background: white;
    font-size: 16px;
    outline: none;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.search-input:focus {
    border-color: #52C41A;
    box-shadow: 0 0 0 4px rgba(82, 196, 26, 0.1);
    transform: scale(1.02);
}

/* 分类相关样式 */
.section {
    padding: 20px;
    background: white;
}

.section h3 {
    margin-bottom: 20px;
    font-size: 18px;
    color: #333;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.category-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
    margin-bottom: 20px;
}

.category-card {
    background: white;
    border-radius: 15px;
    padding: 20px 12px;
    text-align: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    border: 2px solid #f0f0f0;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.category-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(82, 196, 26, 0.1), rgba(115, 209, 61, 0.1));
    transition: left 0.3s ease;
    z-index: 0;
}

.category-card:hover::before {
    left: 0;
}

.category-card:hover {
    transform: translateY(-5px) scale(1.05);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border-color: #52C41A;
}

.category-icon {
    font-size: 36px;
    margin-bottom: 10px;
    position: relative;
    z-index: 1;
}

.category-name {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 6px;
    position: relative;
    z-index: 1;
}

.category-price {
    font-size: 12px;
    color: #52C41A;
    font-weight: 600;
    position: relative;
    z-index: 1;
}

.category-trend {
    font-size: 10px;
    margin-top: 4px;
    font-weight: 500;
    position: relative;
    z-index: 1;
}

.category-trend.up {
    color: #ff4d4f;
}

.category-trend.down {
    color: #52C41A;
}

/* 分类列表 */
.category-list {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    border: 1px solid #f0f0f0;
}

.category-item {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    transition: all 0.3s ease;
}

.category-item:last-child {
    border-bottom: none;
}

.category-item:hover {
    background: linear-gradient(135deg, #f6ffed, #e6f7ff);
    transform: translateX(5px);
}

.item-header {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 8px;
}

.item-icon {
    font-size: 24px;
    width: 30px;
}

.item-name {
    flex: 1;
    font-weight: 600;
    font-size: 16px;
    color: #333;
}

.item-price {
    color: #52C41A;
    font-weight: 600;
    font-size: 14px;
}

.item-desc {
    color: #666;
    font-size: 14px;
    margin-left: 45px;
    line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .demo-container {
        padding: 10px;
    }
    
    .phone-mockup {
        width: 100%;
        max-width: 375px;
        height: 600px;
        transform: scale(1);
    }
    
    .phone-mockup:hover {
        transform: scale(1);
    }
    
    .header-content {
        flex-direction: column;
        text-align: center;
    }
    
    .project-stats {
        flex-direction: column;
        gap: 15px;
    }
    
    .demo-nav {
        gap: 8px;
        padding: 15px;
    }
    
    .nav-btn {
        padding: 10px 15px;
        font-size: 14px;
    }
    
    .category-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* AI路线规划样式 */
.route-info {
    padding: 20px;
    background: linear-gradient(135deg, #f6ffed, #e6f7ff);
    border-bottom: 1px solid #f0f0f0;
}

.info-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
    font-size: 14px;
    color: #333;
    font-weight: 500;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
}

.stat-item {
    text-align: center;
    background: white;
    padding: 15px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.stat-value {
    font-size: 16px;
    font-weight: 700;
    color: #52C41A;
    margin-bottom: 4px;
}

.stat-label {
    font-size: 12px;
    color: #666;
}

.setting-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
}

.setting-card {
    background: white;
    border: 2px solid #e8e8e8;
    border-radius: 12px;
    padding: 16px 8px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.setting-card:hover {
    border-color: #52C41A;
    background: #f6ffed;
    transform: translateY(-2px);
}

.setting-card.active {
    border-color: #52C41A;
    background: linear-gradient(135deg, #52C41A, #73D13D);
    color: white;
}

.setting-icon {
    font-size: 24px;
    margin-bottom: 8px;
}

.setting-text {
    font-size: 12px;
    font-weight: 500;
}

.route-preview {
    padding: 20px;
    background: white;
}

.route-map {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 20px;
    text-align: center;
    border: 2px dashed #52C41A;
}

.route-line {
    font-size: 16px;
    margin-bottom: 10px;
    color: #333;
    font-weight: 500;
}

.route-distances {
    font-size: 12px;
    color: #666;
}

.route-details {
    padding: 0 20px 20px;
    max-height: 200px;
    overflow-y: auto;
}

.route-step {
    display: flex;
    gap: 12px;
    margin-bottom: 16px;
    padding: 15px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    border: 1px solid #f0f0f0;
    cursor: pointer;
    transition: all 0.3s ease;
}

.route-step:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    border-color: #52C41A;
}

.step-number {
    font-size: 16px;
    flex-shrink: 0;
}

.step-content {
    flex: 1;
}

.step-header {
    font-weight: 600;
    margin-bottom: 4px;
    font-size: 14px;
    color: #333;
}

.step-meta {
    font-size: 12px;
    color: #666;
    margin-bottom: 4px;
}

.step-desc {
    font-size: 12px;
    color: #52C41A;
    font-weight: 500;
}

.ai-suggestions {
    padding: 0 20px 20px;
}

.suggestion-list {
    background: linear-gradient(135deg, #e6f7ff, #f6ffed);
    border-radius: 12px;
    padding: 16px;
    margin: 0;
    list-style: none;
    border: 1px solid #91d5ff;
}

.suggestion-list li {
    font-size: 14px;
    color: #333;
    margin-bottom: 8px;
    line-height: 1.4;
}

.suggestion-list li:last-child {
    margin-bottom: 0;
}

.action-buttons {
    padding: 20px;
    display: flex;
    gap: 12px;
}

.btn {
    flex: 1;
    padding: 15px;
    border: none;
    border-radius: 12px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, #52C41A, #73D13D);
    color: white;
    box-shadow: 0 4px 15px rgba(82, 196, 26, 0.3);
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(82, 196, 26, 0.4);
}

.btn-secondary {
    background: white;
    color: #52C41A;
    border: 2px solid #52C41A;
}

.btn-secondary:hover {
    background: #f6ffed;
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(82, 196, 26, 0.2);
}

/* 行情页样式 */
.location-selector {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: #f6ffed;
    border-bottom: 1px solid #f0f0f0;
    font-size: 14px;
}

.change-location {
    color: #52C41A;
    cursor: pointer;
    font-weight: 500;
    padding: 5px 10px;
    border-radius: 15px;
    transition: all 0.3s ease;
}

.change-location:hover {
    background: white;
}

.trend-card {
    background: white;
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    border-left: 4px solid #52C41A;
    cursor: pointer;
    transition: all 0.3s ease;
}

.trend-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.trend-card.down {
    border-left-color: #ff4d4f;
}

.trend-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.product-name {
    font-weight: 600;
    font-size: 16px;
}

.trend-indicator {
    font-size: 14px;
    font-weight: 500;
}

.trend-card.up .trend-indicator {
    color: #ff4d4f;
}

.trend-card.down .trend-indicator {
    color: #52C41A;
}

.price-range {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.trend-desc {
    font-size: 14px;
    color: #666;
}

.chart-container {
    background: white;
    border-radius: 12px;
    padding: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    border: 1px solid #f0f0f0;
}

.chart-title {
    text-align: center;
    font-weight: 600;
    margin-bottom: 16px;
    color: #333;
}

.chart-placeholder {
    height: 120px;
    position: relative;
    background: #f8f9fa;
    border-radius: 8px;
    padding: 16px;
    border: 1px dashed #52C41A;
}

.chart-line {
    position: relative;
    height: 80px;
}

.chart-point {
    position: absolute;
    width: 8px;
    height: 8px;
    background: #52C41A;
    border-radius: 50%;
    font-size: 10px;
    color: #333;
    font-weight: 500;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.chart-labels {
    display: flex;
    justify-content: space-between;
    margin-top: 8px;
    font-size: 12px;
    color: #666;
}

.price-list {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    border: 1px solid #f0f0f0;
}

.price-item {
    display: flex;
    align-items: center;
    padding: 16px;
    border-bottom: 1px solid #f0f0f0;
    gap: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.price-item:last-child {
    border-bottom: none;
}

.price-item:hover {
    background: #f6ffed;
    transform: translateX(5px);
}

.item-icon {
    font-size: 20px;
    width: 24px;
}

.item-name {
    flex: 1;
    font-weight: 500;
}

.item-price {
    font-weight: 600;
    color: #333;
}

.price-change {
    font-size: 14px;
    font-weight: 500;
    min-width: 60px;
    text-align: right;
}

.price-change.up {
    color: #ff4d4f;
}

.price-change.down {
    color: #52C41A;
}

.price-change.stable {
    color: #8c8c8c;
}

/* 个人中心样式 */
.user-header {
    padding: 25px 20px;
    background: linear-gradient(135deg, #52C41A, #73D13D);
    color: white;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
}

.avatar {
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    backdrop-filter: blur(10px);
}

.username {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 4px;
}

.user-phone {
    font-size: 14px;
    opacity: 0.9;
}

.header-actions {
    display: flex;
    justify-content: space-between;
    font-size: 14px;
}

.settings, .messages {
    cursor: pointer;
    opacity: 0.9;
    padding: 5px 10px;
    border-radius: 15px;
    transition: all 0.3s ease;
}

.settings:hover, .messages:hover {
    background: rgba(255, 255, 255, 0.2);
    opacity: 1;
}

.achievement-card {
    margin: 20px;
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    border: 2px solid #f0f0f0;
}

.achievement-title {
    font-size: 18px;
    font-weight: 600;
    color: #52C41A;
    margin-bottom: 15px;
    text-align: center;
}

.achievement-stats {
    display: flex;
    justify-content: space-around;
    margin-bottom: 15px;
}

.stat {
    text-align: center;
}

.stat-label {
    display: block;
    font-size: 14px;
    color: #666;
    margin-bottom: 4px;
}

.stat-value {
    display: block;
    font-size: 20px;
    font-weight: 600;
    color: #333;
}

.eco-contribution {
    text-align: center;
    font-size: 14px;
    color: #52C41A;
    background: linear-gradient(135deg, #f6ffed, #e6f7ff);
    padding: 12px;
    border-radius: 8px;
    font-weight: 500;
}

.service-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
    padding: 0 20px;
}

.service-item {
    background: white;
    border-radius: 12px;
    padding: 20px 10px;
    text-align: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    border: 1px solid #f0f0f0;
    cursor: pointer;
    transition: all 0.3s ease;
}

.service-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    border-color: #52C41A;
}

.service-icon {
    font-size: 24px;
    margin-bottom: 8px;
    display: block;
}

.service-text {
    font-size: 14px;
    font-weight: 500;
    color: #333;
}

.tool-list {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    margin: 0 20px 100px;
    border: 1px solid #f0f0f0;
}

.tool-item {
    display: flex;
    align-items: center;
    padding: 16px;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    transition: all 0.3s ease;
}

.tool-item:last-child {
    border-bottom: none;
}

.tool-item:hover {
    background: #f6ffed;
    transform: translateX(5px);
}

.tool-icon {
    font-size: 20px;
    margin-right: 12px;
    width: 24px;
}

.tool-name {
    flex: 1;
    font-size: 16px;
    color: #333;
}

.tool-arrow {
    color: #8c8c8c;
    font-size: 14px;
}
