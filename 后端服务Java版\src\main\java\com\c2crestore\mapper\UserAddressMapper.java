package com.c2crestore.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.c2crestore.entity.UserAddress;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 用户地址Mapper接口
 * 
 * <AUTHOR> Restore Team
 * @since 2024-01-15
 */
@Mapper
public interface UserAddressMapper extends BaseMapper<UserAddress> {

    /**
     * 获取用户地址列表
     * 
     * @param userId 用户ID
     * @return 地址列表
     */
    @Select("SELECT * FROM user_addresses WHERE user_id = #{userId} AND deleted = 0 ORDER BY is_default DESC, created_at DESC")
    List<UserAddress> selectByUserId(@Param("userId") Long userId);

    /**
     * 获取用户默认地址
     * 
     * @param userId 用户ID
     * @return 默认地址
     */
    @Select("SELECT * FROM user_addresses WHERE user_id = #{userId} AND is_default = 1 AND deleted = 0")
    UserAddress selectDefaultByUserId(@Param("userId") Long userId);

    /**
     * 清除用户所有默认地址
     * 
     * @param userId 用户ID
     * @return 影响行数
     */
    @Update("UPDATE user_addresses SET is_default = 0 WHERE user_id = #{userId}")
    int clearDefaultByUserId(@Param("userId") Long userId);

    /**
     * 获取用户地址数量
     * 
     * @param userId 用户ID
     * @return 地址数量
     */
    @Select("SELECT COUNT(*) FROM user_addresses WHERE user_id = #{userId} AND deleted = 0")
    int countByUserId(@Param("userId") Long userId);
}
