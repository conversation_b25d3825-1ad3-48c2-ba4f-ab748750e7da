import { Map } from 'immutable';

import * as actionTypes from './actionTypes';

const defaultState = Map({
    hotAlbums: [],
    topAlbums: [],
    topZNAlbums: [],
    topEAAlbums: [],
    topKRAlbums: [],
    topJPAlbums: [],
    topTotal: 0
})

function reducer(state = defaultState, action: any) {
    switch (action.type) {
        case actionTypes.CHANGE_HOT_ALBUMS:
            return state.set("hotAlbums", action.hotAlbums);
        case actionTypes.CHANGE_TOP_ALBUMS:
            return state.set("topAlbums", action.topAlbums);
        case actionTypes.CHANGE_TOP_TOTAL:
            return state.set("topTotal", action.total);
        default:
            return state;
    }
}

export default reducer
