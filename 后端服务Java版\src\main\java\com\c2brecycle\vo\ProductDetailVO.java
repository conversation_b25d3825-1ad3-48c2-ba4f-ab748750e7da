package com.c2brecycle.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 产品详情VO
 * 
 * <AUTHOR> Recycle Team
 * @since 2024-01-15
 */
@Data
@ApiModel("产品详情信息")
public class ProductDetailVO {

    @ApiModelProperty("产品ID")
    private Long id;

    @ApiModelProperty("产品名称")
    private String name;

    @ApiModelProperty("产品图片")
    private String image;

    @ApiModelProperty("产品图片列表")
    private List<String> images;

    @ApiModelProperty("分类ID")
    private Long categoryId;

    @ApiModelProperty("分类名称")
    private String categoryName;

    @ApiModelProperty("产品描述")
    private String description;

    @ApiModelProperty("详细说明")
    private String content;

    @ApiModelProperty("单位")
    private String unit;

    @ApiModelProperty("最低价格")
    private BigDecimal minPrice;

    @ApiModelProperty("最高价格")
    private BigDecimal maxPrice;

    @ApiModelProperty("平均价格")
    private BigDecimal avgPrice;

    @ApiModelProperty("搜索次数")
    private Integer searchCount;

    @ApiModelProperty("是否已收藏")
    private Boolean isFavorited;

    @ApiModelProperty("状态 0-下架 1-上架")
    private Integer status;

    @ApiModelProperty("价格列表")
    private List<PriceInfoVO> priceList;

    @ApiModelProperty("相关产品")
    private List<ProductVO> relatedProducts;

    @ApiModelProperty("创建时间")
    private LocalDateTime createdAt;

    /**
     * 价格信息VO
     */
    @Data
    @ApiModel("价格信息")
    public static class PriceInfoVO {
        
        @ApiModelProperty("站点ID")
        private Long stationId;
        
        @ApiModelProperty("站点名称")
        private String stationName;
        
        @ApiModelProperty("站点地址")
        private String stationAddress;
        
        @ApiModelProperty("价格")
        private BigDecimal price;
        
        @ApiModelProperty("价格类型 1-收购价 2-零售价")
        private Integer priceType;
        
        @ApiModelProperty("更新时间")
        private LocalDateTime updatedAt;
    }
}
