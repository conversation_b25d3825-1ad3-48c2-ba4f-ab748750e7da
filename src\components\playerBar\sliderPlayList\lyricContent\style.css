.lyricContentBox {
    position: relative;
    width: 423px;
    height: 100%;
    overflow-x: auto;
    padding: 12px 22px 7px;
}

.lyricContentBox::-webkit-scrollbar-thumb:horizontal {
    /*水平滚动条的样式*/
    width: 4px;
    background-color: #9f9f9f;
    -webkit-border-radius: 4px;
}
.lyricContentBox::-webkit-scrollbar-track-piece {
    background-color: #1a1a1a; /*滚动条的背景颜色*/
    -webkit-border-radius: 0; /*滚动条的圆角宽度*/
}
.lyricContentBox::-webkit-scrollbar {
    width: 8px; /*滚动条的宽度*/
    height: 6px; /*滚动条的高度*/
  }
.lyricContentBox::-webkit-scrollbar-thumb:vertical {
    /*垂直滚动条的样式*/
    height: 50px;
    background-color: #9f9f9f;
    -webkit-border-radius: 4px;
    /* outline: 2px solid #000; */
    /* outline-offset: -2px; */
    border: 2px solid #9f9f9f;
}

.lyricContentBox .lyricContent {
    position: absolute;
    top: 20px;
    left: 0;
    right: 0;
}
.lyricContentBox .lyricContent .lyricItem {
    height: auto !important;
    line-height: 32px;
    color: #989898;
    transition: color 0.7s linear;
    /* position: relative; */
    /* transition: all 1s;+ */
}

.lyricContentBox .active {
    /* top: 12px; */
    color: #fff!important;
}

.para{
    width: 100%;
    height:4rem;
    background: green;
    position: relative;

    .lyricItem{
        width: 10px;
        height: 4px;
    }
    P{
     front-size: 1.5rem;   
     position: absolute;
    }

}

  
