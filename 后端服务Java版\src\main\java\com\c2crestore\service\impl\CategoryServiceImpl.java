package com.c2crestore.service.impl;

import com.c2crestore.common.exception.BusinessException;
import com.c2crestore.common.result.ResultCode;
import com.c2crestore.entity.Category;
import com.c2crestore.mapper.CategoryMapper;
import com.c2crestore.service.CategoryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 分类服务实现类
 * 
 * <AUTHOR> Restore Team
 * @since 2024-01-15
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CategoryServiceImpl implements CategoryService {

    private final CategoryMapper categoryMapper;

    @Override
    @Cacheable(value = "categories", key = "'all'")
    public List<Category> getAllCategories() {
        log.info("获取所有分类列表");
        
        List<Category> categories = categoryMapper.selectAllEnabled();
        
        log.info("获取分类列表成功，数量: {}", categories.size());
        return categories;
    }

    @Override
    @Cacheable(value = "categories", key = "#categoryId")
    public Category getCategoryById(Long categoryId) {
        log.info("根据ID获取分类，categoryId: {}", categoryId);
        
        if (categoryId == null || categoryId <= 0) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "分类ID不能为空");
        }
        
        Category category = categoryMapper.selectById(categoryId);
        if (category == null || category.getDeleted() == 1) {
            throw new BusinessException(ResultCode.CATEGORY_NOT_FOUND);
        }
        
        if (category.getStatus() != 1) {
            throw new BusinessException(ResultCode.CATEGORY_NOT_FOUND, "分类已禁用");
        }
        
        log.info("获取分类成功，categoryName: {}", category.getName());
        return category;
    }
}
