import React, { memo,  Fragment } from 'react';
import { shallowEqual, useDispatch, useSelector } from 'react-redux';

import { getSizeImage } from '../../../../utils/formatUtils';
import { changeCurrentIndexAction, changeCurrentToplistIdAction } from '../store/actionCreators'
import propTypes from 'prop-types';
import { NavLink } from 'react-router-dom';
import './style.css'

function TopListItem(props: any) {
  const { toplistInfo } = props;
  const dispatch = useDispatch()
  const {currentIndex} = useSelector((state: any) => ({
    currentIndex: state.getIn(['toplist', 'currentIndex'])
  }), shallowEqual)

  // other function
  const clickItem = (index: any, id: any) => {
    dispatch(changeCurrentToplistIdAction(id))
    dispatch(changeCurrentIndexAction(index))
  };

  return (
    <div className="toplistItemBox">
      {toplistInfo.map((item: any, index: any) => {
        return (
          <Fragment key={item.id}>
            <h3 style={{ marginTop: index === 4 ? '17px' : '' }}>
              {index === 0 ? '云音乐特色榜' : index === 4 ? '全球媒体榜' : ''}
            </h3>
            <NavLink
              className={"toplistItemInfo " + (index === currentIndex ? 'toplistItemBg' : '')}
              onClick={() => clickItem(index, item.id)}
              to={`/discover/toplist?id=${item.id}`}
            >
              
            <img src={getSizeImage(item.coverImgUrl, 44)} alt="" />
              
              <div className="toplistItemInfoRight">
                <div className="toplistItemInfoTitle">{item.name}</div>
                <div className="toplistItemInfoUpdate">{item.updateFrequency}</div>
              </div>
            </NavLink>
          </Fragment>
        );
      })}
    </div>
  );
}

TopListItem.propTypes = {
  toplistInfo: propTypes.any,
};

TopListItem.defaultProps = {
  selected: true,
};

export default memo(TopListItem);
