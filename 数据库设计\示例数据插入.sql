-- =============================================
-- 慢慢回收小程序 MVP版本示例数据插入脚本
-- 版本: V1.0
-- 创建日期: 2024-01-15
-- 说明: 用于测试和演示的示例数据
-- =============================================

SET NAMES utf8mb4;

-- =============================================
-- 1. 插入示例用户数据
-- =============================================

INSERT INTO `users` (`openid`, `unionid`, `nickname`, `avatar_url`, `phone`, `gender`, `city`, `province`, `country`, `status`) VALUES
('wx_openid_001', 'wx_unionid_001', '环保小达人', 'https://example.com/avatar1.jpg', '13800138001', 1, '北京市', '北京市', '中国', 1),
('wx_openid_002', 'wx_unionid_002', '绿色生活家', 'https://example.com/avatar2.jpg', '13800138002', 2, '上海市', '上海市', '中国', 1),
('wx_openid_003', 'wx_unionid_003', '回收达人', 'https://example.com/avatar3.jpg', '***********', 1, '深圳市', '广东省', '中国', 1),
('wx_openid_004', 'wx_unionid_004', '废品收集者', 'https://example.com/avatar4.jpg', '***********', 2, '杭州市', '浙江省', '中国', 1),
('wx_openid_005', 'wx_unionid_005', '环保志愿者', 'https://example.com/avatar5.jpg', '13800138005', 1, '成都市', '四川省', '中国', 1);

-- =============================================
-- 2. 插入示例用户地址数据
-- =============================================

INSERT INTO `user_addresses` (`user_id`, `name`, `phone`, `province`, `city`, `district`, `address`, `longitude`, `latitude`, `is_default`) VALUES
(1, '张三', '13800138001', '北京市', '北京市', '朝阳区', '朝阳区建国门外大街1号', 116.4074, 39.9042, 1),
(1, '张三', '13800138001', '北京市', '北京市', '海淀区', '海淀区中关村大街27号', 116.3112, 39.9991, 0),
(2, '李四', '13800138002', '上海市', '上海市', '浦东新区', '浦东新区陆家嘴环路1000号', 121.4737, 31.2304, 1),
(3, '王五', '***********', '广东省', '深圳市', '南山区', '南山区深南大道10000号', 113.9306, 22.5329, 1),
(4, '赵六', '***********', '浙江省', '杭州市', '西湖区', '西湖区文三路259号', 120.1551, 30.2741, 1);

-- =============================================
-- 3. 插入示例回收站点数据
-- =============================================

INSERT INTO `recycle_stations` (`name`, `phone`, `address`, `longitude`, `latitude`, `province`, `city`, `district`, `business_hours`, `description`, `images`, `rating`, `review_count`, `status`, `features`) VALUES
('绿色回收站', '010-12345678', '北京市朝阳区建国门外大街100号', 116.4074, 39.9042, '北京市', '北京市', '朝阳区', '08:00-18:00', '专业回收电子产品和金属类废品，价格公道，服务周到', '["https://example.com/station1_1.jpg", "https://example.com/station1_2.jpg"]', 4.8, 156, 1, '["现金结算", "免费停车", "专业称重"]'),

('环保回收中心', '010-87654321', '北京市海淀区中关村大街200号', 116.3112, 39.9991, '北京市', '北京市', '海淀区', '09:00-17:00', '高价回收各类电子产品，支持上门回收服务', '["https://example.com/station2_1.jpg", "https://example.com/station2_2.jpg"]', 4.5, 89, 1, '["上门回收", "微信支付", "专业拆解"]'),

('蓝天回收点', '010-11223344', '北京市朝阳区三里屯路300号', 116.4551, 39.9289, '北京市', '北京市', '朝阳区', '08:30-19:00', '回收各类废品，特别擅长纸类和塑料回收', '["https://example.com/station3_1.jpg"]', 4.2, 67, 1, '["分类回收", "环保处理", "快速结算"]'),

('金属回收站', '021-12345678', '上海市浦东新区陆家嘴环路500号', 121.4737, 31.2304, '上海市', '上海市', '浦东新区', '07:00-16:00', '专业回收各类金属废料，价格透明，诚信经营', '["https://example.com/station4_1.jpg", "https://example.com/station4_2.jpg"]', 4.6, 134, 1, '["金属专业", "价格透明", "大宗回收"]'),

('智能回收站', '0755-12345678', '深圳市南山区深南大道1000号', 113.9306, 22.5329, '广东省', '深圳市', '南山区', '24小时', '智能化回收设备，24小时自助回收服务', '["https://example.com/station5_1.jpg"]', 4.9, 203, 1, '["24小时", "智能设备", "自助服务"]');

-- =============================================
-- 4. 插入示例产品数据
-- =============================================

INSERT INTO `products` (`category_id`, `name`, `brand`, `model`, `specifications`, `images`, `keywords`) VALUES
(5, 'Intel酷睿i7处理器', 'Intel', 'i7-12700K', '12核心20线程，基础频率3.6GHz，最大睿频5.0GHz', '["https://example.com/cpu1.jpg"]', 'Intel CPU 处理器 i7 12700K'),
(5, 'NVIDIA显卡', 'NVIDIA', 'RTX 3080', '显存10GB GDDR6X，CUDA核心8704个', '["https://example.com/gpu1.jpg"]', 'NVIDIA 显卡 RTX 3080 GPU'),
(5, '金士顿内存条', 'Kingston', 'DDR4-3200 16GB', '16GB DDR4 3200MHz 台式机内存', '["https://example.com/ram1.jpg"]', '金士顿 内存 DDR4 16GB'),
(6, 'iPhone手机', 'Apple', 'iPhone 13 Pro', '6.1英寸Super Retina XDR显示屏，A15仿生芯片', '["https://example.com/iphone1.jpg"]', 'iPhone 苹果 手机 13 Pro'),
(6, '华为手机', 'Huawei', 'Mate 40 Pro', '6.76英寸OLED曲面屏，麒麟9000芯片', '["https://example.com/huawei1.jpg"]', '华为 手机 Mate 40 Pro'),
(6, '小米平板', 'Xiaomi', 'Mi Pad 5', '11英寸2.5K显示屏，骁龙860处理器', '["https://example.com/mipad1.jpg"]', '小米 平板 Mi Pad 5'),
(7, '海尔冰箱', 'Haier', 'BCD-470WDPG', '470升风冷无霜多门冰箱', '["https://example.com/fridge1.jpg"]', '海尔 冰箱 470升 风冷'),
(7, '格力空调', 'Gree', 'KFR-35GW', '1.5匹变频冷暖空调', '["https://example.com/ac1.jpg"]', '格力 空调 1.5匹 变频'),
(8, '废铜线', '通用', '电线电缆', '各种规格废铜线，纯度95%以上', '["https://example.com/copper1.jpg"]', '废铜 铜线 电缆 金属'),
(9, '废铝制品', '通用', '铝合金', '废铝门窗、铝制品等', '["https://example.com/aluminum1.jpg"]', '废铝 铝合金 金属 门窗');

-- =============================================
-- 5. 插入示例价格数据
-- =============================================

INSERT INTO `product_prices` (`product_id`, `station_id`, `min_price`, `max_price`, `avg_price`, `unit`, `condition_desc`, `price_date`) VALUES
-- Intel i7处理器价格
(1, 1, 800.00, 1200.00, 1000.00, '台', '正常使用，无损坏', '2024-01-15'),
(1, 2, 850.00, 1250.00, 1050.00, '台', '正常使用，无损坏', '2024-01-15'),
(1, NULL, 825.00, 1225.00, 1025.00, '台', '市场均价', '2024-01-15'),

-- NVIDIA显卡价格
(2, 1, 2000.00, 3000.00, 2500.00, '台', '正常使用，性能良好', '2024-01-15'),
(2, 2, 2100.00, 3100.00, 2600.00, '台', '正常使用，性能良好', '2024-01-15'),
(2, NULL, 2050.00, 3050.00, 2550.00, '台', '市场均价', '2024-01-15'),

-- iPhone 13 Pro价格
(4, 1, 3000.00, 4500.00, 3750.00, '台', '外观良好，功能正常', '2024-01-15'),
(4, 2, 3100.00, 4600.00, 3850.00, '台', '外观良好，功能正常', '2024-01-15'),
(4, NULL, 3050.00, 4550.00, 3800.00, '台', '市场均价', '2024-01-15'),

-- 废铜线价格
(9, 1, 45.00, 50.00, 47.50, 'kg', '纯度95%以上', '2024-01-15'),
(9, 4, 46.00, 51.00, 48.50, 'kg', '纯度95%以上', '2024-01-15'),
(9, NULL, 45.50, 50.50, 48.00, 'kg', '市场均价', '2024-01-15'),

-- 废铝制品价格
(10, 1, 12.00, 15.00, 13.50, 'kg', '干净无杂质', '2024-01-15'),
(10, 4, 12.50, 15.50, 14.00, 'kg', '干净无杂质', '2024-01-15'),
(10, NULL, 12.25, 15.25, 13.75, 'kg', '市场均价', '2024-01-15');

-- =============================================
-- 6. 插入站点服务关系数据
-- =============================================

INSERT INTO `station_services` (`station_id`, `category_id`, `min_price`, `max_price`, `unit`, `is_active`) VALUES
-- 绿色回收站服务
(1, 5, 50.00, 1500.00, '台', 1),  -- 电脑配件
(1, 6, 100.00, 5000.00, '台', 1), -- 手机数码
(1, 8, 40.00, 55.00, 'kg', 1),    -- 废铜
(1, 9, 10.00, 18.00, 'kg', 1),    -- 废铝

-- 环保回收中心服务
(2, 5, 80.00, 1600.00, '台', 1),  -- 电脑配件
(2, 6, 150.00, 5200.00, '台', 1), -- 手机数码
(2, 7, 200.00, 2000.00, '台', 1), -- 家用电器

-- 蓝天回收点服务
(3, 3, 1.50, 3.50, 'kg', 1),      -- 纸类
(3, 4, 0.80, 2.20, 'kg', 1),      -- 塑料类
(3, 9, 11.00, 16.00, 'kg', 1),    -- 废铝

-- 金属回收站服务
(4, 8, 42.00, 52.00, 'kg', 1),    -- 废铜
(4, 9, 11.50, 17.00, 'kg', 1),    -- 废铝
(4, 10, 2.50, 4.50, 'kg', 1),     -- 废铁

-- 智能回收站服务
(5, 5, 60.00, 1400.00, '台', 1),  -- 电脑配件
(5, 6, 120.00, 4800.00, '台', 1), -- 手机数码
(5, 3, 1.80, 3.20, 'kg', 1),      -- 纸类
(5, 4, 1.00, 2.00, 'kg', 1);      -- 塑料类

-- =============================================
-- 7. 插入示例用户行为数据
-- =============================================

-- 用户搜索记录
INSERT INTO `user_search_logs` (`user_id`, `keyword`, `search_type`, `result_count`) VALUES
(1, 'iPhone', 1, 5),
(1, '回收站', 2, 8),
(2, '笔记本电脑', 1, 12),
(2, '废铜价格', 1, 3),
(3, '显卡', 1, 7),
(3, '朝阳区回收', 2, 6),
(4, '冰箱', 1, 4),
(5, '手机回收', 1, 9);

-- 用户收藏记录
INSERT INTO `user_favorites` (`user_id`, `target_type`, `target_id`) VALUES
(1, 1, 1), -- 收藏绿色回收站
(1, 1, 2), -- 收藏环保回收中心
(1, 2, 4), -- 收藏iPhone产品
(2, 1, 4), -- 收藏金属回收站
(2, 2, 1), -- 收藏Intel处理器
(3, 1, 1), -- 收藏绿色回收站
(3, 2, 2), -- 收藏NVIDIA显卡
(4, 1, 3), -- 收藏蓝天回收点
(5, 1, 5); -- 收藏智能回收站

-- 用户浏览记录
INSERT INTO `user_browse_logs` (`user_id`, `target_type`, `target_id`, `duration`) VALUES
(1, 1, 1, 120), -- 浏览绿色回收站2分钟
(1, 2, 4, 45),  -- 浏览iPhone产品45秒
(2, 1, 4, 180), -- 浏览金属回收站3分钟
(2, 2, 1, 60),  -- 浏览Intel处理器1分钟
(3, 1, 1, 90),  -- 浏览绿色回收站1.5分钟
(3, 2, 2, 75),  -- 浏览NVIDIA显卡1分15秒
(4, 1, 3, 150), -- 浏览蓝天回收点2.5分钟
(5, 1, 5, 200); -- 浏览智能回收站3分20秒

-- =============================================
-- 8. 插入示例评价数据
-- =============================================

INSERT INTO `station_reviews` (`user_id`, `station_id`, `rating`, `content`, `service_rating`, `price_rating`, `environment_rating`, `is_anonymous`, `status`) VALUES
(1, 1, 5, '服务很好，价格公道，老板人很nice！强烈推荐！', 5, 5, 4, 0, 1),
(2, 1, 4, '回收价格还可以，就是排队时间有点长。', 4, 4, 4, 0, 1),
(3, 2, 5, '上门回收很方便，师傅很专业，价格也合理。', 5, 4, 5, 0, 1),
(4, 3, 4, '分类做得很好，环保意识强，值得支持。', 4, 3, 5, 0, 1),
(5, 4, 5, '专业的金属回收站，价格透明，称重准确。', 5, 5, 4, 0, 1),
(1, 5, 5, '24小时营业太方便了，智能设备操作简单。', 5, 4, 5, 0, 1),
(2, 2, 3, '价格一般，服务态度需要改进。', 3, 3, 4, 1, 1),
(3, 1, 4, '总体不错，就是停车位有点紧张。', 4, 4, 3, 0, 1);

-- =============================================
-- 9. 更新站点评分统计
-- =============================================

-- 更新绿色回收站评分
UPDATE `recycle_stations` SET 
    `rating` = (SELECT AVG(rating) FROM `station_reviews` WHERE `station_id` = 1 AND `status` = 1),
    `review_count` = (SELECT COUNT(*) FROM `station_reviews` WHERE `station_id` = 1 AND `status` = 1)
WHERE `id` = 1;

-- 更新环保回收中心评分
UPDATE `recycle_stations` SET 
    `rating` = (SELECT AVG(rating) FROM `station_reviews` WHERE `station_id` = 2 AND `status` = 1),
    `review_count` = (SELECT COUNT(*) FROM `station_reviews` WHERE `station_id` = 2 AND `status` = 1)
WHERE `id` = 2;

-- 更新蓝天回收点评分
UPDATE `recycle_stations` SET 
    `rating` = (SELECT AVG(rating) FROM `station_reviews` WHERE `station_id` = 3 AND `status` = 1),
    `review_count` = (SELECT COUNT(*) FROM `station_reviews` WHERE `station_id` = 3 AND `status` = 1)
WHERE `id` = 3;

-- 更新金属回收站评分
UPDATE `recycle_stations` SET 
    `rating` = (SELECT AVG(rating) FROM `station_reviews` WHERE `station_id` = 4 AND `status` = 1),
    `review_count` = (SELECT COUNT(*) FROM `station_reviews` WHERE `station_id` = 4 AND `status` = 1)
WHERE `id` = 4;

-- 更新智能回收站评分
UPDATE `recycle_stations` SET 
    `rating` = (SELECT AVG(rating) FROM `station_reviews` WHERE `station_id` = 5 AND `status` = 1),
    `review_count` = (SELECT COUNT(*) FROM `station_reviews` WHERE `station_id` = 5 AND `status` = 1)
WHERE `id` = 5;

-- =============================================
-- 完成示例数据插入
-- =============================================

SELECT '示例数据插入完成！' as message;
