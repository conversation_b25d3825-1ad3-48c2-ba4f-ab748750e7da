# 服务器配置
NODE_ENV=development
PORT=3000
HOST=0.0.0.0

# 跨域配置
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8080

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_NAME=c2b_recycle
DB_USER=root
DB_PASSWORD=123456

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# JWT配置
JWT_SECRET=c2b-recycle-jwt-secret-key-for-token-generation
JWT_EXPIRES_IN=7d
JWT_REFRESH_EXPIRES_IN=30d

# 微信小程序配置
WECHAT_APP_ID=your-wechat-app-id
WECHAT_APP_SECRET=your-wechat-app-secret

# 文件上传配置
UPLOAD_PATH=./uploads
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif

# 日志配置
LOG_LEVEL=info
LOG_FILE=logs/app.log

# 价格监控配置
PRICE_ALERT_ENABLED=true
PRICE_UPDATE_INTERVAL=300000
WEBSOCKET_ENABLED=true

# 外部API配置
GEOCODING_API_KEY=your-geocoding-api-key
SMS_API_KEY=your-sms-api-key
EMAIL_API_KEY=your-email-api-key
