import { getSizeImage } from "../../utils/formatUtils";
import './style.css'

export default function RadioRecommendCover(props: any) {
    const { info } = props;

    return (
        <div className='radioNewItem'>
            <a href="#/discover/djRadio/todo">
                <img className='radioNewIcon' src={getSizeImage(info.picUrl, 150)} alt="" />
            </a>
            <h3 className='radioNewName'>
                <a href="#/discover/djRadio/todo" >
                    {info.name}
                </a>
            </h3>
            <p className='radioNewDetails'>{info.desc}</p>
        </div>
    )
}
