package com.c2crestore.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.c2crestore.entity.Category;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 产品分类Mapper接口
 * 
 * <AUTHOR> Restore Team
 * @since 2024-01-15
 */
@Mapper
public interface CategoryMapper extends BaseMapper<Category> {

    /**
     * 获取所有启用的分类
     * 
     * @return 分类列表
     */
    @Select("SELECT * FROM categories WHERE status = 1 AND deleted = 0 ORDER BY sort ASC, created_at ASC")
    List<Category> selectAllEnabled();

    /**
     * 根据名称查询分类
     * 
     * @param name 分类名称
     * @return 分类信息
     */
    @Select("SELECT * FROM categories WHERE name = #{name} AND deleted = 0")
    Category selectByName(@Param("name") String name);
}
