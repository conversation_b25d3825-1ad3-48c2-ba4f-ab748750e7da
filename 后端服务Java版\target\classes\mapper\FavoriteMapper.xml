<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.c2brecycle.mapper.FavoriteMapper">

    <!-- 收藏VO结果映射 -->
    <resultMap id="FavoriteVOMap" type="com.c2brecycle.vo.FavoriteVO">
        <id column="id" property="id"/>
        <result column="type" property="type"/>
        <result column="target_id" property="targetId"/>
        <result column="target_name" property="targetName"/>
        <result column="target_image" property="targetImage"/>
        <result column="target_description" property="targetDescription"/>
        <result column="extra_info" property="extraInfo"/>
        <result column="price" property="price"/>
        <result column="rating" property="rating"/>
        <result column="created_at" property="createdAt"/>
    </resultMap>

    <!-- 获取用户收藏列表 -->
    <select id="selectUserFavorites" resultMap="FavoriteVOMap">
        SELECT 
            f.id,
            f.type,
            f.target_id,
            f.created_at,
            CASE 
                WHEN f.type = 1 THEN s.name
                WHEN f.type = 2 THEN p.name
            END as target_name,
            CASE 
                WHEN f.type = 1 THEN s.image
                WHEN f.type = 2 THEN p.image
            END as target_image,
            CASE 
                WHEN f.type = 1 THEN s.address
                WHEN f.type = 2 THEN p.description
            END as target_description,
            CASE 
                WHEN f.type = 1 THEN s.hours
                WHEN f.type = 2 THEN p.unit
            END as extra_info,
            CASE 
                WHEN f.type = 2 THEN p.avg_price
                ELSE NULL
            END as price,
            CASE 
                WHEN f.type = 1 THEN s.rating
                ELSE NULL
            END as rating
        FROM favorites f
        LEFT JOIN stations s ON f.type = 1 AND f.target_id = s.id AND s.deleted = 0
        LEFT JOIN products p ON f.type = 2 AND f.target_id = p.id AND p.deleted = 0
        WHERE f.user_id = #{userId} AND f.deleted = 0
        <if test="type != null">
            AND f.type = #{type}
        </if>
        ORDER BY f.created_at DESC
    </select>

</mapper>
