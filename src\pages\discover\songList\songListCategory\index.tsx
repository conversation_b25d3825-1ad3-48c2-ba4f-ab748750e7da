import React, { memo } from 'react';
import { useSelector, useDispatch, shallowEqual } from "react-redux";

import {
    changeCategoryIsVisible,
    changeCurrentCategoryAction,
    getSongList
} from "../store/actionCreators";
import './style.css'

export default memo(function SongListCategory() {
    // redux

    const { category } = useSelector((state: any) => ({
        category: state.getIn(["songList", "category"])
    }), shallowEqual);
    
    const dispatch = useDispatch();

    function selectCategory(name: any) {
        dispatch(changeCurrentCategoryAction(name));
        dispatch(getSongList(0));
    }
    

    return (
        <div className="songListCategoryBox">
            <div className="songListCategoryArrow"></div>
            <div className="songListCategoryAll">
                <a href='#/discover/songList/?cat=全部' onClick={() => {selectCategory("全部"); dispatch(changeCategoryIsVisible(false));}}>全部风格</a>
            </div>
            <div className="songListCategory">
                {
                    category.map((item: any, index: any) => {
                        return (
                            <dl key={item.name} className={"songListCategoryItem" + index}>
                                <dt>
                                    <i className="songListCategoryIcon"></i>
                                    <span>{item.name}</span>
                                </dt>
                                <dd>
                                    {
                                        item.subs.map((sItem: any) => {
                                            return (
                                                <div key={sItem.name}>
                                                    <a href={`#/discover/songList/?cat=${sItem.name}`} onClick={() => {selectCategory(sItem.name);dispatch(changeCategoryIsVisible(false));}}>{sItem.name}</a>
                                                    <span className="songListCategoryDivider">|</span>
                                                </div>
                                            )
                                        })
                                    }
                                </dd>
                            </dl>
                        )
                    })
                }
            </div>
        </div>
    )
})
