# 🎯 慢慢回收核心接口深度设计

## 📊 价格行情系统设计

### 1. 价格数据模型

#### 1.1 实时价格表 (real_time_prices)
```sql
CREATE TABLE `real_time_prices` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` int(11) NOT NULL COMMENT '产品ID',
  `station_id` int(11) DEFAULT NULL COMMENT '站点ID(NULL表示市场价)',
  `price_type` tinyint(1) NOT NULL COMMENT '价格类型 1-收购价 2-零售价 3-批发价',
  `current_price` decimal(10,2) NOT NULL COMMENT '当前价格',
  `min_price` decimal(10,2) NOT NULL COMMENT '最低价',
  `max_price` decimal(10,2) NOT NULL COMMENT '最高价',
  `unit` varchar(10) NOT NULL COMMENT '单位',
  `quality_grade` varchar(20) DEFAULT NULL COMMENT '品质等级',
  `price_source` varchar(50) DEFAULT NULL COMMENT '价格来源',
  `confidence_score` decimal(3,2) DEFAULT 1.00 COMMENT '价格可信度 0-1',
  `last_updated` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_product_station_type` (`product_id`, `station_id`, `price_type`),
  KEY `idx_product_updated` (`product_id`, `last_updated`),
  KEY `idx_station_updated` (`station_id`, `last_updated`)
) ENGINE=InnoDB COMMENT='实时价格表';
```

#### 1.2 价格历史表 (price_history)
```sql
CREATE TABLE `price_history` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` int(11) NOT NULL,
  `station_id` int(11) DEFAULT NULL,
  `price_type` tinyint(1) NOT NULL,
  `price` decimal(10,2) NOT NULL,
  `volume` int(11) DEFAULT 0 COMMENT '交易量',
  `change_rate` decimal(5,2) DEFAULT 0.00 COMMENT '变化率%',
  `change_reason` varchar(100) DEFAULT NULL COMMENT '变化原因',
  `market_trend` tinyint(1) DEFAULT 0 COMMENT '市场趋势 -1下跌 0平稳 1上涨',
  `recorded_at` timestamp NOT NULL COMMENT '记录时间',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_product_time` (`product_id`, `recorded_at`),
  KEY `idx_station_time` (`station_id`, `recorded_at`)
) ENGINE=InnoDB COMMENT='价格历史表';
```

#### 1.3 价格预警表 (price_alerts)
```sql
CREATE TABLE `price_alerts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `alert_type` tinyint(1) NOT NULL COMMENT '预警类型 1-价格上涨 2-价格下跌 3-达到目标价',
  `target_price` decimal(10,2) NOT NULL COMMENT '目标价格',
  `current_price` decimal(10,2) DEFAULT NULL COMMENT '触发时价格',
  `threshold_rate` decimal(5,2) DEFAULT 5.00 COMMENT '阈值百分比',
  `is_triggered` tinyint(1) DEFAULT 0 COMMENT '是否已触发',
  `triggered_at` timestamp NULL COMMENT '触发时间',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态 0-停用 1-启用',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_product` (`user_id`, `product_id`),
  KEY `idx_status_triggered` (`status`, `is_triggered`)
) ENGINE=InnoDB COMMENT='价格预警表';
```

### 2. 核心价格接口设计

#### 2.1 获取实时价格行情
**接口**: `GET /api/prices/realtime`

**查询参数**:
```json
{
  "productIds": [1, 2, 3],
  "stationIds": [1, 2],
  "priceType": 1,
  "city": "北京市",
  "qualityGrade": "A级",
  "includeHistory": true,
  "historyDays": 7
}
```

**响应数据**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "updateTime": "2024-01-15T14:30:00.000Z",
    "marketStatus": "active",
    "prices": [
      {
        "productId": 1,
        "productName": "iPhone 13 Pro",
        "category": "手机数码",
        "currentPrice": 3750.00,
        "minPrice": 3000.00,
        "maxPrice": 4500.00,
        "unit": "台",
        "priceType": 1,
        "qualityGrade": "8成新以上",
        "changeRate": 5.2,
        "changeAmount": 185.00,
        "trend": "up",
        "confidenceScore": 0.95,
        "lastUpdated": "2024-01-15T14:25:00.000Z",
        "stationPrices": [
          {
            "stationId": 1,
            "stationName": "绿色回收站",
            "price": 3800.00,
            "distance": 0.5,
            "updateTime": "2024-01-15T14:20:00.000Z"
          }
        ],
        "priceHistory": [
          {
            "date": "2024-01-15",
            "price": 3750.00,
            "changeRate": 5.2,
            "volume": 156
          },
          {
            "date": "2024-01-14",
            "price": 3565.00,
            "changeRate": -2.1,
            "volume": 89
          }
        ],
        "marketFactors": [
          {
            "factor": "新品发布",
            "impact": "positive",
            "description": "iPhone 15发布，旧款价格上涨"
          }
        ]
      }
    ]
  }
}
```

#### 2.2 价格趋势分析
**接口**: `GET /api/prices/trends`

**查询参数**:
```json
{
  "productId": 1,
  "timeRange": "30d",
  "granularity": "daily",
  "includeVolume": true,
  "includePrediction": true,
  "city": "北京市"
}
```

**响应数据**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "productInfo": {
      "id": 1,
      "name": "iPhone 13 Pro",
      "category": "手机数码"
    },
    "timeRange": {
      "start": "2023-12-16",
      "end": "2024-01-15",
      "granularity": "daily"
    },
    "currentPrice": {
      "price": 3750.00,
      "changeRate": 5.2,
      "trend": "up"
    },
    "statistics": {
      "avgPrice": 3580.50,
      "maxPrice": 3850.00,
      "minPrice": 3200.00,
      "volatility": 12.5,
      "totalVolume": 2456
    },
    "trendData": [
      {
        "date": "2024-01-15",
        "price": 3750.00,
        "volume": 156,
        "changeRate": 5.2,
        "marketEvents": ["新品发布影响"]
      }
    ],
    "prediction": {
      "nextWeek": {
        "predictedPrice": 3820.00,
        "confidence": 0.78,
        "trend": "up",
        "factors": ["市场需求增加", "供应量减少"]
      },
      "nextMonth": {
        "predictedPrice": 3650.00,
        "confidence": 0.65,
        "trend": "down",
        "factors": ["新品上市冲击"]
      }
    },
    "marketAnalysis": {
      "sentiment": "bullish",
      "riskLevel": "medium",
      "recommendation": "适合出售",
      "reasoning": "价格处于上升通道，建议近期出售"
    }
  }
}
```

#### 2.3 价格预警管理
**接口**: `POST /api/prices/alerts`

**请求参数**:
```json
{
  "productId": 1,
  "alertType": 1,
  "targetPrice": 4000.00,
  "thresholdRate": 5.0,
  "notificationMethods": ["push", "sms"],
  "validUntil": "2024-02-15T00:00:00.000Z"
}
```

#### 2.4 批量价格更新 (内部接口)
**接口**: `POST /api/prices/batch-update`

**请求参数**:
```json
{
  "source": "market_crawler",
  "timestamp": "2024-01-15T14:30:00.000Z",
  "prices": [
    {
      "productId": 1,
      "stationId": 1,
      "priceType": 1,
      "price": 3750.00,
      "volume": 10,
      "qualityGrade": "A级",
      "changeReason": "市场需求增加"
    }
  ]
}
```

### 3. 高级价格功能

#### 3.1 价格比较接口
**接口**: `GET /api/prices/compare`

**查询参数**:
```json
{
  "productIds": [1, 2, 3],
  "location": {
    "lng": 116.4074,
    "lat": 39.9042,
    "radius": 10
  },
  "sortBy": "price_desc",
  "includeDistance": true
}
```

#### 3.2 价格波动提醒
**接口**: `GET /api/prices/volatility-alerts`

**响应数据**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "highVolatility": [
      {
        "productId": 2,
        "productName": "RTX 3080显卡",
        "volatilityRate": 25.8,
        "priceRange": "2000-3500",
        "reason": "挖矿需求波动",
        "recommendation": "观望"
      }
    ],
    "priceSpikes": [
      {
        "productId": 1,
        "productName": "iPhone 13 Pro",
        "spikeRate": 15.2,
        "duration": "3天",
        "cause": "供应短缺"
      }
    ]
  }
}
```

#### 3.3 市场热度分析
**接口**: `GET /api/market/heat-analysis`

**响应数据**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "hotProducts": [
      {
        "productId": 1,
        "productName": "iPhone 13 Pro",
        "heatScore": 95.6,
        "searchVolume": 12580,
        "priceInquiries": 3456,
        "transactionVolume": 156,
        "trendDirection": "up"
      }
    ],
    "emergingTrends": [
      {
        "category": "新能源电池",
        "growthRate": 45.2,
        "description": "锂电池回收需求激增"
      }
    ],
    "marketSentiment": {
      "overall": "positive",
      "confidence": 0.82,
      "factors": ["政策支持", "环保意识提升"]
    }
  }
}
```

### 4. 价格数据同步策略

#### 4.1 数据源管理
```json
{
  "dataSources": [
    {
      "id": "official_market",
      "name": "官方市场价",
      "weight": 0.4,
      "updateFrequency": "1h",
      "reliability": 0.95
    },
    {
      "id": "station_reports",
      "name": "站点上报价",
      "weight": 0.3,
      "updateFrequency": "real-time",
      "reliability": 0.85
    },
    {
      "id": "user_feedback",
      "name": "用户反馈价",
      "weight": 0.2,
      "updateFrequency": "real-time",
      "reliability": 0.70
    },
    {
      "id": "crawler_data",
      "name": "爬虫数据",
      "weight": 0.1,
      "updateFrequency": "4h",
      "reliability": 0.60
    }
  ]
}
```

#### 4.2 价格计算算法
```javascript
// 加权平均价格计算
function calculateWeightedPrice(priceSources) {
  let totalWeight = 0;
  let weightedSum = 0;
  
  priceSources.forEach(source => {
    const weight = source.weight * source.reliability;
    weightedSum += source.price * weight;
    totalWeight += weight;
  });
  
  return {
    price: weightedSum / totalWeight,
    confidence: totalWeight / priceSources.length
  };
}

// 价格异常检测
function detectPriceAnomaly(currentPrice, historicalPrices) {
  const avgPrice = historicalPrices.reduce((sum, p) => sum + p, 0) / historicalPrices.length;
  const deviation = Math.abs(currentPrice - avgPrice) / avgPrice;
  
  return {
    isAnomaly: deviation > 0.3, // 30%以上偏差视为异常
    deviationRate: deviation,
    confidence: 1 - deviation
  };
}
```

### 5. 实时推送机制

#### 5.1 WebSocket价格推送
**连接**: `ws://api.manmanrecycle.com/ws/prices`

**订阅消息**:
```json
{
  "action": "subscribe",
  "channels": [
    "product.1.price",
    "category.6.trend",
    "market.alerts"
  ],
  "token": "JWT_TOKEN"
}
```

**推送消息格式**:
```json
{
  "channel": "product.1.price",
  "type": "price_update",
  "data": {
    "productId": 1,
    "newPrice": 3750.00,
    "oldPrice": 3565.00,
    "changeRate": 5.2,
    "timestamp": "2024-01-15T14:30:00.000Z"
  }
}
```

#### 5.2 价格变动通知
```json
{
  "channel": "market.alerts",
  "type": "price_alert",
  "data": {
    "alertId": 123,
    "userId": 1,
    "productId": 1,
    "alertType": "price_spike",
    "message": "iPhone 13 Pro价格上涨5.2%，当前价格3750元",
    "actionRequired": true,
    "recommendations": [
      "现在是出售的好时机",
      "预计价格将继续上涨"
    ]
  }
}
```

### 6. 缓存和性能优化

#### 6.1 Redis缓存策略
```javascript
// 价格缓存键设计
const CACHE_KEYS = {
  REAL_TIME_PRICE: 'price:realtime:{productId}:{stationId}',
  PRICE_TREND: 'price:trend:{productId}:{timeRange}',
  HOT_PRODUCTS: 'market:hot:products:{city}',
  MARKET_ANALYSIS: 'market:analysis:{date}'
};

// 缓存过期时间
const CACHE_TTL = {
  REAL_TIME_PRICE: 300, // 5分钟
  PRICE_TREND: 3600,    // 1小时
  HOT_PRODUCTS: 1800,   // 30分钟
  MARKET_ANALYSIS: 7200 // 2小时
};
```

#### 6.2 数据库查询优化
```sql
-- 价格查询索引优化
CREATE INDEX idx_realtime_product_updated ON real_time_prices(product_id, last_updated DESC);
CREATE INDEX idx_history_product_time ON price_history(product_id, recorded_at DESC);

-- 分区表设计（按月分区）
ALTER TABLE price_history PARTITION BY RANGE (YEAR(recorded_at) * 100 + MONTH(recorded_at)) (
  PARTITION p202401 VALUES LESS THAN (202402),
  PARTITION p202402 VALUES LESS THAN (202403),
  -- ...
);
```

这套深度设计的价格行情系统能够：
- 🔄 **实时更新**：多数据源实时价格同步
- 📈 **趋势分析**：智能价格预测和市场分析
- ⚡ **快速响应**：毫秒级价格查询
- 🎯 **精准预警**：个性化价格提醒
- 📊 **数据洞察**：深度市场分析和建议
