package com.c2crestore.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 产品详情VO
 * 
 * <AUTHOR> Restore Team
 * @since 2024-01-15
 */
@Data
@ApiModel("产品详情信息")
public class ProductDetailVO {

    @ApiModelProperty("产品ID")
    private Long id;

    @ApiModelProperty("分类ID")
    private Long categoryId;

    @ApiModelProperty("分类名称")
    private String categoryName;

    @ApiModelProperty("分类图标")
    private String categoryIcon;

    @ApiModelProperty("产品名称")
    private String name;

    @ApiModelProperty("品牌")
    private String brand;

    @ApiModelProperty("型号")
    private String model;

    @ApiModelProperty("图片")
    private String image;

    @ApiModelProperty("关键词")
    private String keywords;

    @ApiModelProperty("状态")
    private Integer status;

    @ApiModelProperty("是否已收藏")
    private Boolean isFavorited;

    @ApiModelProperty("收藏数量")
    private Integer favoriteCount;

    @ApiModelProperty("价格信息")
    private PriceInfoVO priceInfo;

    @ApiModelProperty("站点价格列表")
    private List<StationPriceVO> stationPrices;

    @ApiModelProperty("创建时间")
    private LocalDateTime createdAt;

    /**
     * 价格信息VO
     */
    @Data
    @ApiModel("价格信息")
    public static class PriceInfoVO {
        
        @ApiModelProperty("当前最低价")
        private BigDecimal currentMinPrice;
        
        @ApiModelProperty("当前最高价")
        private BigDecimal currentMaxPrice;
        
        @ApiModelProperty("平均价格")
        private BigDecimal avgPrice;
        
        @ApiModelProperty("价格单位")
        private String unit;
        
        @ApiModelProperty("价格更新时间")
        private LocalDateTime updatedAt;
    }

    /**
     * 站点价格VO
     */
    @Data
    @ApiModel("站点价格信息")
    public static class StationPriceVO {
        
        @ApiModelProperty("站点ID")
        private Long stationId;
        
        @ApiModelProperty("站点名称")
        private String stationName;
        
        @ApiModelProperty("站点地址")
        private String stationAddress;
        
        @ApiModelProperty("站点城市")
        private String stationCity;
        
        @ApiModelProperty("最低价")
        private BigDecimal minPrice;
        
        @ApiModelProperty("最高价")
        private BigDecimal maxPrice;
        
        @ApiModelProperty("单位")
        private String unit;
        
        @ApiModelProperty("距离(km)")
        private BigDecimal distance;
        
        @ApiModelProperty("更新时间")
        private LocalDateTime updatedAt;
    }
}
