# 🌿 慢慢回收 - 二手回收小程序项目

## 📱 项目简介

"慢慢回收"是一个专业的废品回收信息服务平台，集成了回收站点查找、智能估价、AI路线规划等功能，为用户提供高效便捷的回收服务体验。

## 🎯 核心特色

### 🤖 AI智能路线规划
- **智能算法**：自动优化多点访问路线
- **个性化推荐**：基于用户行为习惯学习
- **实时优化**：结合交通状况动态调整
- **效率提升**：预计节省30%时间，提升收益

### 🗺️ 地图为核心的设计
- **大面积地图展示**：占据70%屏幕空间
- **智能标记系统**：不同颜色标识站点状态
- **实时信息展示**：距离、营业状态、评分

### 💰 专业估价系统
- **多种识别方式**：手动输入、拍照识别、扫码查询
- **分类估价**：电子产品、金属、纸类、塑料等
- **价格趋势**：实时行情和历史数据分析

## 📂 项目结构

```
二手回收项目/
├── index.html          # 主页面文件
├── css/
│   └── styles.css      # 样式文件
├── js/
│   └── main.js         # 主要JavaScript逻辑
└── README.md           # 项目说明文档
```

## 🎨 设计规范

### 色彩系统
- **主色调**：#52C41A (环保绿)
- **辅助色**：#73D13D (浅绿色)
- **功能色**：#1890FF (信息蓝)、#FAAD14 (警告黄)、#FF4D4F (错误红)

### 字体规范
- **主字体**：-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto
- **字号层级**：12px - 40px，6个层级
- **字重**：400 (常规)、500 (中等)、600 (半粗)、700 (粗体)

### 间距系统
- **基础间距**：8px、12px、16px、24px、32px、48px
- **组件内边距**：15px - 25px
- **组件间距**：15px - 30px

## 📱 页面功能

### 🏠 首页 (地图页)
- **地图展示**：显示附近回收站点
- **快捷功能**：快速查价、AI路线、回收指南
- **站点列表**：附近回收站点详细信息
- **实时定位**：自动获取用户位置

### 📂 分类页
- **搜索功能**：产品名称/型号搜索
- **热门分类**：网格展示热门回收类别
- **完整分类**：所有回收类别列表
- **价格信息**：实时价格范围显示

### 🤖 AI路线规划页
- **智能规划**：AI自动生成最优路线
- **设置选项**：出行方式、优先级设置
- **路线预览**：可视化路线展示
- **详细步骤**：每个站点的详细信息
- **AI建议**：智能优化建议

### 📈 行情页
- **实时行情**：当日价格和趋势
- **价格图表**：可视化价格走势
- **热门产品**：涨跌幅较大的产品
- **详细列表**：各类废品详细价格

### 👤 个人中心页
- **用户信息**：头像、昵称、联系方式
- **成就系统**：环保贡献和徽章
- **服务功能**：收藏、记录、统计等
- **工具帮助**：指南、计算器、客服等

## 🔧 技术特性

### 响应式设计
- **多屏适配**：支持不同尺寸设备
- **触摸优化**：针对移动端优化交互
- **手势支持**：滑动切换、缩放等手势

### 交互动效
- **页面切换**：流畅的页面转场动画
- **卡片交互**：悬停和点击反馈效果
- **加载动画**：数据加载和状态变化动画
- **微交互**：按钮、输入框等细节动效

### 用户体验
- **键盘导航**：支持方向键切换页面
- **触摸手势**：左右滑动切换页面
- **即时反馈**：操作确认和状态提示
- **容错设计**：友好的错误处理

## 🚀 快速开始

### 1. 下载项目
```bash
# 下载项目文件到本地
# 确保包含 index.html, css/styles.css, js/main.js
```

### 2. 本地运行
```bash
# 方法一：直接打开HTML文件
# 双击 index.html 文件在浏览器中打开

# 方法二：使用本地服务器 (推荐)
# 使用 Live Server 或其他本地服务器工具
```

### 3. 浏览器兼容性
- **Chrome** 60+ ✅
- **Firefox** 55+ ✅
- **Safari** 12+ ✅
- **Edge** 79+ ✅

## 📋 功能清单

### ✅ 已实现功能
- [x] 响应式页面布局
- [x] 5个核心页面设计
- [x] 页面切换动画
- [x] 交互式UI组件
- [x] 地图标记动画
- [x] 卡片悬停效果
- [x] 搜索框交互
- [x] 键盘和触摸导航
- [x] 提示消息系统
- [x] 数据加载动画

### 🔄 待扩展功能
- [ ] 真实地图集成 (百度地图/高德地图)
- [ ] 后端API接口对接
- [ ] 用户登录注册系统
- [ ] 实时数据更新
- [ ] 推送通知功能
- [ ] 离线缓存支持
- [ ] 多语言支持
- [ ] 深色模式主题

## 🎨 设计亮点

### 环保主题
- **绿色配色**：体现环保理念
- **自然元素**：使用植物和回收图标
- **可持续性**：倡导绿色生活方式

### 现代设计
- **扁平化风格**：简洁现代的视觉效果
- **卡片式布局**：信息层次清晰
- **圆角设计**：友好亲和的视觉感受

### 用户体验
- **直观导航**：清晰的信息架构
- **快速操作**：减少用户操作步骤
- **即时反馈**：及时的状态提示

## 📞 联系方式

如有问题或建议，欢迎联系：

- **项目名称**：慢慢回收
- **设计理念**：环保 + 智能 + 便民
- **技术栈**：HTML5 + CSS3 + JavaScript (ES6+)

## 📄 许可证

本项目仅用于展示和学习目的。

---

**🌿 让回收变得更简单，让环保成为习惯！**
