.userBox {
    width: 980px;
    min-height: 700px;
    margin: 0 auto;
    background-color: #fff;
    border: 1px solid #d3d3d3;
    border-width: 0 1px;
    padding: 40px;
}

.userBox .userInfo {
    display: flex;
}
.userBox .userInfo .userPic {
    margin-right: 40px;
}
.userBox .userInfo .userPic img {
    padding: 3px;
    background: #fff;
    border: 1px solid #d5d5d5;
}
.userBox .userInfo .userDetail {
    padding-top: 10px;
    width: 100%;
    height: 52px;
    padding-bottom: 12px;
    margin-bottom: 10px;
    border-bottom: 1px solid #ddd;
}

.userBox .userInfo .userDetail .userNicknameBox {
    display: flex;
}

.userBox .userInfo .userDetail .userNicknameBox .userNickname {
    font-size: 22px;
    font-weight: normal;
    line-height: 30px;
    margin-right: 5px;
} 

.userBox .userInfo .userDetail .userNicknameBox .userVip {
    display: flex;
    margin: 3px 5px 0 10px;
    display: inline-block;
    height: 19px;
    overflow: hidden;
    padding-left: 29px;
    line-height: 21px;
    color: #e03a24;
    font-size: 14px;
    font-weight: bold;
    font-style: italic;
    background: url(../../static/images/icon2.png) -135px -190px;
    vertical-align: middle;
}

.userBox .userInfo .userDetail .userNicknameBox .userVip i {
    margin-left: 1px;
    width: 9px;
    height: 19px;
    background: url(../../static/images/icon2.png) -191px -192px;
    display: inline-block;
    overflow: hidden;
    vertical-align: middle;
}

.userBox .userInfo .userDetail .userNicknameBox .userGender {
    font-size: 16px;
    color: #e60026;
  }
.userBox .userInfo .userDetail .userNicknameBox .userGender .man {
    color: #26a6e4;
}
.userBox .userInfo .userDetail .userDynamic {
    display: flex;
    margin-top: 18px;
}
.userBox .userInfo .userDetail .userDynamic .dynamicItem {
    padding: 0 40px 0 20px;
}
.userBox .userInfo .userDetail .userDynamic .dynamicItem .dynamicCount {
    display: block;
    margin-top: -4px;
    font-size: 24px;
    font-weight: normal;
    cursor: pointer;
    padding-left: 6px;
}
.userBox .userInfo .userDetail .userDynamic .dynamicItem  span {
    display: block;
    text-indent: 2px;
    cursor: pointer;
}
.userBox .userInfo .userDetail .userRecommend {
    margin: 15px 0;
}

.userBox .userSongListBox {
    margin-top: 25px;
    height: auto;
}
.userBox .userSongListBox .userSongList {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
}

.userBox .userSongListBox .userSongList a {
    margin-right: 38px;
}
.userBox .userSongListBox .userSongListCreator:hover {
    text-decoration: underline;
    cursor: pointer;
}
