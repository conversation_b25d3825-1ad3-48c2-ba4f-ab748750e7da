import { Comment, Avatar, Form, Button, Input, message } from 'antd'
import React from 'react'

const { TextArea } = Input

const Editor = ({ onChange, onSubmit, submitting, value, onFocus }: any) => (
  <>
    <Form.Item>
      <TextArea rows={4} onChange={onChange} value={value} onFocus={(e) => onFocus(e)} />
    </Form.Item>
    <Form.Item>
      <Button
        htmlType="submit"
        loading={submitting}
        onClick={onSubmit}
        type="primary"
      >
        评论
      </Button>
    </Form.Item>
  </>
)

export default class ThemeComment extends React.Component<any, any> {
  constructor(props: any) {
    super(props)
    this.state = {
      comments: [],
      submitting: false,
      value: '',
      avatar: props.photo
        ? props.photo
        : 'https://imgsa.baidu.com/forum/w%3D580/sign=cf0406cc95504fc2a25fb00dd5dce7f0/18d33c46f21fbe098b4949d065600c338644adfc.jpg',
    }
  }

  handleSubmit = () => {
    if (!this.state.value) {
      message.warning('请输入评论内容')
      return
    }

    this.props.callbackOk(this.state.value)
  }

  handleChange = (e: any) => {
    this.setState({
      value: e.target.value,
    })
  }

  handleFocus = (e: any) => {
    !this.props.isLogin && e.target.blur()
    this.props.onFocus(this.state.value)
  }


  render() {
    const { submitting, value, avatar }: any = this.state
    return (
      <>
        <Comment
          avatar={<Avatar src={avatar} alt="KK" />}
          content={
            <Editor
              onChange={this.handleChange}
              onSubmit={this.handleSubmit}
              submitting={submitting}
              value={value}
              onFocus={(e: any) => this.handleFocus(e)}
            />
          }
        />
      </>
    )
  }
}
