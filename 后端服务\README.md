# 🌿 慢慢回收小程序后端API服务

## 📋 项目简介

慢慢回收是一个专业的废品回收价格查询平台，为用户提供实时的回收价格信息、附近回收站点查询、AI智能路线规划等服务。

### 🎯 核心功能
- 💰 **实时价格查询** - 提供各类废品的实时回收价格
- 🗺️ **站点地图服务** - 查找附近的回收站点
- 🔍 **智能搜索** - 支持产品和站点的智能搜索
- 👤 **用户管理** - 完整的用户认证和信息管理
- 📊 **数据统计** - 用户行为分析和价格趋势

### 🛠️ 技术栈
- **运行环境**: Node.js 16+
- **Web框架**: Express.js
- **数据库**: MySQL 8.0
- **认证**: JWT
- **验证**: Joi
- **日志**: Winston
- **测试**: Jest

## 🚀 快速开始

### 1. 环境准备

#### 安装Node.js
```bash
# 确保Node.js版本 >= 16.0.0
node --version
npm --version
```

#### 安装MySQL
```bash
# 确保MySQL版本 >= 8.0
mysql --version
```

### 2. 项目安装

```bash
# 克隆项目
git clone https://github.com/your-org/manman-recycle-api.git
cd manman-recycle-api

# 安装依赖
npm install
```

### 3. 环境配置

```bash
# 复制环境变量文件
cp .env.example .env

# 编辑环境变量
vim .env
```

**必需的环境变量**:
```bash
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=recycle_db

# JWT密钥
JWT_SECRET=your_super_secret_jwt_key_change_in_production

# 微信小程序配置
WECHAT_APP_ID=your_wechat_app_id
WECHAT_APP_SECRET=your_wechat_app_secret
```

### 4. 数据库初始化

```bash
# 创建数据库
mysql -u root -p -e "CREATE DATABASE recycle_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

# 导入数据表结构
mysql -u root -p recycle_db < ../数据库设计/简化版数据表设计.sql

# 导入示例数据
mysql -u root -p recycle_db < ../数据库设计/简化版示例数据.sql
```

### 5. 启动服务

```bash
# 开发环境启动
npm run dev

# 生产环境启动
npm start
```

服务启动后访问: http://localhost:3000

## 📖 API文档

### 接口基础信息
- **Base URL**: `http://localhost:3000/api`
- **认证方式**: Bearer Token
- **数据格式**: JSON

### 快速测试

#### 1. 健康检查
```bash
curl http://localhost:3000/health
```

#### 2. 获取产品分类
```bash
curl http://localhost:3000/api/categories
```

#### 3. 搜索附近站点
```bash
curl "http://localhost:3000/api/stations/nearby?lng=116.4074&lat=39.9042&radius=5"
```

### 完整API文档
详细的API接口文档请查看: [API接口文档.md](./API接口文档.md)

## 🏗️ 项目结构

```
后端服务/
├── app.js                    # 应用入口
├── package.json              # 项目配置
├── .env.example             # 环境变量示例
├── config/
│   └── database.js          # 数据库配置
├── middleware/              # 中间件
│   ├── auth.js              # 认证中间件
│   ├── errorHandler.js      # 错误处理
│   └── validation.js        # 参数验证
├── utils/                   # 工具类
│   ├── logger.js            # 日志工具
│   └── response.js          # 响应工具
├── routes/                  # 路由模块
│   ├── auth.js              # 认证路由
│   ├── user.js              # 用户路由
│   ├── stations.js          # 站点路由
│   ├── products.js          # 产品路由
│   └── ...                  # 其他路由
├── services/                # 业务服务
├── models/                  # 数据模型
├── tests/                   # 测试文件
└── logs/                    # 日志文件
```

## 🧪 测试

### 运行测试
```bash
# 运行所有测试
npm test

# 运行特定测试
npm test -- auth.test.js

# 生成测试覆盖率报告
npm run test:coverage
```

### 测试用例
- 认证模块测试
- 用户模块测试
- 站点模块测试
- 产品模块测试
- 接口集成测试

## 📊 监控和日志

### 日志系统
- **日志级别**: error, warn, info, debug
- **日志文件**: `./logs/combined.log`
- **错误日志**: `./logs/error.log`

### 查看日志
```bash
# 查看实时日志
tail -f logs/combined.log

# 查看错误日志
tail -f logs/error.log
```

### 性能监控
- 接口响应时间监控
- 数据库连接池监控
- 内存使用监控
- 错误率统计

## 🔧 开发指南

### 代码规范
- 使用ESLint进行代码检查
- 遵循RESTful API设计规范
- 统一的错误处理和响应格式
- 完善的注释和文档

### 添加新接口
1. 在对应的路由文件中添加路由
2. 实现业务逻辑
3. 添加参数验证
4. 编写测试用例
5. 更新API文档

### 数据库操作
```javascript
// 查询示例
const users = await db.query('SELECT * FROM users WHERE status = ?', [1]);

// 插入示例
const userId = await db.insert('users', { nickname: '用户', openid: 'wx123' });

// 更新示例
await db.update('users', { nickname: '新昵称' }, 'id = ?', [userId]);

// 分页查询示例
const result = await db.paginate('SELECT * FROM users', [], 1, 20);
```

## 🚀 部署

### Docker部署
```bash
# 构建镜像
docker build -t manman-recycle-api .

# 运行容器
docker run -d -p 3000:3000 --name recycle-api manman-recycle-api
```

### PM2部署
```bash
# 安装PM2
npm install -g pm2

# 启动应用
pm2 start app.js --name recycle-api

# 查看状态
pm2 status

# 查看日志
pm2 logs recycle-api
```

### Nginx配置
```nginx
server {
    listen 80;
    server_name api.manmanrecycle.com;
    
    location / {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
}
```

## 🔒 安全

### 安全措施
- JWT Token认证
- 接口限流保护
- SQL注入防护
- XSS攻击防护
- CORS跨域配置
- 敏感信息加密

### 安全配置
```javascript
// 限流配置
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 100 // 最多100个请求
});

// CORS配置
app.use(cors({
  origin: ['https://yourdomain.com'],
  credentials: true
}));
```

## 🤝 贡献指南

### 提交代码
1. Fork项目
2. 创建功能分支
3. 提交代码
4. 创建Pull Request

### 代码审查
- 代码风格检查
- 功能测试验证
- 性能影响评估
- 安全风险评估

## 📞 支持

### 问题反馈
- GitHub Issues: [项目Issues](https://github.com/your-org/manman-recycle-api/issues)
- 邮箱: <EMAIL>

### 技术支持
- 开发文档: [Wiki](https://github.com/your-org/manman-recycle-api/wiki)
- API文档: [接口文档](./API接口文档.md)
- 常见问题: [FAQ](./FAQ.md)

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

---

**🌿 慢慢回收 - 让废品回收更简单、更环保！**
