import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { 
  Row, 
  Col, 
  Card, 
  Button, 
  Space, 
  Statistic, 
  List,
  Avatar,
  Typography,
  Spin
} from 'antd';
import {
  ShoppingOutlined,
  EnvironmentOutlined,
  StarOutlined,
  RightOutlined
} from '@ant-design/icons';
import { fetchCategories, fetchHotProducts } from '../store/actions/productActions';

const { Title, Paragraph } = Typography;

const Home = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  
  const { 
    categories, 
    categoriesLoading,
    hotProducts, 
    hotProductsLoading 
  } = useSelector(state => state.product);

  useEffect(() => {
    // 获取分类和热门产品
    dispatch(fetchCategories());
    dispatch(fetchHotProducts({ limit: 6 }));
  }, [dispatch]);

  // 快捷功能
  const quickActions = [
    {
      title: '产品搜索',
      description: '查找回收产品和价格',
      icon: <ShoppingOutlined />,
      color: '#1890ff',
      path: '/products'
    },
    {
      title: '附近站点',
      description: '查找附近的回收站点',
      icon: <EnvironmentOutlined />,
      color: '#52c41a',
      path: '/stations'
    },
    {
      title: '我的收藏',
      description: '查看收藏的产品和站点',
      icon: <StarOutlined />,
      color: '#faad14',
      path: '/profile'
    }
  ];

  return (
    <div className="home-page">
      {/* 欢迎横幅 */}
      <Card className="welcome-banner" style={{ marginBottom: 24 }}>
        <Row gutter={[24, 24]} align="middle">
          <Col xs={24} md={16}>
            <Title level={2} style={{ margin: 0, color: '#1890ff' }}>
              欢迎使用C2B回收平台
            </Title>
            <Paragraph style={{ fontSize: 16, margin: '8px 0 0 0' }}>
              专业的回收服务平台，为您提供便捷的回收解决方案
            </Paragraph>
          </Col>
          <Col xs={24} md={8}>
            <Row gutter={16}>
              <Col span={8}>
                <Statistic title="产品分类" value={categories.length} />
              </Col>
              <Col span={8}>
                <Statistic title="热门产品" value={hotProducts.length} />
              </Col>
              <Col span={8}>
                <Statistic title="服务站点" value="100+" />
              </Col>
            </Row>
          </Col>
        </Row>
      </Card>

      <Row gutter={[24, 24]}>
        {/* 快捷功能 */}
        <Col xs={24} lg={16}>
          <Card title="快捷功能" style={{ marginBottom: 24 }}>
            <Row gutter={[16, 16]}>
              {quickActions.map((action, index) => (
                <Col xs={24} sm={8} key={index}>
                  <Card 
                    hoverable
                    className="quick-action-card"
                    onClick={() => navigate(action.path)}
                    style={{ textAlign: 'center', height: '100%' }}
                  >
                    <div style={{ fontSize: 32, color: action.color, marginBottom: 16 }}>
                      {action.icon}
                    </div>
                    <Title level={4} style={{ margin: '0 0 8px 0' }}>
                      {action.title}
                    </Title>
                    <Paragraph style={{ margin: 0, color: '#666' }}>
                      {action.description}
                    </Paragraph>
                  </Card>
                </Col>
              ))}
            </Row>
          </Card>

          {/* 产品分类 */}
          <Card 
            title="产品分类" 
            extra={
              <Button 
                type="link" 
                icon={<RightOutlined />}
                onClick={() => navigate('/products')}
              >
                查看更多
              </Button>
            }
          >
            {categoriesLoading ? (
              <div style={{ textAlign: 'center', padding: 40 }}>
                <Spin size="large" />
              </div>
            ) : (
              <Row gutter={[16, 16]}>
                {categories.slice(0, 8).map(category => (
                  <Col xs={12} sm={8} md={6} key={category.id}>
                    <Card 
                      hoverable
                      size="small"
                      style={{ textAlign: 'center' }}
                      onClick={() => navigate(`/products?categoryId=${category.id}`)}
                    >
                      <div style={{ fontSize: 24, marginBottom: 8 }}>
                        {category.icon || '📦'}
                      </div>
                      <div>{category.name}</div>
                    </Card>
                  </Col>
                ))}
              </Row>
            )}
          </Card>
        </Col>

        {/* 热门产品 */}
        <Col xs={24} lg={8}>
          <Card 
            title="热门产品" 
            extra={
              <Button 
                type="link" 
                icon={<RightOutlined />}
                onClick={() => navigate('/products')}
              >
                查看更多
              </Button>
            }
          >
            {hotProductsLoading ? (
              <div style={{ textAlign: 'center', padding: 40 }}>
                <Spin size="large" />
              </div>
            ) : (
              <List
                dataSource={hotProducts}
                renderItem={product => (
                  <List.Item
                    style={{ cursor: 'pointer' }}
                    onClick={() => navigate(`/products/${product.id}`)}
                  >
                    <List.Item.Meta
                      avatar={
                        <Avatar 
                          src={product.image} 
                          icon={<ShoppingOutlined />}
                          size={40}
                        />
                      }
                      title={product.name}
                      description={
                        <Space>
                          <span style={{ color: '#f50' }}>
                            ¥{product.minPrice}-{product.maxPrice}
                          </span>
                          <span style={{ color: '#999' }}>
                            /{product.unit}
                          </span>
                        </Space>
                      }
                    />
                  </List.Item>
                )}
              />
            )}
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Home;
