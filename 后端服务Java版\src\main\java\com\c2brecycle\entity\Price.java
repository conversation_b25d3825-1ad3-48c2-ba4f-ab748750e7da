package com.c2brecycle.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 价格实体类
 * 
 * <AUTHOR> Recycle Team
 * @since 2024-01-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("prices")
public class Price implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 价格ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 产品ID
     */
    @TableField("product_id")
    private Long productId;

    /**
     * 站点ID
     */
    @TableField("station_id")
    private Long stationId;

    /**
     * 价格类型 1-收购价 2-零售价 3-批发价
     */
    @TableField("price_type")
    private Integer priceType;

    /**
     * 价格
     */
    @TableField("price")
    private BigDecimal price;

    /**
     * 质量等级
     */
    @TableField("quality_grade")
    private String qualityGrade;

    /**
     * 最小数量
     */
    @TableField("min_quantity")
    private BigDecimal minQuantity;

    /**
     * 最大数量
     */
    @TableField("max_quantity")
    private BigDecimal maxQuantity;

    /**
     * 单位
     */
    @TableField("unit")
    private String unit;

    /**
     * 状态 0-无效 1-有效
     */
    @TableField("status")
    private Integer status;

    /**
     * 生效时间
     */
    @TableField("effective_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime effectiveTime;

    /**
     * 失效时间
     */
    @TableField("expire_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime expireTime;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    /**
     * 逻辑删除标记
     */
    @TableLogic
    @TableField("deleted")
    private Integer deleted;
}
