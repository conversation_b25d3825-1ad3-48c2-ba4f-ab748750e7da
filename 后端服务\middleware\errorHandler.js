const logger = require('../utils/logger');

/**
 * 业务错误类
 */
class BusinessError extends Error {
  constructor(message, code = 500, data = null) {
    super(message);
    this.name = 'BusinessError';
    this.code = code;
    this.data = data;
  }
}

/**
 * 验证错误类
 */
class ValidationError extends Error {
  constructor(message, details = []) {
    super(message);
    this.name = 'ValidationError';
    this.code = 400;
    this.details = details;
  }
}

/**
 * 认证错误类
 */
class AuthError extends Error {
  constructor(message = '认证失败') {
    super(message);
    this.name = 'AuthError';
    this.code = 401;
  }
}

/**
 * 权限错误类
 */
class PermissionError extends Error {
  constructor(message = '权限不足') {
    super(message);
    this.name = 'PermissionError';
    this.code = 403;
  }
}

/**
 * 资源不存在错误类
 */
class NotFoundError extends Error {
  constructor(message = '资源不存在') {
    super(message);
    this.name = 'NotFoundError';
    this.code = 404;
  }
}

/**
 * 全局错误处理中间件
 */
const errorHandler = (err, req, res, next) => {
  let code = 500;
  let message = '服务器内部错误';
  let data = null;

  // 记录错误日志
  logger.error('API错误:', {
    error: err.message,
    stack: err.stack,
    url: req.url,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent')
  });

  // 根据错误类型设置响应
  if (err instanceof BusinessError) {
    code = err.code;
    message = err.message;
    data = err.data;
  } else if (err instanceof ValidationError) {
    code = 400;
    message = err.message;
    data = err.details;
  } else if (err instanceof AuthError) {
    code = 401;
    message = err.message;
  } else if (err instanceof PermissionError) {
    code = 403;
    message = err.message;
  } else if (err instanceof NotFoundError) {
    code = 404;
    message = err.message;
  } else if (err.name === 'ValidationError') {
    // Joi验证错误
    code = 400;
    message = '请求参数验证失败';
    data = err.details?.map(detail => ({
      field: detail.path?.join('.'),
      message: detail.message
    }));
  } else if (err.name === 'JsonWebTokenError') {
    code = 401;
    message = 'Token无效';
  } else if (err.name === 'TokenExpiredError') {
    code = 401;
    message = 'Token已过期';
  } else if (err.name === 'SyntaxError' && err.status === 400) {
    code = 400;
    message = '请求体格式错误';
  } else if (err.code === 'LIMIT_FILE_SIZE') {
    code = 413;
    message = '文件大小超出限制';
  } else if (err.code === 'LIMIT_FILE_COUNT') {
    code = 413;
    message = '文件数量超出限制';
  } else if (err.code === 'ECONNREFUSED') {
    code = 503;
    message = '服务暂时不可用';
  } else if (err.code === 'ER_DUP_ENTRY') {
    code = 409;
    message = '数据重复';
  }

  // 生产环境不返回详细错误信息
  if (process.env.NODE_ENV === 'production' && code === 500) {
    message = '服务器内部错误';
    data = null;
  }

  res.status(code).json({
    code,
    message,
    data,
    timestamp: new Date().toISOString(),
    path: req.url
  });
};

/**
 * 404处理中间件
 */
const notFoundHandler = (req, res) => {
  res.status(404).json({
    code: 404,
    message: `接口 ${req.method} ${req.url} 不存在`,
    timestamp: new Date().toISOString(),
    path: req.url
  });
};

/**
 * 异步错误包装器
 */
const asyncHandler = (fn) => {
  return (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

module.exports = {
  BusinessError,
  ValidationError,
  AuthError,
  PermissionError,
  NotFoundError,
  errorHandler,
  notFoundHandler,
  asyncHandler
};
