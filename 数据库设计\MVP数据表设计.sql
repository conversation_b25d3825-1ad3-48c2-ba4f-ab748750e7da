-- =============================================
-- 慢慢回收小程序 MVP版本数据库设计
-- 版本: V1.0
-- 创建日期: 2024-01-15
-- 说明: 包含核心功能的最小可行产品数据表
-- =============================================

-- 设置字符集
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- =============================================
-- 1. 用户相关表
-- =============================================

-- 用户基础信息表
CREATE TABLE `users` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `openid` varchar(64) NOT NULL COMMENT '微信openid',
  `unionid` varchar(64) DEFAULT NULL COMMENT '微信unionid',
  `nickname` varchar(50) DEFAULT NULL COMMENT '用户昵称',
  `avatar_url` varchar(255) DEFAULT NULL COMMENT '头像URL',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `gender` tinyint(1) DEFAULT 0 COMMENT '性别 0-未知 1-男 2-女',
  `city` varchar(50) DEFAULT NULL COMMENT '城市',
  `province` varchar(50) DEFAULT NULL COMMENT '省份',
  `country` varchar(50) DEFAULT NULL COMMENT '国家',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态 0-禁用 1-正常',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_openid` (`openid`),
  KEY `idx_phone` (`phone`),
  KEY `idx_city` (`city`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户基础信息表';

-- 用户地址表
CREATE TABLE `user_addresses` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '地址ID',
  `user_id` bigint(20) unsigned NOT NULL COMMENT '用户ID',
  `name` varchar(50) NOT NULL COMMENT '联系人姓名',
  `phone` varchar(20) NOT NULL COMMENT '联系电话',
  `province` varchar(50) NOT NULL COMMENT '省份',
  `city` varchar(50) NOT NULL COMMENT '城市',
  `district` varchar(50) NOT NULL COMMENT '区县',
  `address` varchar(200) NOT NULL COMMENT '详细地址',
  `longitude` decimal(10,7) DEFAULT NULL COMMENT '经度',
  `latitude` decimal(10,7) DEFAULT NULL COMMENT '纬度',
  `is_default` tinyint(1) DEFAULT 0 COMMENT '是否默认地址 0-否 1-是',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户地址表';

-- =============================================
-- 2. 回收站点相关表
-- =============================================

-- 回收站点表
CREATE TABLE `recycle_stations` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '站点ID',
  `name` varchar(100) NOT NULL COMMENT '站点名称',
  `phone` varchar(20) NOT NULL COMMENT '联系电话',
  `address` varchar(200) NOT NULL COMMENT '详细地址',
  `longitude` decimal(10,7) NOT NULL COMMENT '经度',
  `latitude` decimal(10,7) NOT NULL COMMENT '纬度',
  `province` varchar(50) NOT NULL COMMENT '省份',
  `city` varchar(50) NOT NULL COMMENT '城市',
  `district` varchar(50) NOT NULL COMMENT '区县',
  `business_hours` varchar(100) DEFAULT NULL COMMENT '营业时间',
  `description` text COMMENT '站点描述',
  `images` json DEFAULT NULL COMMENT '站点图片JSON数组',
  `rating` decimal(3,2) DEFAULT 0.00 COMMENT '评分 0-5.00',
  `review_count` int(11) DEFAULT 0 COMMENT '评价数量',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态 0-停业 1-营业 2-休息',
  `features` json DEFAULT NULL COMMENT '服务特色JSON数组',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_location` (`longitude`,`latitude`),
  KEY `idx_city` (`city`),
  KEY `idx_status` (`status`),
  KEY `idx_rating` (`rating`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='回收站点表';

-- 回收站点服务类型表
CREATE TABLE `station_services` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '服务ID',
  `station_id` bigint(20) unsigned NOT NULL COMMENT '站点ID',
  `category_id` bigint(20) unsigned NOT NULL COMMENT '分类ID',
  `min_price` decimal(10,2) DEFAULT NULL COMMENT '最低价格',
  `max_price` decimal(10,2) DEFAULT NULL COMMENT '最高价格',
  `unit` varchar(20) DEFAULT NULL COMMENT '单位 kg/台/个',
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否启用',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_station_id` (`station_id`),
  KEY `idx_category_id` (`category_id`),
  FOREIGN KEY (`station_id`) REFERENCES `recycle_stations` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='回收站点服务类型表';

-- =============================================
-- 3. 产品分类和价格相关表
-- =============================================

-- 回收分类表
CREATE TABLE `recycle_categories` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `parent_id` bigint(20) unsigned DEFAULT 0 COMMENT '父分类ID 0为顶级分类',
  `name` varchar(50) NOT NULL COMMENT '分类名称',
  `icon` varchar(100) DEFAULT NULL COMMENT '分类图标',
  `description` varchar(200) DEFAULT NULL COMMENT '分类描述',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序权重',
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否启用',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='回收分类表';

-- 产品信息表
CREATE TABLE `products` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '产品ID',
  `category_id` bigint(20) unsigned NOT NULL COMMENT '分类ID',
  `name` varchar(100) NOT NULL COMMENT '产品名称',
  `brand` varchar(50) DEFAULT NULL COMMENT '品牌',
  `model` varchar(100) DEFAULT NULL COMMENT '型号',
  `specifications` text COMMENT '规格参数',
  `images` json DEFAULT NULL COMMENT '产品图片JSON数组',
  `keywords` varchar(200) DEFAULT NULL COMMENT '搜索关键词',
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否启用',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_brand` (`brand`),
  KEY `idx_name` (`name`),
  FOREIGN KEY (`category_id`) REFERENCES `recycle_categories` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='产品信息表';

-- 产品价格表
CREATE TABLE `product_prices` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '价格ID',
  `product_id` bigint(20) unsigned NOT NULL COMMENT '产品ID',
  `station_id` bigint(20) unsigned DEFAULT NULL COMMENT '站点ID 为空表示市场均价',
  `min_price` decimal(10,2) NOT NULL COMMENT '最低价格',
  `max_price` decimal(10,2) NOT NULL COMMENT '最高价格',
  `avg_price` decimal(10,2) NOT NULL COMMENT '平均价格',
  `unit` varchar(20) NOT NULL COMMENT '单位 kg/台/个',
  `condition_desc` varchar(100) DEFAULT NULL COMMENT '成色要求',
  `price_date` date NOT NULL COMMENT '价格日期',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_station_id` (`station_id`),
  KEY `idx_price_date` (`price_date`),
  FOREIGN KEY (`product_id`) REFERENCES `products` (`id`),
  FOREIGN KEY (`station_id`) REFERENCES `recycle_stations` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='产品价格表';

-- =============================================
-- 4. 用户行为相关表
-- =============================================

-- 用户搜索记录表
CREATE TABLE `user_search_logs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `user_id` bigint(20) unsigned NOT NULL COMMENT '用户ID',
  `keyword` varchar(100) NOT NULL COMMENT '搜索关键词',
  `search_type` tinyint(1) NOT NULL COMMENT '搜索类型 1-产品 2-站点',
  `result_count` int(11) DEFAULT 0 COMMENT '结果数量',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_keyword` (`keyword`),
  KEY `idx_created_at` (`created_at`),
  FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户搜索记录表';

-- 用户收藏表
CREATE TABLE `user_favorites` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '收藏ID',
  `user_id` bigint(20) unsigned NOT NULL COMMENT '用户ID',
  `target_type` tinyint(1) NOT NULL COMMENT '收藏类型 1-站点 2-产品',
  `target_id` bigint(20) unsigned NOT NULL COMMENT '目标ID',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_target` (`user_id`,`target_type`,`target_id`),
  KEY `idx_user_id` (`user_id`),
  FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户收藏表';

-- 用户浏览记录表
CREATE TABLE `user_browse_logs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `user_id` bigint(20) unsigned NOT NULL COMMENT '用户ID',
  `target_type` tinyint(1) NOT NULL COMMENT '浏览类型 1-站点 2-产品',
  `target_id` bigint(20) unsigned NOT NULL COMMENT '目标ID',
  `duration` int(11) DEFAULT 0 COMMENT '浏览时长(秒)',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_created_at` (`created_at`),
  FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户浏览记录表';

-- =============================================
-- 5. 评价反馈相关表
-- =============================================

-- 站点评价表
CREATE TABLE `station_reviews` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '评价ID',
  `user_id` bigint(20) unsigned NOT NULL COMMENT '用户ID',
  `station_id` bigint(20) unsigned NOT NULL COMMENT '站点ID',
  `rating` tinyint(1) NOT NULL COMMENT '评分 1-5星',
  `content` text COMMENT '评价内容',
  `images` json DEFAULT NULL COMMENT '评价图片JSON数组',
  `service_rating` tinyint(1) DEFAULT NULL COMMENT '服务评分',
  `price_rating` tinyint(1) DEFAULT NULL COMMENT '价格评分',
  `environment_rating` tinyint(1) DEFAULT NULL COMMENT '环境评分',
  `is_anonymous` tinyint(1) DEFAULT 0 COMMENT '是否匿名',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态 0-隐藏 1-显示',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_station_id` (`station_id`),
  KEY `idx_rating` (`rating`),
  KEY `idx_created_at` (`created_at`),
  FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`station_id`) REFERENCES `recycle_stations` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='站点评价表';

-- =============================================
-- 6. 系统配置相关表
-- =============================================

-- 系统配置表
CREATE TABLE `system_configs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `config_key` varchar(100) NOT NULL COMMENT '配置键',
  `config_value` text COMMENT '配置值',
  `config_desc` varchar(200) DEFAULT NULL COMMENT '配置描述',
  `config_type` varchar(50) DEFAULT 'string' COMMENT '配置类型',
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否启用',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_config_key` (`config_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统配置表';

-- =============================================
-- 7. 初始化数据
-- =============================================

-- 插入回收分类初始数据
INSERT INTO `recycle_categories` (`id`, `parent_id`, `name`, `icon`, `description`, `sort_order`) VALUES
(1, 0, '电子产品', '💻', '电脑、手机、数码产品等', 1),
(2, 0, '金属类', '🔩', '废铜、废铝、废铁等', 2),
(3, 0, '纸类', '📄', '报纸、纸箱、书本等', 3),
(4, 0, '塑料类', '🥤', '塑料瓶、塑料袋等', 4),
(5, 1, '电脑配件', '💻', 'CPU、显卡、主板等', 11),
(6, 1, '手机数码', '📱', '手机、平板、耳机等', 12),
(7, 1, '家用电器', '📺', '电视、冰箱、洗衣机等', 13),
(8, 2, '废铜', '🔩', '废铜线、铜管等', 21),
(9, 2, '废铝', '🔩', '铝合金、铝制品等', 22),
(10, 2, '废铁', '🔩', '废钢铁、铁制品等', 23);

-- 插入系统配置初始数据
INSERT INTO `system_configs` (`config_key`, `config_value`, `config_desc`) VALUES
('app_name', '慢慢回收', '应用名称'),
('app_version', '1.0.0', '应用版本'),
('default_city', '北京市', '默认城市'),
('search_radius', '10', '默认搜索半径(公里)'),
('max_route_points', '10', '最大路线点数'),
('price_update_interval', '24', '价格更新间隔(小时)');

SET FOREIGN_KEY_CHECKS = 1;
