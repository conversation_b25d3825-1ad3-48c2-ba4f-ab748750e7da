# 🏗️ C2B回收平台前后端核心链路架构说明

## 📊 **架构图总览**

我已经为您绘制了4个核心架构图，全面展示了C2B回收平台的技术架构和业务流程：

### **1. 系统架构图** 🏗️
- **前端层**: React + Redux生态
- **网络层**: HTTP/HTTPS + RESTful API + JWT
- **后端层**: Spring Boot生态
- **数据层**: MySQL + Redis

### **2. 业务流程时序图** 🔄
- **用户认证流程**: 登录 → Token管理 → 状态更新
- **产品搜索流程**: 搜索 → 查询 → 结果展示
- **附近站点查询**: 位置 → 计算 → 排序展示

### **3. 数据库关系图** 🗄️
- **8张核心表**: 用户、产品、站点、价格等
- **完整关联关系**: 外键约束和业务关联
- **数据完整性**: 逻辑删除和索引优化

### **4. 技术栈架构图** 🛠️
- **分层架构**: 用户层 → 前端层 → 网络层 → 后端层 → 数据层
- **技术选型**: 现代化技术栈
- **监控部署**: 完整的运维体系

## 🔍 **核心链路详解**

### **前端链路 (React生态)**

#### **组件层**
```
用户界面 → React组件 → 状态管理 → API调用
```

#### **状态管理流程**
```
用户操作 → dispatch(action) → reducer → 状态更新 → 界面重渲染
```

#### **API调用链路**
```
组件 → Redux Action → API Service → Axios → HTTP请求
```

### **后端链路 (Spring Boot生态)**

#### **请求处理流程**
```
HTTP请求 → Controller → Service → Mapper → 数据库
```

#### **认证授权链路**
```
JWT Token → 拦截器验证 → 用户身份 → 权限控制
```

#### **数据访问链路**
```
业务逻辑 → MyBatis Mapper → SQL执行 → 结果映射
```

### **数据库链路 (MySQL)**

#### **查询优化链路**
```
业务查询 → 索引匹配 → 执行计划 → 结果返回
```

#### **地理位置计算**
```sql
-- 基于经纬度计算距离的SQL
ROUND(
    6371 * ACOS(
        COS(RADIANS(lat1)) * COS(RADIANS(lat2)) * 
        COS(RADIANS(lng2) - RADIANS(lng1)) + 
        SIN(RADIANS(lat1)) * SIN(RADIANS(lat2))
    ), 2
) AS distance
```

## 🎯 **关键业务流程**

### **1. 用户认证流程**

#### **登录链路**
1. **前端**: 用户输入 → 表单验证 → dispatch(wechatLogin)
2. **Redux**: Action → API调用 → 状态更新
3. **后端**: 接收请求 → 验证用户 → 生成Token
4. **数据库**: 查询用户 → 验证身份 → 返回信息
5. **缓存**: 存储Token → 设置过期时间
6. **响应**: 返回Token → 更新前端状态

#### **Token验证链路**
1. **请求拦截**: 提取Authorization头
2. **Token解析**: JWT解码 → 验证签名
3. **用户识别**: 提取用户ID → 设置请求属性
4. **权限控制**: 检查访问权限 → 放行/拒绝

### **2. 产品搜索流程**

#### **搜索链路**
1. **前端**: 输入关键词 → 构建查询参数
2. **API调用**: GET /products/search?keyword=xxx&categoryId=xxx
3. **后端处理**: 参数验证 → 构建查询条件
4. **数据库查询**: 
   ```sql
   SELECT p.*, c.name as category_name 
   FROM products p 
   LEFT JOIN categories c ON p.category_id = c.id 
   WHERE p.name LIKE '%keyword%' 
   ORDER BY p.search_count DESC
   ```
5. **结果处理**: 分页 → VO转换 → 返回结果
6. **前端展示**: 更新状态 → 渲染列表

### **3. 附近站点查询流程**

#### **地理位置查询链路**
1. **前端**: 获取用户位置 → 构建查询参数
2. **API调用**: GET /stations/nearby?lng=xxx&lat=xxx&radius=5
3. **后端处理**: 参数验证 → 地理位置计算
4. **数据库查询**:
   ```sql
   SELECT *, 
   ROUND(6371 * ACOS(COS(RADIANS(lat)) * COS(RADIANS(s.lat)) * 
   COS(RADIANS(s.lng) - RADIANS(lng)) + 
   SIN(RADIANS(lat)) * SIN(RADIANS(s.lat))), 2) AS distance
   FROM stations s 
   HAVING distance <= radius 
   ORDER BY distance ASC
   ```
5. **结果处理**: 距离排序 → VO转换
6. **前端展示**: 地图标记 → 列表展示

## 🔧 **技术实现细节**

### **前端技术栈**
- **React 18**: 函数组件 + Hooks
- **Redux + Thunk**: 状态管理 + 异步处理
- **React Router v6**: 路由管理
- **Ant Design**: UI组件库
- **Axios**: HTTP客户端
- **响应式设计**: 移动端适配

### **后端技术栈**
- **Spring Boot 2.7**: 应用框架
- **Spring Security**: 安全框架
- **MyBatis Plus**: ORM框架
- **Druid**: 数据库连接池
- **Redis**: 缓存和Session
- **Swagger**: API文档

### **数据库设计**
- **MySQL 8.0**: 主数据库
- **8张核心表**: 完整业务模型
- **索引优化**: 查询性能优化
- **外键约束**: 数据完整性
- **逻辑删除**: 软删除机制

## 📈 **性能优化策略**

### **前端优化**
- **组件懒加载**: React.lazy + Suspense
- **状态管理优化**: 避免不必要的重渲染
- **请求缓存**: Redux状态缓存
- **分页加载**: 大数据集分页处理

### **后端优化**
- **数据库连接池**: Druid连接池管理
- **查询优化**: 索引优化 + SQL优化
- **缓存策略**: Redis热点数据缓存
- **分页查询**: MyBatis Plus分页插件

### **数据库优化**
- **索引设计**: 主键、外键、查询字段索引
- **查询优化**: 避免全表扫描
- **分页优化**: LIMIT + OFFSET优化
- **地理位置优化**: 空间索引（可扩展）

## 🔒 **安全机制**

### **认证安全**
- **JWT Token**: 无状态认证
- **Token刷新**: 自动续期机制
- **权限控制**: 基于角色的访问控制

### **数据安全**
- **SQL注入防护**: MyBatis参数化查询
- **XSS防护**: 前端输入验证
- **CSRF防护**: Token验证

### **传输安全**
- **HTTPS**: 数据传输加密
- **CORS配置**: 跨域安全控制

## 🚀 **部署架构**

### **开发环境**
- **前端**: npm start (localhost:3000)
- **后端**: mvn spring-boot:run (localhost:8080)
- **数据库**: MySQL本地实例

### **生产环境**
- **前端**: Nginx静态文件服务
- **后端**: Spring Boot JAR包部署
- **数据库**: MySQL主从架构
- **缓存**: Redis集群
- **负载均衡**: Nginx反向代理

---

**📌 总结**: 这套架构图完整展示了C2B回收平台的技术架构、业务流程、数据模型和核心链路，为系统的开发、部署和维护提供了清晰的指导。
