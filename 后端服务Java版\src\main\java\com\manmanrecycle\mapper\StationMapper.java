package com.manmanrecycle.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.manmanrecycle.entity.Station;
import com.manmanrecycle.vo.StationVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.util.List;

/**
 * 回收站点Mapper接口
 * 
 * <AUTHOR> Team
 * @since 2024-01-15
 */
@Mapper
public interface StationMapper extends BaseMapper<Station> {

    /**
     * 查询附近站点
     * 
     * @param page 分页参数
     * @param lng 经度
     * @param lat 纬度
     * @param radius 半径(km)
     * @param categoryId 分类ID
     * @param status 状态
     * @return 站点列表
     */
    IPage<StationVO> selectNearbyStations(
            Page<StationVO> page,
            @Param("lng") BigDecimal lng,
            @Param("lat") BigDecimal lat,
            @Param("radius") BigDecimal radius,
            @Param("categoryId") Long categoryId,
            @Param("status") Integer status
    );

    /**
     * 搜索站点
     * 
     * @param page 分页参数
     * @param keyword 关键词
     * @param city 城市
     * @param categoryId 分类ID
     * @return 站点列表
     */
    IPage<StationVO> searchStations(
            Page<StationVO> page,
            @Param("keyword") String keyword,
            @Param("city") String city,
            @Param("categoryId") Long categoryId
    );

    /**
     * 获取站点详情
     * 
     * @param stationId 站点ID
     * @return 站点详情
     */
    StationVO selectStationDetail(@Param("stationId") Long stationId);

    /**
     * 获取热门站点
     * 
     * @param city 城市
     * @param limit 数量限制
     * @return 热门站点列表
     */
    @Select("SELECT s.*, " +
            "(SELECT COUNT(*) FROM favorites f WHERE f.type = 1 AND f.target_id = s.id) as favorite_count " +
            "FROM stations s " +
            "WHERE s.status = 1 AND s.deleted = 0 " +
            "AND (#{city} IS NULL OR s.city = #{city}) " +
            "ORDER BY s.rating DESC, favorite_count DESC " +
            "LIMIT #{limit}")
    List<Station> selectHotStations(@Param("city") String city, @Param("limit") Integer limit);

    /**
     * 更新站点评分
     * 
     * @param stationId 站点ID
     * @param rating 新评分
     * @param reviewCount 评价数量
     * @return 影响行数
     */
    @Select("UPDATE stations SET rating = #{rating}, review_count = #{reviewCount} WHERE id = #{stationId}")
    int updateStationRating(@Param("stationId") Long stationId, 
                           @Param("rating") BigDecimal rating, 
                           @Param("reviewCount") Integer reviewCount);
}
