package com.manmanrecycle;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * 慢慢回收应用启动类
 * 
 * <AUTHOR> Team
 * @since 2024-01-15
 */
@SpringBootApplication
@MapperScan("com.manmanrecycle.mapper")
@EnableTransactionManagement
@EnableCaching
@EnableAsync
@EnableScheduling
public class ManmanRecycleApplication {

    public static void main(String[] args) {
        SpringApplication.run(ManmanRecycleApplication.class, args);
        System.out.println("\n" +
                "  __  __                                   ____                      _      \n" +
                " |  \\/  | __ _ _ __  _ __ ___   __ _ _ __   |  _ \\ ___  ___ _   _  ___| | ___ \n" +
                " | |\\/| |/ _` | '_ \\| '_ ` _ \\ / _` | '_ \\  | |_) / _ \\/ __| | | |/ __| |/ _ \\\n" +
                " | |  | | (_| | | | | | | | | | (_| | | | | |  _ <  __/ (__| |_| | (__| |  __/\n" +
                " |_|  |_|\\__,_|_| |_|_| |_| |_|\\__,_|_| |_| |_| \\_\\___|\\___|\\__, |\\___|_|\\___|\n" +
                "                                                          |___/             \n" +
                "\n🌿 慢慢回收API服务启动成功！\n" +
                "📡 接口文档: http://localhost:8080/api/swagger-ui/\n" +
                "📊 监控面板: http://localhost:8080/api/druid/\n" +
                "🔍 健康检查: http://localhost:8080/api/actuator/health\n");
    }
}
