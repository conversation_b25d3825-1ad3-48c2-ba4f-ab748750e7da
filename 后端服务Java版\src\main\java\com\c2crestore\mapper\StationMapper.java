package com.c2crestore.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.c2crestore.entity.Station;
import com.c2crestore.vo.StationVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.math.BigDecimal;
import java.util.List;

/**
 * 回收站点Mapper接口
 * 
 * <AUTHOR> Restore Team
 * @since 2024-01-15
 */
@Mapper
public interface StationMapper extends BaseMapper<Station> {

    /**
     * 查询附近站点
     * 
     * @param page 分页参数
     * @param lng 经度
     * @param lat 纬度
     * @param radius 半径(km)
     * @param status 状态
     * @return 站点列表
     */
    @Select("<script>" +
            "SELECT s.*, " +
            "ROUND(6371 * acos(cos(radians(#{lat})) * cos(radians(s.lat)) * " +
            "cos(radians(s.lng) - radians(#{lng})) + sin(radians(#{lat})) * " +
            "sin(radians(s.lat))), 2) AS distance " +
            "FROM stations s " +
            "WHERE s.deleted = 0 " +
            "<if test='status != null'> AND s.status = #{status} </if>" +
            "HAVING distance &lt;= #{radius} " +
            "ORDER BY distance ASC" +
            "</script>")
    IPage<StationVO> selectNearbyStations(
            Page<StationVO> page,
            @Param("lng") BigDecimal lng,
            @Param("lat") BigDecimal lat,
            @Param("radius") BigDecimal radius,
            @Param("status") Integer status
    );

    /**
     * 搜索站点
     * 
     * @param page 分页参数
     * @param keyword 关键词
     * @param city 城市
     * @return 站点列表
     */
    @Select("<script>" +
            "SELECT s.* FROM stations s " +
            "WHERE s.deleted = 0 " +
            "<if test='keyword != null and keyword != \"\"'>" +
            "AND (s.name LIKE CONCAT('%', #{keyword}, '%') " +
            "OR s.address LIKE CONCAT('%', #{keyword}, '%')) " +
            "</if>" +
            "<if test='city != null and city != \"\"'>" +
            "AND s.city = #{city} " +
            "</if>" +
            "ORDER BY s.rating DESC, s.created_at DESC" +
            "</script>")
    IPage<StationVO> searchStations(
            Page<StationVO> page,
            @Param("keyword") String keyword,
            @Param("city") String city
    );

    /**
     * 获取站点详情
     * 
     * @param stationId 站点ID
     * @return 站点详情
     */
    @Select("SELECT s.* FROM stations s WHERE s.id = #{stationId} AND s.deleted = 0")
    StationVO selectStationDetail(@Param("stationId") Long stationId);

    /**
     * 获取热门站点
     * 
     * @param city 城市
     * @param limit 数量限制
     * @return 热门站点列表
     */
    @Select("<script>" +
            "SELECT s.*, " +
            "(SELECT COUNT(*) FROM favorites f WHERE f.type = 1 AND f.target_id = s.id AND f.deleted = 0) as favorite_count " +
            "FROM stations s " +
            "WHERE s.status = 1 AND s.deleted = 0 " +
            "<if test='city != null and city != \"\"'> AND s.city = #{city} </if>" +
            "ORDER BY s.rating DESC, favorite_count DESC " +
            "LIMIT #{limit}" +
            "</script>")
    List<Station> selectHotStations(@Param("city") String city, @Param("limit") Integer limit);

    /**
     * 更新站点评分
     * 
     * @param stationId 站点ID
     * @param rating 新评分
     * @param reviewCount 评价数量
     * @return 影响行数
     */
    @Update("UPDATE stations SET rating = #{rating}, review_count = #{reviewCount} WHERE id = #{stationId}")
    int updateStationRating(@Param("stationId") Long stationId, 
                           @Param("rating") BigDecimal rating, 
                           @Param("reviewCount") Integer reviewCount);
}
