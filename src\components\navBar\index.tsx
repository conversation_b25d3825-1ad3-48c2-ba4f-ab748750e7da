import { dicoverMenu } from '../../common/localData'
import './style.css'
import { NavLink } from 'react-router-dom'


type NavBarMenuType = {
  title: string;
  link: string
}
type NavBarMenuListType2 = {
  title: string;
  link: string;
  children?: NavBarMenuListType2[]
}
export default function NavBar() {
  // other handle
  return (
    // navBarBox className  dicoverMenu.map(item: NavBarMenuListType2)
    <div className='navBarBox'>
      <div className="width1100 navBarCategoryList">
        {dicoverMenu.map((item: NavBarMenuType) => {
          return (
            <li key={item.title} className="navBarCategoryListItem">
              <NavLink to={item.link}>
                {item.title}
              </NavLink>
            </li>
          )
        })}
      </div>
    </div>
  )
}
