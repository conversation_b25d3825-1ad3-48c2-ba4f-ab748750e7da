package com.c2crecycle;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * C2C回收平台主启动类
 * 
 * <AUTHOR> Recycle Team
 * @since 2024-01-15
 */
@SpringBootApplication
public class C2cRecycleApplication {

    public static void main(String[] args) {
        SpringApplication.run(C2cRecycleApplication.class, args);
        System.out.println("\n" +
                "  ____  ____   ____   ____                      _      \n" +
                " / ___|/ ___| / ___| |  _ \\ ___  ___ _   _  ___| | ___ \n" +
                "| |   | |    | |     | |_) / _ \\/ __| | | |/ __| |/ _ \\\n" +
                "| |___| |___ | |___  |  _ <  __/ (__| |_| | (__| |  __/\n" +
                " \\____|\\____| \\____| |_| \\_\\___|\\___|\\__, |\\___|_|\\___|\n" +
                "                                    |___/             \n" +
                "\n" +
                "🎉 C2C回收平台启动成功！\n" +
                "📖 API文档地址: http://localhost:8080/api/doc.html\n" +
                "🔍 Swagger地址: http://localhost:8080/api/swagger-ui/\n" +
                "📊 监控地址: http://localhost:8080/api/actuator\n" +
                "💾 数据库监控: http://localhost:8080/api/druid\n");
    }
}
