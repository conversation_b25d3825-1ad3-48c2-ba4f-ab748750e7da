/**
 * 歌曲简略信息
 */
export type MusicBrief = {
  name: string;
  id: number;
  artists: {
    id: number;
    name: string;
  }[];
};

/**
 * 歌曲详细信息
 */
export type MusicDetail = {
  /** album */
  al: {
    id: number;
    name: string;
    picUrl: string;
  };
  /** 歌曲时长, ms */
  dt: number;
  /** 歌曲出处 */
  alia: string[];
  publishTime: number;
  picUrl: string;
} & MusicBrief;

/**
 * music and url
 * 歌曲完整信息, 请求 `/song/url` 得到
 */
export type MusicUrl = {
  url?: string;
} & MusicDetail;

export const defaultMusic: MusicUrl = {
  id: 0,
  url: "",
  name: "",
  artists: [
    {
      id: 0,
      name: "",
    },
  ],
  dt: 0,
  al: {
    id: 0,
    name: "",
    picUrl: "",
  },
  picUrl: "",
  publishTime: Date.now(),
  alia: [],
};

export enum PlayMode {
  LOOP,
  NORMAL,
  RAND,
}

/**
 * 调用`/recommend/resource`接口获得的音乐数据
 */
export type RecommendedPlaylist = {
  id: number;
  type: 1;
  /** 例子: "这些充满『强烈画面感』的音乐"; */
  name: string;
  /** 例子: "根据你喜欢的单曲《通天大道宽又阔》推荐"; */
  copywriter: string;
  picUrl: string;
  playcount: number;
  /** ms 时间戳 */
  createTime: number;
  trackCount: 462;
};
