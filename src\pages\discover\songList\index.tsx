import React, { useEffect, memo } from 'react'
import NavBar from "../../../components/navBar";
import { useDispatch } from 'react-redux'
import { useSearchParams } from 'react-router-dom'

import {
    getCategory,
    getSongList,
    changeCurrentCategoryAction,
} from './store/actionCreators'
import Song<PERSON>istHeader from './songListHeader'
import SongListMain from './songListMain'
import './style.css'

export default memo(function SongList() {

    let [searchParams, setSearchParams] = useSearchParams();
    let cat = searchParams.get("cat")
    const dispatch = useDispatch()

    useEffect(() => {
        dispatch(changeCurrentCategoryAction(cat || "全部"))
    }, [dispatch, cat])

    // hooks
    useEffect(() => {
        dispatch(getCategory())
        dispatch(getSongList(0))
    }, [dispatch])

    return (
        <div>
            <NavBar />
            <div className="songList">  
                <SongListHeader />
                <SongListMain />
            </div>
        </div>
    )
})

