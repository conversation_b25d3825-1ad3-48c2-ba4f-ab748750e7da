import React, { memo } from 'react'
import { shallowEqual, useSelector } from 'react-redux'
import { getSizeImage } from '../../../utils/formatUtils'
import { getPlayUrl } from '../../../utils/handleData'
import { Collapse } from 'antd'
import './style.css'

export default memo(function SongInfo() {
  // redux hook
  const { currentSong, lyricList, totalComment } = useSelector(
    (state: any) => ({
      currentSong: state.getIn(['player', 'currentSong']),
      lyricList: state.getIn(['player', 'lyricList']),
      totalComment: state.getIn(['player', 'currentCommentTotal']),
    }),
    shallowEqual
  )
  // other handle
  const { Panel } = Collapse
  const pirUrl = currentSong.al && currentSong.al.picUrl
  const songName = currentSong.name ? currentSong.name : ''
  const singer = currentSong.ar && currentSong.ar[0].name
  const album = currentSong.al && currentSong.al.name
  const albumId = currentSong.al && currentSong.al.id
  const singerId = currentSong.ar && currentSong.ar[0].id
  // funciton
  const playMusic = () => {
    (document.querySelector('#audio') as any).play()
  }

  return (
      <div className="songInfoBox">
        <div className="songInfoAlbum">
          <img src={getSizeImage(pirUrl, 130)} alt="" />
          <div className="songInfoAlbumCover"></div>
        </div>
        <div className="songInfoDetail">
          <div className="songInfoTitle">
            <i className="songInfoSingleSong"></i>
            <h2 className="songInfoSongName">{songName}</h2>
            <em className="songInfoMV"></em>
          </div>
          <div className="songInfoSinger">
            <span>歌手：</span>
            <a href={`#/artist?id=${singerId}`}>{singer}</a>
          </div>
          <div className="songInfoSettleAlbum">
            <span>所属专辑：</span>
            <a href={`#/album?id=${albumId}`}>
              {album}
            </a>
          </div>
          <div className="songInfoControls">
            <div className="songInfoControlPlay" onClick={() => playMusic()}>
              <i className="songInfoControlInner">
                <em className="songInfoControlPlayIcon"></em>
                播放
              </i>
            </div>
            <div className="songInfoControlDownload" onClick={() => window.open(getPlayUrl(currentSong.id))}>
              <i className="songInfoControlInner">
                <em className="favorite-icon"></em>
                下载
              </i>
            </div>
            <div className="songInfoControlComment">
              <i className="songInfoControlInner">
                <em className="favorite-icon"></em>({totalComment})
              </i>
            </div>
          </div>
          <Collapse>
            <Panel key="show" header={`歌词展示`}>
              {lyricList &&
                lyricList.map((item: any, index: any) => {
                  return (
                    <div key={item.totalTime + index} className="songInfoLyricItem">
                      {item.content}
                    </div>
                  )
                })}
            </Panel>
          </Collapse>
        </div>
      </div>
  )
})
