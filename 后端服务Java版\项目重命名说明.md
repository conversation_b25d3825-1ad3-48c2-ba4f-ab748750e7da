# 🔄 项目重命名说明 - C2C Restore

## 📋 项目名称变更

原项目名称：**慢慢回收 (ManmanRecycle)**  
新项目名称：**C2C Restore (C2C二手回收平台)**

## 🔧 已更新的配置

### 1. Maven配置 (pom.xml)
```xml
<groupId>com.c2crestore</groupId>
<artifactId>c2c-restore-api</artifactId>
<name>C2C Restore API服务</name>
<description>C2C二手回收平台后端API服务</description>
```

### 2. 包名结构
```
原包名: com.manmanrecycle.*
新包名: com.c2crestore.*
```

### 3. 主启动类
```java
// 原文件: ManmanRecycleApplication.java
// 新文件: C2cRestoreApplication.java
@SpringBootApplication
@MapperScan("com.c2crestore.mapper")
public class C2cRestoreApplication
```

### 4. 应用配置 (application.yml)
```yaml
spring:
  application:
    name: c2c-restore-api

mybatis-plus:
  type-aliases-package: com.c2crestore.entity

logging:
  level:
    com.c2crestore: debug
    com.c2crestore.mapper: debug
  file:
    name: logs/c2c-restore.log

app:
  jwt:
    secret: c2c-restore-jwt-secret-key-2024
```

### 5. Swagger配置
```java
private ApiInfo apiInfo() {
    return new ApiInfoBuilder()
            .title("C2C Restore API接口文档")
            .description("C2C二手回收平台后端API服务接口文档")
            .contact(new Contact("C2C Restore Team", "https://c2crestore.com", "<EMAIL>"))
            .build();
}
```

## 📁 目录结构变更

### 新的包结构
```
src/main/java/com/c2crestore/
├── C2cRestoreApplication.java        # 主启动类
├── config/                           # 配置类
│   └── SwaggerConfig.java           # Swagger配置
├── controller/                       # 控制器层
│   └── AuthController.java         # 认证控制器
├── service/                          # 服务层
├── mapper/                           # 数据访问层
├── entity/                           # 实体类
├── dto/                              # 数据传输对象
├── vo/                               # 视图对象
├── common/                           # 公共模块
│   ├── result/                      # 统一响应结果
│   └── exception/                   # 异常处理
└── util/                             # 工具类
```

## 🚀 启动信息更新

### 新的启动横幅
```
   ____ ____   ____   ____           _                  
  / ___|___ \ / ___| |  _ \ ___  ___| |_ ___  _ __ ___  
 | |     __) | |     | |_) / _ \/ __| __/ _ \| '__/ _ \ 
 | |___ / __/| |___  |  _ <  __/\__ \ || (_) | | |  __/ 
  \____|_____|____| |_| \_\___||___/\__\___/|_|  \___| 

🔄 C2C Restore API服务启动成功！
📡 接口文档: http://localhost:8080/api/swagger-ui/
📊 监控面板: http://localhost:8080/api/druid/
🔍 健康检查: http://localhost:8080/api/actuator/health
```

## 🔗 访问地址

### 开发环境
- **API文档**: http://localhost:8080/api/swagger-ui/
- **数据库监控**: http://localhost:8080/api/druid/
- **健康检查**: http://localhost:8080/api/actuator/health
- **应用信息**: http://localhost:8080/api/actuator/info

### 项目仓库
- **GitHub**: https://github.com/your-org/c2c-restore-java
- **文档Wiki**: https://github.com/your-org/c2c-restore-java/wiki

## 📦 构建和部署

### Maven构建
```bash
# 编译项目
mvn clean compile

# 运行测试
mvn test

# 打包项目
mvn clean package -DskipTests

# 生成的JAR文件
target/c2c-restore-api-1.0.0.jar
```

### Docker部署
```bash
# 构建镜像
docker build -t c2c-restore-api .

# 运行容器
docker run -d -p 8080:8080 --name c2c-restore-api c2c-restore-api
```

### 系统服务
```bash
# 服务配置文件
sudo cp c2c-restore.service /etc/systemd/system/

# 启动服务
sudo systemctl enable c2c-restore
sudo systemctl start c2c-restore
```

## 🔧 开发环境配置

### IDE配置更新
1. **项目导入**: 重新导入Maven项目
2. **包名重构**: 如果使用IDE重构，确保所有引用都已更新
3. **运行配置**: 更新主类为 `com.c2crestore.C2cRestoreApplication`

### 环境变量
```bash
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_NAME=c2c_restore_db
DB_USERNAME=root
DB_PASSWORD=your_password

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379

# JWT配置
JWT_SECRET=c2c-restore-jwt-secret-key-2024

# 微信小程序配置
WECHAT_APP_ID=your_wechat_app_id
WECHAT_APP_SECRET=your_wechat_app_secret
```

## 📝 注意事项

### 1. 数据库更新
如果已有数据库，建议重命名数据库：
```sql
-- 重命名数据库（可选）
CREATE DATABASE c2c_restore_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
-- 然后迁移数据
```

### 2. 配置文件检查
确保所有配置文件中的包名和应用名称都已更新。

### 3. 日志文件
新的日志文件路径：
- 开发环境: `logs/c2c-restore.log`
- 生产环境: `/var/log/c2c-restore/app.log`

### 4. 接口测试
重新导入Postman集合，更新所有接口的baseUrl。

---

**🔄 项目重命名完成，现在可以使用新的C2C Restore项目名称进行开发！**
