package com.c2crestore.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.c2crestore.dto.NearbyStationDTO;
import com.c2crestore.dto.SearchStationDTO;
import com.c2crestore.vo.StationDetailVO;
import com.c2crestore.vo.StationVO;

import java.util.List;

/**
 * 站点服务接口
 * 
 * <AUTHOR> Restore Team
 * @since 2024-01-15
 */
public interface StationService {

    /**
     * 获取附近站点
     * 
     * @param queryDTO 查询参数
     * @return 站点列表
     */
    IPage<StationVO> getNearbyStations(NearbyStationDTO queryDTO);

    /**
     * 搜索站点
     * 
     * @param searchDTO 搜索参数
     * @return 站点列表
     */
    IPage<StationVO> searchStations(SearchStationDTO searchDTO);

    /**
     * 获取站点详情
     * 
     * @param stationId 站点ID
     * @param userId 用户ID（可选）
     * @return 站点详情
     */
    StationDetailVO getStationDetail(Long stationId, Long userId);

    /**
     * 获取热门站点
     * 
     * @param city 城市
     * @param limit 数量限制
     * @return 热门站点列表
     */
    List<StationVO> getHotStations(String city, Integer limit);
}
