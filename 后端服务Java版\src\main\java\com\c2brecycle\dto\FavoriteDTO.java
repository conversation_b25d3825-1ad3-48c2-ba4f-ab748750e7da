package com.c2brecycle.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.*;

/**
 * 收藏参数DTO
 * 
 * <AUTHOR> Recycle Team
 * @since 2024-01-15
 */
@Data
@ApiModel("收藏参数")
public class FavoriteDTO {

    @ApiModelProperty(value = "收藏类型", required = true, example = "1", notes = "1-站点 2-产品")
    @NotNull(message = "收藏类型不能为空")
    @Min(value = 1, message = "收藏类型只能是1或2")
    @Max(value = 2, message = "收藏类型只能是1或2")
    private Integer type;

    @ApiModelProperty(value = "目标ID", required = true, example = "123")
    @NotNull(message = "目标ID不能为空")
    @Min(value = 1, message = "目标ID必须大于0")
    private Long targetId;
}
