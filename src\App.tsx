
import { Provider } from 'react-redux'
import { HashRouter } from 'react-router-dom'
import AppFooter from './components/appFooter';
import AppHeader from './components/appHeader';
import BackTop from './components/backTop';
import PlayerBar from './components/playerBar';
import APP from './pages/app'
import store from './store';
export default function App() {
  return (
    <Provider store={store}>
    <HashRouter>
      <AppHeader />
      <APP />
      <AppFooter />
      <PlayerBar/>
      <BackTop />
    </HashRouter>
  </Provider>
  )

}


