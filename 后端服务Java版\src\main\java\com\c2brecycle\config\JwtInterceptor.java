package com.c2brecycle.config;

import cn.hutool.core.util.StrUtil;
import com.c2brecycle.service.AuthService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * JWT拦截器
 * 
 * <AUTHOR> Recycle Team
 * @since 2024-01-15
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class JwtInterceptor implements HandlerInterceptor {

    private final AuthService authService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 预检请求直接放行
        if ("OPTIONS".equals(request.getMethod())) {
            return true;
        }

        // 获取Authorization头
        String authHeader = request.getHeader("Authorization");
        
        if (StrUtil.isBlank(authHeader) || !authHeader.startsWith("Bearer ")) {
            log.warn("请求缺少Authorization头或格式错误: {}", request.getRequestURI());
            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
            response.setContentType("application/json;charset=UTF-8");
            response.getWriter().write("{\"code\":401,\"message\":\"未授权访问\",\"timestamp\":" + System.currentTimeMillis() + "}");
            return false;
        }

        // 提取Token
        String token = authHeader.substring(7);
        
        // 验证Token
        Long userId = authService.verifyToken(token);
        if (userId == null) {
            log.warn("Token验证失败: {}", request.getRequestURI());
            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
            response.setContentType("application/json;charset=UTF-8");
            response.getWriter().write("{\"code\":401,\"message\":\"Token无效或已过期\",\"timestamp\":" + System.currentTimeMillis() + "}");
            return false;
        }

        // 将用户ID存储到请求属性中
        request.setAttribute("userId", userId);
        
        log.debug("Token验证成功，userId: {}, uri: {}", userId, request.getRequestURI());
        return true;
    }
}
