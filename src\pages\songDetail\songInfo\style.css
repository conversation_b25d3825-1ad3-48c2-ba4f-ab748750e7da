.songInfoBox {
    display: flex;
}
.songInfoBox .songInfoAlbum {
    position: relative;
    width: 206px;
}
.songInfoBox .songInfoAlbum img {
    margin: 34px;
}
.songInfoBox .songInfoAlbum .songInfoAlbumCover {
    position: absolute;
    left: -4px;
    right: 0;
    top: -4px;
    bottom: 0;
    background: url(../../../static/images/sprite_cover.png) no-repeat -145px -57px;
    background-position: -140px -580px;
    height: 215px;
}
.songInfoBox .songInfoDetail {
    width: 414px;
    margin-left: 20px;
}

.songInfoBox .songInfoDetail .songInfoTitle,
.songInfoBox .songInfoDetail .songInfoSinger,
.songInfoBox .songInfoDetail .songInfoControls,
.songInfoBox .songInfoDetail .songInfoSettleAlbum {
  display: flex;
  align-items: center;
}

.songInfoBox .songInfoDetail .songInfoSinger>span,
.songInfoBox .songInfoDetail .songInfoSettleAlbum>span {
    color: #999;
}
.songInfoBox .songInfoDetail .songInfoSinger>a,
.songInfoBox .songInfoDetail .songInfoSettleAlbum>a {
    color: #0c73c2;
    cursor: pointer;
}

.songInfoBox .songInfoDetail .songInfoTitle {
    height: 32px;
    line-height: 32px;
}
.songInfoBox .songInfoDetail .songInfoTitle .songInfoSingleSong {
    width: 54px;
    height: 24px;
    background: url(../../../static/images/sprite_icon2.png) 0 -463px;
}
.songInfoBox .songInfoDetail .songInfoTitle .songInfoSongName {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 24px;
    margin: 0 15px 0 11px;
}
.songInfoBox .songInfoDetail .songInfoTitle .songInfoMV {
    width: 21px;
    height: 18px;
    background: url(../../../static/images/sprite_icon2.png) 0 -18px;
}

.songInfoBox .songInfoDetail .songInfoSinger,
.songInfoBox .songInfoDetail .songInfoSettleAlbum {
    margin: 10px 0;
}

.songInfoBox .songInfoDetail .songInfoControls {
    margin-bottom: 25px;
    margin-right: -10px;
}

.songInfoBox .songInfoDetail .songInfoControls .songInfoControlPlay {
    position: relative;
    color: #fff;
    width: 66px;
    height: 31px;
    padding: 0 5px 0 0;
    margin-right: 35px;
    line-height: 31px;
    background: url(../../../static/images/sprite_button.png) right -428px;
    cursor: pointer;
}
.songInfoBox .songInfoDetail .songInfoControls .songInfoControlPlay .songInfoControlPlayIcon {
    width: 20px;
    height: 18px;
    margin: 6px 2px 2px 0;
    background: url(../../../static/images/sprite_button.png) 0 -1622px;
    overflow: hidden;
}
.songInfoBox .songInfoDetail .songInfoControls .songInfoControlPlay .songInfoControlPlayIcon:after {
    content: '';
    position: absolute;
    right: -29px;
    top: 0;
    bottom: 0;
    width: 31px;
    background: url(../../../static/images/sprite_button.png) 0 -1588px;
}

.songInfoBox .songInfoDetail .songInfoControls .songInfoControlInner {
    display: flex;
    width: 100%;
    height: 31px;
    padding: 0 7px 0 8px;
    background: url(../../../static/images/sprite_button.png) 0 -387px;
    line-height: 31px;
}

.songInfoBox .songInfoDetail .songInfoControls .songInfoControlDownload,
.songInfoBox .songInfoDetail .songInfoControls .songInfoControlComment {
    cursor: pointer;
    background-position: right -1020px;
    margin-right: 6px;
    padding: 0 5px 0 0;
}

.songInfoBox .songInfoDetail .songInfoControls .songInfoControlDownload>.songInfoControlInner,
.songInfoBox .songInfoDetail .songInfoControls .songInfoControlComment >.songInfoControlInner {
    padding-right: 2px;
    padding-left: 28px;
    border-right: 1px solid #999;
    border-radius: 4px;
}

.songInfoBox .songInfoDetail .songInfoControls .songInfoControlDownload .songInfoControlInner {
    background-position: 0 -2761px;
}
  

.songInfoBox .songInfoDetail .songInfoControls .songInfoControlComment .songInfoControlInner {
    background-position: 0 -1465px;
}


.songInfoLyricItem {
  color: #333;
  text-align: center;
  margin: 9px 0;
}
.songInfoLyricItem:last-child,
.songInfoLyricItem:first-child {
    margin-bottom: 0;
    margin-top: 0;
}
