import React, { memo } from 'react';
import { useSelector, shallowEqual, useDispatch } from 'react-redux';
import SongListCategory from '../songListCategory'
import { changeCategoryIsVisible } from '../store/actionCreators';
import './style.css'
export default memo(function SongListHeader() {
  // hooks
  const dispatch = useDispatch();
  const { showCategory } = useSelector(
    (state: any) => ({
      showCategory: state.getIn(['songList', 'categoryIsVisible']),
    }),
    shallowEqual
  );
  // redux
  const { currentCategory } = useSelector((state: any) => ({
    currentCategory: state.getIn(["songList", "currentCategory"])
  }), shallowEqual);

  return (
    <div className="songListHeader">
      <div className="songListLeft">
        <span className="songListTitle">{currentCategory || "全部"}</span>
        <button className="songListSelect" onClick={() => dispatch(changeCategoryIsVisible(!showCategory))}>
          <span>选择分类</span>
          <i></i>
        </button>
        {showCategory ? <SongListCategory /> : null}
      </div>

      <div className="songListRight">
        <button className="songListHot">热门</button>
      </div>
    </div>

  )
})
