package com.manmanrecycle.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 微信登录请求DTO
 * 
 * <AUTHOR> Team
 * @since 2024-01-15
 */
@Data
@ApiModel("微信登录请求")
public class WechatLoginDTO {

    @ApiModelProperty(value = "微信授权码", required = true)
    @NotBlank(message = "微信授权码不能为空")
    private String code;

    @ApiModelProperty("用户昵称")
    private String nickname;

    @ApiModelProperty("用户头像")
    private String avatar;

    @ApiModelProperty("性别 0-未知 1-男 2-女")
    private Integer gender;

    @ApiModelProperty("城市")
    private String city;

    @ApiModelProperty("省份")
    private String province;

    @ApiModelProperty("国家")
    private String country;
}
