package com.c2crestore.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.c2crestore.entity.Favorite;
import com.c2crestore.vo.FavoriteVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 用户收藏Mapper接口
 * 
 * <AUTHOR> Restore Team
 * @since 2024-01-15
 */
@Mapper
public interface FavoriteMapper extends BaseMapper<Favorite> {

    /**
     * 获取用户收藏列表
     * 
     * @param page 分页参数
     * @param userId 用户ID
     * @param type 收藏类型
     * @return 收藏列表
     */
    @Select("<script>" +
            "SELECT f.*, " +
            "CASE " +
            "WHEN f.type = 1 THEN s.name " +
            "WHEN f.type = 2 THEN p.name " +
            "END as target_name, " +
            "CASE " +
            "WHEN f.type = 1 THEN s.image " +
            "WHEN f.type = 2 THEN p.image " +
            "END as target_image, " +
            "CASE " +
            "WHEN f.type = 1 THEN s.address " +
            "WHEN f.type = 2 THEN c.name " +
            "END as target_desc " +
            "FROM favorites f " +
            "LEFT JOIN stations s ON f.type = 1 AND f.target_id = s.id " +
            "LEFT JOIN products p ON f.type = 2 AND f.target_id = p.id " +
            "LEFT JOIN categories c ON f.type = 2 AND p.category_id = c.id " +
            "WHERE f.user_id = #{userId} AND f.deleted = 0 " +
            "<if test='type != null'> AND f.type = #{type} </if>" +
            "ORDER BY f.created_at DESC" +
            "</script>")
    IPage<FavoriteVO> selectByUserId(
            Page<FavoriteVO> page,
            @Param("userId") Long userId,
            @Param("type") Integer type
    );

    /**
     * 检查是否已收藏
     * 
     * @param userId 用户ID
     * @param type 收藏类型
     * @param targetId 目标ID
     * @return 收藏记录
     */
    @Select("SELECT * FROM favorites WHERE user_id = #{userId} AND type = #{type} AND target_id = #{targetId} AND deleted = 0")
    Favorite selectByUserAndTarget(@Param("userId") Long userId, @Param("type") Integer type, @Param("targetId") Long targetId);
}
