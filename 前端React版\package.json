{"name": "c2b-recycle-frontend", "version": "1.0.0", "description": "C2B回收平台前端 - React版本", "private": true, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "react-router-dom": "^6.8.1", "redux": "^4.2.1", "react-redux": "^8.0.5", "redux-thunk": "^2.4.2", "redux-devtools-extension": "^2.13.9", "axios": "^1.3.4", "antd": "^5.2.2", "@ant-design/icons": "^5.0.1", "styled-components": "^5.3.9", "moment": "^2.29.4", "lodash": "^4.17.21", "classnames": "^2.3.2"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.0"}, "proxy": "http://localhost:8080"}