# 🗄️ 慢慢回收数据库领域划分设计

## 📋 设计原则

### 简化原则
- **字段简短**：字段名称简洁明了，避免冗长
- **类型优化**：使用合适的数据类型，节省存储空间
- **索引精简**：只在必要字段上建立索引
- **关系清晰**：表间关系明确，避免复杂依赖

### 领域驱动
- **按业务领域划分**：将相关功能的表归类到同一领域
- **职责单一**：每个表只负责一个明确的业务概念
- **边界清晰**：领域间通过外键关联，保持独立性

---

## 🏗️ 领域架构图

```
┌─────────────────┐    ┌─────────────────┐
│   用户领域       │    │   产品领域       │
│  User Domain    │    │ Product Domain  │
├─────────────────┤    ├─────────────────┤
│ • users         │    │ • categories    │
│ • user_addresses│    │ • products      │
└─────────────────┘    │ • prices        │
                       └─────────────────┘
           │                      │
           └──────────┬───────────┘
                      │
┌─────────────────┐   │   ┌─────────────────┐
│   站点领域       │   │   │   行为领域       │
│ Station Domain  │   │   │Behavior Domain  │
├─────────────────┤   │   ├─────────────────┤
│ • stations      │   │   │ • search_logs   │
│ • station_services│ │   │ • favorites     │
│ • reviews       │   │   │ • browse_logs   │
└─────────────────┘   │   └─────────────────┘
                      │
              ┌─────────────────┐
              │   系统领域       │
              │ System Domain   │
              ├─────────────────┤
              │ • configs       │
              └─────────────────┘
```

---

## 📊 领域详细设计

### 1. 用户领域 (User Domain) 👥

**职责**：管理用户信息和用户相关的基础数据

#### 1.1 users - 用户表
| 字段 | 类型 | 长度 | 说明 | 索引 |
|------|------|------|------|------|
| id | int | 11 | 用户ID | 主键 |
| openid | varchar | 32 | 微信openid | 唯一 |
| nickname | varchar | 20 | 昵称 | - |
| avatar | varchar | 200 | 头像URL | - |
| phone | varchar | 11 | 手机号 | - |
| city | varchar | 20 | 城市 | 普通 |
| status | tinyint | 1 | 状态 | - |

**设计要点**：
- openid长度32位足够存储微信openid
- nickname限制20字符，符合中文昵称习惯
- phone使用varchar(11)存储中国手机号
- city建立索引支持地域统计

#### 1.2 user_addresses - 用户地址表
| 字段 | 类型 | 长度 | 说明 | 索引 |
|------|------|------|------|------|
| id | int | 11 | 地址ID | 主键 |
| user_id | int | 11 | 用户ID | 外键 |
| name | varchar | 20 | 联系人 | - |
| phone | varchar | 11 | 电话 | - |
| address | varchar | 100 | 地址 | - |
| lng | decimal | 9,6 | 经度 | - |
| lat | decimal | 9,6 | 纬度 | - |
| is_default | tinyint | 1 | 默认地址 | - |

**设计要点**：
- 经纬度使用decimal(9,6)精度足够定位
- address长度100字符满足详细地址需求
- 支持多地址管理和默认地址设置

### 2. 产品领域 (Product Domain) 💰

**职责**：管理产品分类、产品信息和价格数据

#### 2.1 categories - 产品分类表
| 字段 | 类型 | 长度 | 说明 | 索引 |
|------|------|------|------|------|
| id | int | 11 | 分类ID | 主键 |
| parent_id | int | 11 | 父分类ID | 普通 |
| name | varchar | 20 | 分类名称 | - |
| icon | varchar | 10 | 图标emoji | - |
| sort | int | 11 | 排序 | - |
| status | tinyint | 1 | 状态 | - |

**设计要点**：
- 支持树形结构分类
- icon存储emoji字符，长度10足够
- sort字段支持自定义排序

#### 2.2 products - 产品表
| 字段 | 类型 | 长度 | 说明 | 索引 |
|------|------|------|------|------|
| id | int | 11 | 产品ID | 主键 |
| category_id | int | 11 | 分类ID | 外键 |
| name | varchar | 50 | 产品名称 | 普通 |
| brand | varchar | 20 | 品牌 | - |
| model | varchar | 30 | 型号 | - |
| image | varchar | 200 | 图片URL | - |
| keywords | varchar | 100 | 搜索关键词 | - |
| status | tinyint | 1 | 状态 | - |

**设计要点**：
- name建立索引支持快速搜索
- brand和model长度适中，满足常见产品
- keywords支持搜索优化

#### 2.3 prices - 产品价格表
| 字段 | 类型 | 长度 | 说明 | 索引 |
|------|------|------|------|------|
| id | int | 11 | 价格ID | 主键 |
| product_id | int | 11 | 产品ID | 外键 |
| station_id | int | 11 | 站点ID(可空) | 外键 |
| min_price | decimal | 8,2 | 最低价 | - |
| max_price | decimal | 8,2 | 最高价 | - |
| unit | varchar | 10 | 单位 | - |
| date | date | - | 日期 | 普通 |

**设计要点**：
- decimal(8,2)支持最高999999.99的价格
- station_id为空表示市场均价
- date索引支持历史价格查询

### 3. 站点领域 (Station Domain) 🏪

**职责**：管理回收站点信息、服务和评价

#### 3.1 stations - 回收站点表
| 字段 | 类型 | 长度 | 说明 | 索引 |
|------|------|------|------|------|
| id | int | 11 | 站点ID | 主键 |
| name | varchar | 30 | 站点名称 | - |
| phone | varchar | 11 | 电话 | - |
| address | varchar | 100 | 地址 | - |
| lng | decimal | 9,6 | 经度 | 复合 |
| lat | decimal | 9,6 | 纬度 | 复合 |
| city | varchar | 20 | 城市 | 普通 |
| hours | varchar | 50 | 营业时间 | - |
| image | varchar | 200 | 图片URL | - |
| rating | decimal | 3,2 | 评分 | 普通 |
| review_count | int | 11 | 评价数 | - |
| status | tinyint | 1 | 状态 | - |

**设计要点**：
- lng,lat复合索引支持地理位置查询
- rating索引支持按评分排序
- hours存储营业时间字符串

#### 3.2 station_services - 站点服务表
| 字段 | 类型 | 长度 | 说明 | 索引 |
|------|------|------|------|------|
| id | int | 11 | 服务ID | 主键 |
| station_id | int | 11 | 站点ID | 外键 |
| category_id | int | 11 | 分类ID | 外键 |
| min_price | decimal | 8,2 | 最低价 | - |
| max_price | decimal | 8,2 | 最高价 | - |
| unit | varchar | 10 | 单位 | - |
| status | tinyint | 1 | 状态 | - |

**设计要点**：
- 关联站点和分类的多对多关系
- 存储该站点该分类的价格范围

#### 3.3 reviews - 站点评价表
| 字段 | 类型 | 长度 | 说明 | 索引 |
|------|------|------|------|------|
| id | int | 11 | 评价ID | 主键 |
| user_id | int | 11 | 用户ID | 外键 |
| station_id | int | 11 | 站点ID | 外键 |
| rating | tinyint | 1 | 评分1-5 | - |
| content | varchar | 200 | 评价内容 | - |
| images | json | - | 图片数组 | - |
| status | tinyint | 1 | 状态 | - |

**设计要点**：
- content限制200字符，适合评价长度
- images使用JSON存储图片数组
- 支持评价状态管理

### 4. 行为领域 (Behavior Domain) 📊

**职责**：记录用户行为数据，支持分析和推荐

#### 4.1 search_logs - 搜索记录表
| 字段 | 类型 | 长度 | 说明 | 索引 |
|------|------|------|------|------|
| id | int | 11 | 记录ID | 主键 |
| user_id | int | 11 | 用户ID | 外键 |
| keyword | varchar | 50 | 关键词 | 普通 |
| type | tinyint | 1 | 搜索类型 | - |
| result_count | int | 11 | 结果数 | - |

**设计要点**：
- keyword建立索引支持热门搜索统计
- type区分产品搜索和站点搜索
- result_count记录搜索质量

#### 4.2 favorites - 收藏表
| 字段 | 类型 | 长度 | 说明 | 索引 |
|------|------|------|------|------|
| id | int | 11 | 收藏ID | 主键 |
| user_id | int | 11 | 用户ID | 外键 |
| type | tinyint | 1 | 收藏类型 | - |
| target_id | int | 11 | 目标ID | - |

**设计要点**：
- 统一收藏表设计，支持多种类型
- 唯一索引防止重复收藏
- type区分收藏的对象类型

#### 4.3 browse_logs - 浏览记录表
| 字段 | 类型 | 长度 | 说明 | 索引 |
|------|------|------|------|------|
| id | int | 11 | 记录ID | 主键 |
| user_id | int | 11 | 用户ID | 外键 |
| type | tinyint | 1 | 浏览类型 | - |
| target_id | int | 11 | 目标ID | - |
| duration | int | 11 | 浏览时长 | - |

**设计要点**：
- 记录用户浏览行为
- duration记录停留时间，分析兴趣度
- 支持个性化推荐算法

### 5. 系统领域 (System Domain) ⚙️

**职责**：管理系统配置和全局设置

#### 5.1 configs - 系统配置表
| 字段 | 类型 | 长度 | 说明 | 索引 |
|------|------|------|------|------|
| id | int | 11 | 配置ID | 主键 |
| key | varchar | 50 | 配置键 | 唯一 |
| value | text | - | 配置值 | - |
| desc | varchar | 100 | 描述 | - |
| type | varchar | 20 | 数据类型 | - |
| status | tinyint | 1 | 状态 | - |

**设计要点**：
- key唯一索引保证配置唯一性
- value使用text支持长配置
- type标识配置值的数据类型

---

## 🔗 领域关系图

```
用户领域 ←→ 行为领域 (用户行为记录)
    ↓
站点领域 ←→ 产品领域 (站点服务关联)
    ↓
系统领域 (全局配置支撑)
```

### 关系说明
1. **用户领域 → 行为领域**：用户产生各种行为数据
2. **用户领域 → 站点领域**：用户评价站点
3. **产品领域 → 站点领域**：站点提供产品服务
4. **系统领域**：为所有领域提供配置支撑

---

## 📈 优化特点

### 存储优化
- **字段长度精确**：根据实际需求设置合适长度
- **数据类型优化**：使用最小满足需求的类型
- **索引精简**：只在必要字段建立索引

### 查询优化
- **复合索引**：地理位置查询使用lng,lat复合索引
- **外键索引**：所有外键字段自动建立索引
- **业务索引**：根据查询场景建立业务索引

### 扩展性
- **领域独立**：各领域相对独立，便于扩展
- **版本兼容**：字段设计考虑向后兼容
- **分表准备**：大数据量表可按时间或用户分表

---

**📝 此设计方案在保证功能完整的前提下，最大化简化了表结构，提高了查询效率和维护性。**
