.simiSongItemBox {
    display: flex;
    align-items: center;
    height: 30px;
    margin: 30px 10px;
}

.simiSongItemBox .simiSongItem {
    padding: 6px 10px;
    line-height: 18px;
    text-align: left;
}

.simiSongItemBox .simiSongInfo {
    display: flex;
    justify-content: space-between;
    width: 230px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.simiSongItemBox .simiSongInfo .active {
    font-size: 15px;
    cursor: pointer;
    margin-right: 8px;
}
.simiSongItemBox .simiSongInfo .active:hover {
    color: #d31111;
}
.simiSongItemBox .simiSongInfo .simiSongAddto {
    width: 17px;
    height: 17px;
    margin-left: 8px;
    cursor: pointer;
    position: relative;
    top: 2px;
    background: url(../../../static/images/sprite_icon2.png) 0 -700px;
}

.simiSongItemBox .simiSongInfo .simiSongInfoLeft {
    display: flex;
    overflow: hidden;
}
.simiSongItemBox .simiSongInfo .simiSongInfoLeft .singer{
    padding-left: 0px;
}
.simiSongItemBox .simiSongInfo .simiSongInfoLeft>a {
    display: inline-block;
    width: 130px;
}