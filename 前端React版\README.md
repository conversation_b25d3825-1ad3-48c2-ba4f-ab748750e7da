# C2B回收平台前端 - React版本

基于React + Redux构建的C2B回收平台前端应用。

## 🚀 技术栈

- **React 18** - 前端框架
- **Redux + Redux Thunk** - 状态管理
- **React Router v6** - 路由管理
- **Ant Design** - UI组件库
- **Axios** - HTTP客户端
- **Styled Components** - CSS-in-JS

## 📦 项目结构

```
src/
├── api/                    # API接口定义
│   ├── auth.js            # 认证相关API
│   ├── product.js         # 产品相关API
│   ├── station.js         # 站点相关API
│   └── user.js            # 用户相关API
├── components/            # 公共组件
│   ├── Auth/              # 认证组件
│   │   ├── Login.js       # 登录页面
│   │   └── ProtectedRoute.js # 路由保护
│   └── Layout/            # 布局组件
│       └── MainLayout.js  # 主布局
├── pages/                 # 页面组件
│   ├── Home.js            # 首页
│   ├── Products.js        # 产品列表
│   ├── ProductDetail.js   # 产品详情
│   ├── Stations.js        # 站点列表
│   ├── StationDetail.js   # 站点详情
│   └── Profile.js         # 个人中心
├── store/                 # Redux状态管理
│   ├── actions/           # Action创建器
│   ├── reducers/          # Reducer函数
│   └── index.js           # Store配置
├── utils/                 # 工具函数
│   └── request.js         # Axios配置
├── App.js                 # 主应用组件
└── index.js               # 应用入口
```

## 🛠️ 开发环境设置

### 1. 安装依赖

```bash
npm install
```

### 2. 启动开发服务器

```bash
npm start
```

应用将在 http://localhost:3000 启动

### 3. 构建生产版本

```bash
npm run build
```

## 🔧 配置说明

### API代理配置

项目已配置代理，所有 `/api` 请求会转发到后端服务器：

```json
{
  "proxy": "http://localhost:8080"
}
```

### 环境变量

可以创建 `.env` 文件配置环境变量：

```env
REACT_APP_API_BASE_URL=http://localhost:8080/api
REACT_APP_APP_NAME=C2B回收平台
```

## 📱 功能特性

### ✅ 已实现功能

#### 认证系统
- [x] 微信登录（模拟）
- [x] 模拟登录（开发环境）
- [x] Token管理
- [x] 路由保护
- [x] 自动登出

#### 产品管理
- [x] 产品分类展示
- [x] 产品搜索
- [x] 产品列表
- [x] 分页功能
- [x] 热门产品

#### 用户界面
- [x] 响应式布局
- [x] 侧边栏导航
- [x] 用户头像菜单
- [x] 首页仪表板

#### 状态管理
- [x] Redux状态管理
- [x] 异步Action处理
- [x] 错误处理
- [x] 加载状态

### 🚧 开发中功能

- [ ] 产品详情页面
- [ ] 站点列表和搜索
- [ ] 站点详情页面
- [ ] 用户个人中心
- [ ] 地址管理
- [ ] 收藏功能

## 🔌 API接口

### 认证接口
- `POST /api/auth/wechat-login` - 微信登录
- `POST /api/auth/refresh-token` - 刷新Token
- `POST /api/auth/logout` - 用户登出
- `GET /api/auth/verify` - 验证Token

### 产品接口
- `GET /api/products/categories` - 获取产品分类
- `GET /api/products/search` - 搜索产品
- `GET /api/products/:id` - 获取产品详情
- `GET /api/products/hot` - 获取热门产品

### 站点接口
- `GET /api/stations/nearby` - 获取附近站点
- `GET /api/stations/search` - 搜索站点
- `GET /api/stations/:id` - 获取站点详情

### 用户接口
- `GET /api/user/profile` - 获取用户信息
- `PUT /api/user/profile` - 更新用户信息
- `GET /api/user/addresses` - 获取地址列表
- `GET /api/user/favorites` - 获取收藏列表

## 🎨 UI组件

使用Ant Design组件库，主要组件包括：

- **Layout** - 布局组件
- **Menu** - 导航菜单
- **Card** - 卡片容器
- **Form** - 表单组件
- **Table** - 数据表格
- **Pagination** - 分页组件
- **Modal** - 模态框
- **Message** - 消息提示

## 📱 响应式设计

- 移动端优先设计
- 断点：576px, 768px, 992px, 1200px
- 自适应布局和组件

## 🔒 安全特性

- JWT Token认证
- 请求拦截器
- 路由保护
- XSS防护
- CSRF防护

## 🚀 部署

### 开发环境
```bash
npm start
```

### 生产环境
```bash
npm run build
npm install -g serve
serve -s build
```

### Docker部署
```dockerfile
FROM node:16-alpine
WORKDIR /app
COPY package*.json ./
RUN npm install
COPY . .
RUN npm run build
EXPOSE 3000
CMD ["npm", "start"]
```

## 🤝 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 📄 许可证

MIT License

---

**🎉 前端React版本已完成基础架构和核心功能！**
