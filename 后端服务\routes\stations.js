const express = require('express');
const { validate, schemas, commonRules } = require('../middleware/validation');
const { optionalAuth } = require('../middleware/auth');
const { asyncHandler } = require('../middleware/errorHandler');
const Response = require('../utils/response');
const logger = require('../utils/logger');
const db = require('../config/database');
const Joi = require('joi');

const router = express.Router();

/**
 * 附近站点查询验证模式
 */
const nearbyStationsSchema = Joi.object({
  lng: commonRules.longitude.required(),
  lat: commonRules.latitude.required(),
  radius: Joi.number().min(0.1).max(50).default(5),
  category: commonRules.optionalId,
  status: Joi.number().valid(0, 1).optional(),
  page: commonRules.page,
  limit: commonRules.limit
});

/**
 * 站点搜索验证模式
 */
const searchStationsSchema = Joi.object({
  keyword: commonRules.keyword.required(),
  city: commonRules.city.optional(),
  category: commonRules.optionalId,
  page: commonRules.page,
  limit: commonRules.limit
});

/**
 * @route GET /api/stations/nearby
 * @desc 获取附近回收站点
 * @access Public
 */
router.get('/nearby',
  validate(nearbyStationsSchema, 'query'),
  optionalAuth,
  asyncHandler(async (req, res) => {
    const { lng, lat, radius, category, status, page, limit } = req.query;

    try {
      // 构建查询条件
      let whereConditions = ['s.status = 1'];
      let queryParams = [];

      if (status !== undefined) {
        whereConditions.push('s.status = ?');
        queryParams.push(status);
      }

      if (category) {
        whereConditions.push('EXISTS (SELECT 1 FROM station_services ss WHERE ss.station_id = s.id AND ss.category_id = ? AND ss.status = 1)');
        queryParams.push(category);
      }

      // 计算距离的SQL（使用Haversine公式）
      const distanceFormula = `
        (6371 * acos(
          cos(radians(?)) * cos(radians(s.lat)) * 
          cos(radians(s.lng) - radians(?)) + 
          sin(radians(?)) * sin(radians(s.lat))
        ))
      `;

      // 添加距离条件
      whereConditions.push(`${distanceFormula} <= ?`);
      queryParams.push(lat, lng, lat, radius);

      const whereClause = whereConditions.join(' AND ');

      // 查询站点
      const sql = `
        SELECT 
          s.id,
          s.name,
          s.phone,
          s.address,
          s.lng,
          s.lat,
          s.city,
          s.hours,
          s.image,
          s.rating,
          s.review_count,
          s.status,
          ${distanceFormula} as distance,
          GROUP_CONCAT(
            CONCAT(c.name, ':', ss.min_price, '-', ss.max_price, ss.unit)
            SEPARATOR ';'
          ) as services
        FROM stations s
        LEFT JOIN station_services ss ON s.id = ss.station_id AND ss.status = 1
        LEFT JOIN categories c ON ss.category_id = c.id
        WHERE ${whereClause}
        GROUP BY s.id
        ORDER BY distance ASC
      `;

      // 执行分页查询
      const result = await db.paginate(sql, queryParams, page, limit);

      // 处理服务信息
      const stations = result.data.map(station => {
        const services = [];
        if (station.services) {
          station.services.split(';').forEach(service => {
            const [name, priceRange] = service.split(':');
            if (name && priceRange) {
              services.push({
                categoryName: name,
                priceRange: priceRange
              });
            }
          });
        }

        return {
          ...station,
          distance: parseFloat(station.distance.toFixed(2)),
          services,
          services: undefined // 移除原始services字段
        };
      });

      return Response.paginate(res, stations, result.pagination, '获取附近站点成功');

    } catch (error) {
      logger.error('获取附近站点失败:', error);
      return Response.internalError(res, '获取站点信息失败');
    }
  })
);

/**
 * @route GET /api/stations/search
 * @desc 搜索回收站点
 * @access Public
 */
router.get('/search',
  validate(searchStationsSchema, 'query'),
  optionalAuth,
  asyncHandler(async (req, res) => {
    const { keyword, city, category, page, limit } = req.query;

    try {
      // 记录搜索行为
      if (req.user) {
        await db.insert('search_logs', {
          user_id: req.user.id,
          keyword,
          type: 2, // 站点搜索
          created_at: new Date()
        });
      }

      // 构建查询条件
      let whereConditions = ['s.status = 1'];
      let queryParams = [];

      // 关键词搜索
      whereConditions.push('(s.name LIKE ? OR s.address LIKE ?)');
      queryParams.push(`%${keyword}%`, `%${keyword}%`);

      // 城市筛选
      if (city) {
        whereConditions.push('s.city = ?');
        queryParams.push(city);
      }

      // 分类筛选
      if (category) {
        whereConditions.push('EXISTS (SELECT 1 FROM station_services ss WHERE ss.station_id = s.id AND ss.category_id = ? AND ss.status = 1)');
        queryParams.push(category);
      }

      const whereClause = whereConditions.join(' AND ');

      // 查询SQL
      const sql = `
        SELECT 
          s.id,
          s.name,
          s.phone,
          s.address,
          s.lng,
          s.lat,
          s.city,
          s.hours,
          s.image,
          s.rating,
          s.review_count,
          s.status,
          GROUP_CONCAT(
            CONCAT(c.name, ':', ss.min_price, '-', ss.max_price, ss.unit)
            SEPARATOR ';'
          ) as services
        FROM stations s
        LEFT JOIN station_services ss ON s.id = ss.station_id AND ss.status = 1
        LEFT JOIN categories c ON ss.category_id = c.id
        WHERE ${whereClause}
        GROUP BY s.id
        ORDER BY s.rating DESC, s.review_count DESC
      `;

      // 执行分页查询
      const result = await db.paginate(sql, queryParams, page, limit);

      // 更新搜索结果数量
      if (req.user) {
        await db.query(
          'UPDATE search_logs SET result_count = ? WHERE user_id = ? AND keyword = ? AND type = 2 ORDER BY created_at DESC LIMIT 1',
          [result.pagination.total, req.user.id, keyword]
        );
      }

      // 处理服务信息
      const stations = result.data.map(station => {
        const services = [];
        if (station.services) {
          station.services.split(';').forEach(service => {
            const [name, priceRange] = service.split(':');
            if (name && priceRange) {
              services.push({
                categoryName: name,
                priceRange: priceRange
              });
            }
          });
        }

        return {
          ...station,
          services,
          services: undefined
        };
      });

      return Response.paginate(res, stations, result.pagination, '搜索站点成功');

    } catch (error) {
      logger.error('搜索站点失败:', error);
      return Response.internalError(res, '搜索服务异常');
    }
  })
);

/**
 * @route GET /api/stations/:id
 * @desc 获取站点详情
 * @access Public
 */
router.get('/:id',
  validate(Joi.object({ id: commonRules.id }), 'params'),
  optionalAuth,
  asyncHandler(async (req, res) => {
    const { id } = req.params;

    try {
      // 记录浏览行为
      if (req.user) {
        await db.insert('browse_logs', {
          user_id: req.user.id,
          type: 1, // 站点浏览
          target_id: id,
          duration: 0, // 前端可以通过另一个接口更新浏览时长
          created_at: new Date()
        });
      }

      // 查询站点基本信息
      const stationSql = `
        SELECT 
          s.*,
          (SELECT COUNT(*) FROM favorites f WHERE f.type = 1 AND f.target_id = s.id) as favorite_count
        FROM stations s 
        WHERE s.id = ? AND s.status = 1
      `;

      const stations = await db.query(stationSql, [id]);

      if (!stations || stations.length === 0) {
        return Response.notFound(res, '站点不存在');
      }

      const station = stations[0];

      // 查询站点服务
      const servicesSql = `
        SELECT 
          ss.*,
          c.name as category_name,
          c.icon as category_icon
        FROM station_services ss
        JOIN categories c ON ss.category_id = c.id
        WHERE ss.station_id = ? AND ss.status = 1
        ORDER BY c.sort ASC
      `;

      const services = await db.query(servicesSql, [id]);

      // 查询最新评价
      const reviewsSql = `
        SELECT 
          r.id,
          r.rating,
          r.content,
          r.images,
          r.created_at,
          u.nickname,
          u.avatar
        FROM reviews r
        JOIN users u ON r.user_id = u.id
        WHERE r.station_id = ? AND r.status = 1
        ORDER BY r.created_at DESC
        LIMIT 10
      `;

      const reviews = await db.query(reviewsSql, [id]);

      // 检查用户是否收藏了该站点
      let isFavorited = false;
      if (req.user) {
        const favoriteCheck = await db.query(
          'SELECT id FROM favorites WHERE user_id = ? AND type = 1 AND target_id = ?',
          [req.user.id, id]
        );
        isFavorited = favoriteCheck.length > 0;
      }

      // 组装返回数据
      const result = {
        ...station,
        services: services.map(service => ({
          categoryId: service.category_id,
          categoryName: service.category_name,
          categoryIcon: service.category_icon,
          minPrice: service.min_price,
          maxPrice: service.max_price,
          unit: service.unit
        })),
        reviews: reviews.map(review => ({
          id: review.id,
          rating: review.rating,
          content: review.content,
          images: review.images ? JSON.parse(review.images) : [],
          createdAt: review.created_at,
          user: {
            nickname: review.nickname,
            avatar: review.avatar
          }
        })),
        isFavorited,
        favoriteCount: station.favorite_count
      };

      return Response.success(res, result, '获取站点详情成功');

    } catch (error) {
      logger.error('获取站点详情失败:', error);
      return Response.internalError(res, '获取站点详情失败');
    }
  })
);

module.exports = router;
