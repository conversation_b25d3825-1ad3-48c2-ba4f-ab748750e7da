package com.c2crestore.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.c2crestore.common.result.Result;
import com.c2crestore.dto.SearchProductDTO;
import com.c2crestore.entity.Category;
import com.c2crestore.service.CategoryService;
import com.c2crestore.service.ProductService;
import com.c2crestore.vo.ProductDetailVO;
import com.c2crestore.vo.ProductVO;
import io.swagger.annotations.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;

/**
 * 产品控制器
 * 
 * <AUTHOR> Restore Team
 * @since 2024-01-15
 */
@Slf4j
@RestController
@RequestMapping("/products")
@RequiredArgsConstructor
@Api(tags = "💰 产品模块", description = "产品和分类相关接口")
public class ProductController {

    private final ProductService productService;
    private final CategoryService categoryService;

    /**
     * 获取产品分类
     */
    @GetMapping("/categories")
    @ApiOperation(value = "获取产品分类", notes = "获取所有产品分类列表")
    @ApiResponses({
        @ApiResponse(code = 200, message = "获取成功")
    })
    public Result<List<Category>> getCategories() {
        log.info("获取产品分类列表");
        
        List<Category> categories = categoryService.getAllCategories();
        
        return Result.success("获取分类成功", categories);
    }

    /**
     * 搜索产品
     */
    @GetMapping("/search")
    @ApiOperation(value = "搜索产品", notes = "根据关键词搜索产品")
    @ApiResponses({
        @ApiResponse(code = 200, message = "搜索成功"),
        @ApiResponse(code = 400, message = "请求参数错误")
    })
    public Result<IPage<ProductVO>> searchProducts(
            @ApiParam("产品搜索参数") @Valid SearchProductDTO searchDTO,
            HttpServletRequest request) {
        
        log.info("搜索产品，关键词: {}, 分类: {}", searchDTO.getKeyword(), searchDTO.getCategoryId());
        
        // 记录搜索行为（如果已登录）
        Long userId = (Long) request.getAttribute("userId");
        if (userId != null) {
            log.info("用户{}搜索产品: {}", userId, searchDTO.getKeyword());
        }
        
        IPage<ProductVO> products = productService.searchProducts(searchDTO);
        
        return Result.success("搜索产品成功", products);
    }

    /**
     * 获取产品详情
     */
    @GetMapping("/{id}")
    @ApiOperation(value = "获取产品详情", notes = "根据产品ID获取详细信息")
    @ApiImplicitParam(name = "id", value = "产品ID", required = true, dataType = "long", paramType = "path")
    @ApiResponses({
        @ApiResponse(code = 200, message = "获取成功"),
        @ApiResponse(code = 404, message = "产品不存在")
    })
    public Result<ProductDetailVO> getProductDetail(
            @PathVariable Long id,
            HttpServletRequest request) {
        
        log.info("获取产品详情，productId: {}", id);
        
        // 记录浏览行为（如果已登录）
        Long userId = (Long) request.getAttribute("userId");
        if (userId != null) {
            log.info("用户{}查看产品详情: {}", userId, id);
        }
        
        ProductDetailVO productDetail = productService.getProductDetail(id, userId);
        
        return Result.success("获取产品详情成功", productDetail);
    }

    /**
     * 获取热门产品
     */
    @GetMapping("/hot")
    @ApiOperation(value = "获取热门产品", notes = "获取搜索量较高的热门产品")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "categoryId", value = "分类ID", dataType = "long", paramType = "query"),
        @ApiImplicitParam(name = "limit", value = "数量限制", dataType = "int", paramType = "query", defaultValue = "10")
    })
    @ApiResponses({
        @ApiResponse(code = 200, message = "获取成功")
    })
    public Result<List<ProductVO>> getHotProducts(
            @RequestParam(required = false) Long categoryId,
            @RequestParam(defaultValue = "10") Integer limit) {
        
        log.info("获取热门产品，分类: {}, 数量: {}", categoryId, limit);
        
        List<ProductVO> hotProducts = productService.getHotProducts(categoryId, limit);
        
        return Result.success("获取热门产品成功", hotProducts);
    }

    /**
     * 获取产品价格信息
     */
    @GetMapping("/{id}/prices")
    @ApiOperation(value = "获取产品价格", notes = "获取产品在各个站点的价格信息")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "id", value = "产品ID", required = true, dataType = "long", paramType = "path"),
        @ApiImplicitParam(name = "stationId", value = "站点ID", dataType = "long", paramType = "query"),
        @ApiImplicitParam(name = "city", value = "城市", dataType = "string", paramType = "query")
    })
    @ApiResponses({
        @ApiResponse(code = 200, message = "获取成功"),
        @ApiResponse(code = 404, message = "产品不存在")
    })
    public Result<Object> getProductPrices(
            @PathVariable Long id,
            @RequestParam(required = false) Long stationId,
            @RequestParam(required = false) String city) {
        
        log.info("获取产品价格，productId: {}, stationId: {}, city: {}", id, stationId, city);
        
        Object priceInfo = productService.getProductPrices(id, stationId, city);
        
        return Result.success("获取产品价格成功", priceInfo);
    }
}
