<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.c2brecycle.mapper.ProductMapper">

    <!-- 产品VO结果映射 -->
    <resultMap id="ProductVOMap" type="com.c2brecycle.vo.ProductVO">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="image" property="image"/>
        <result column="category_id" property="categoryId"/>
        <result column="category_name" property="categoryName"/>
        <result column="description" property="description"/>
        <result column="unit" property="unit"/>
        <result column="min_price" property="minPrice"/>
        <result column="max_price" property="maxPrice"/>
        <result column="avg_price" property="avgPrice"/>
        <result column="search_count" property="searchCount"/>
        <result column="status" property="status"/>
        <result column="created_at" property="createdAt"/>
    </resultMap>

    <!-- 产品详情VO结果映射 -->
    <resultMap id="ProductDetailVOMap" type="com.c2brecycle.vo.ProductDetailVO">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="image" property="image"/>
        <result column="category_id" property="categoryId"/>
        <result column="category_name" property="categoryName"/>
        <result column="description" property="description"/>
        <result column="content" property="content"/>
        <result column="unit" property="unit"/>
        <result column="min_price" property="minPrice"/>
        <result column="max_price" property="maxPrice"/>
        <result column="avg_price" property="avgPrice"/>
        <result column="search_count" property="searchCount"/>
        <result column="is_favorited" property="isFavorited"/>
        <result column="status" property="status"/>
        <result column="created_at" property="createdAt"/>
    </resultMap>

    <!-- 搜索产品 -->
    <select id="searchProducts" resultMap="ProductVOMap">
        SELECT 
            p.id,
            p.name,
            p.image,
            p.category_id,
            c.name as category_name,
            p.description,
            p.unit,
            p.min_price,
            p.max_price,
            p.avg_price,
            p.search_count,
            p.status,
            p.created_at
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.id
        WHERE p.deleted = 0 AND p.status = 1
        <if test="keyword != null and keyword != ''">
            AND (p.name LIKE CONCAT('%', #{keyword}, '%') 
                 OR p.description LIKE CONCAT('%', #{keyword}, '%'))
        </if>
        <if test="categoryId != null">
            AND p.category_id = #{categoryId}
        </if>
        ORDER BY p.search_count DESC, p.created_at DESC
    </select>

    <!-- 获取产品详情 -->
    <select id="selectProductDetail" resultMap="ProductDetailVOMap">
        SELECT 
            p.id,
            p.name,
            p.image,
            p.category_id,
            c.name as category_name,
            p.description,
            p.content,
            p.unit,
            p.min_price,
            p.max_price,
            p.avg_price,
            p.search_count,
            p.status,
            p.created_at,
            CASE WHEN f.id IS NOT NULL THEN 1 ELSE 0 END as is_favorited
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.id
        LEFT JOIN favorites f ON f.target_id = p.id AND f.type = 2 AND f.user_id = #{userId} AND f.deleted = 0
        WHERE p.id = #{productId} AND p.deleted = 0
    </select>

    <!-- 获取产品价格信息 -->
    <select id="selectProductPrices" resultType="java.util.Map">
        SELECT 
            pr.id,
            pr.price,
            pr.quality_grade,
            pr.unit,
            s.id as station_id,
            s.name as station_name,
            s.address as station_address,
            s.phone as station_phone,
            s.rating as station_rating,
            pr.updated_at
        FROM prices pr
        INNER JOIN stations s ON pr.station_id = s.id
        WHERE pr.product_id = #{productId} 
            AND pr.deleted = 0 
            AND pr.status = 1
            AND s.deleted = 0 
            AND s.status = 1
        <if test="stationId != null">
            AND pr.station_id = #{stationId}
        </if>
        <if test="city != null and city != ''">
            AND s.city = #{city}
        </if>
        ORDER BY pr.price DESC, s.rating DESC
    </select>

</mapper>
