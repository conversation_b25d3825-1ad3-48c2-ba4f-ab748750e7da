import React, { memo } from 'react'
import { shallowEqual, useSelector } from 'react-redux'

import { getSizeImage } from '../../../../utils/formatUtils'
import { formatMonthDay } from '../../../../utils/handleData'
import { FieldTimeOutlined } from '@ant-design/icons'
import './style.css'
export default memo(function ToplistTitle() {

  const { titleInfo } = useSelector((state: any) => ({
    titleInfo: state.getIn(['toplist', 'currentToplistTitleInfo'])
  }), shallowEqual)

  const picUrl = titleInfo && titleInfo.coverImgUrl
  const name = titleInfo && titleInfo.name
  const updateTime = titleInfo && titleInfo.trackNumberUpdateTime
  const commentCount = titleInfo && titleInfo.commentCount
  const shareCount = titleInfo && titleInfo.shareCount
  const subscribedCount = titleInfo && titleInfo.subscribedCount

  return (
    <div className="toplistTitleBox">
      <div className="toplistTitleImage">
        <img src={getSizeImage(picUrl, 150)} alt="" />
        <div className="toplistTitleImageCover"></div>
      </div>
      <div className="toplistTitleInfo">
        <h2>{name}</h2>
        <div className="toplistTitleUpdateInfo">
          <FieldTimeOutlined className="toplistTitleTimer" />最近更新: {formatMonthDay(updateTime)}
        </div>
        <div className="toplistTitleControls">
          <div className="toplistTitleButtonPlay">
            <em className="toplistTitleButtonPlayIcon"></em>
            播放
          </div>
          <div className="toplistTitleButtonFavorite">
            <i className="toplistTitleButtonInner">
              ({subscribedCount})
            </i>
          </div>
          <div className="toplistTitleButtonShare">
            <i className="toplistTitleButtonInner">
              ({shareCount})
            </i>
          </div>
          <div className="toplistTitleButtonDownload">
            <i className="toplistTitleButtonInner">
              下载
            </i>
          </div>
          <div className="toplistTitleButtonComment">
            <i className="toplistTitleButtonInner">
              ({commentCount})
            </i>
          </div>
        </div>
      </div>
    </div>

  )
})
