import * as authApi from '../../api/auth';

// Action Types
export const AUTH_LOGIN_REQUEST = 'AUTH_LOGIN_REQUEST';
export const AUTH_LOGIN_SUCCESS = 'AUTH_LOGIN_SUCCESS';
export const AUTH_LOGIN_FAILURE = 'AUTH_LOGIN_FAILURE';

export const AUTH_LOGOUT = 'AUTH_LOGOUT';

export const AUTH_REFRESH_TOKEN_REQUEST = 'AUTH_REFRESH_TOKEN_REQUEST';
export const AUTH_REFRESH_TOKEN_SUCCESS = 'AUTH_REFRESH_TOKEN_SUCCESS';
export const AUTH_REFRESH_TOKEN_FAILURE = 'AUTH_REFRESH_TOKEN_FAILURE';

export const AUTH_VERIFY_TOKEN_REQUEST = 'AUTH_VERIFY_TOKEN_REQUEST';
export const AUTH_VERIFY_TOKEN_SUCCESS = 'AUTH_VERIFY_TOKEN_SUCCESS';
export const AUTH_VERIFY_TOKEN_FAILURE = 'AUTH_VERIFY_TOKEN_FAILURE';

// Action Creators

// 微信登录
export const wechatLogin = (loginData) => {
  return async (dispatch) => {
    dispatch({ type: AUTH_LOGIN_REQUEST });
    
    try {
      const response = await authApi.wechatLogin(loginData);
      
      // 保存token到localStorage
      localStorage.setItem('token', response.token);
      localStorage.setItem('refreshToken', response.refreshToken);
      
      dispatch({
        type: AUTH_LOGIN_SUCCESS,
        payload: {
          user: response.user,
          token: response.token,
          refreshToken: response.refreshToken,
          isNewUser: response.isNewUser,
          expiresIn: response.expiresIn
        }
      });
      
      return response;
    } catch (error) {
      dispatch({
        type: AUTH_LOGIN_FAILURE,
        payload: error.message
      });
      throw error;
    }
  };
};

// 用户登出
export const logout = () => {
  return async (dispatch) => {
    try {
      await authApi.logout();
    } catch (error) {
      console.error('Logout API error:', error);
    } finally {
      // 清除本地存储
      localStorage.removeItem('token');
      localStorage.removeItem('refreshToken');
      
      dispatch({ type: AUTH_LOGOUT });
    }
  };
};

// 刷新Token
export const refreshToken = () => {
  return async (dispatch) => {
    dispatch({ type: AUTH_REFRESH_TOKEN_REQUEST });
    
    try {
      const refreshToken = localStorage.getItem('refreshToken');
      if (!refreshToken) {
        throw new Error('No refresh token available');
      }
      
      const response = await authApi.refreshToken(refreshToken);
      
      // 更新token
      localStorage.setItem('token', response.token);
      localStorage.setItem('refreshToken', response.refreshToken);
      
      dispatch({
        type: AUTH_REFRESH_TOKEN_SUCCESS,
        payload: {
          token: response.token,
          refreshToken: response.refreshToken,
          expiresIn: response.expiresIn
        }
      });
      
      return response;
    } catch (error) {
      dispatch({
        type: AUTH_REFRESH_TOKEN_FAILURE,
        payload: error.message
      });
      
      // 刷新失败，清除token并登出
      localStorage.removeItem('token');
      localStorage.removeItem('refreshToken');
      dispatch({ type: AUTH_LOGOUT });
      
      throw error;
    }
  };
};

// 验证Token
export const verifyToken = () => {
  return async (dispatch) => {
    dispatch({ type: AUTH_VERIFY_TOKEN_REQUEST });
    
    try {
      const response = await authApi.verifyToken();
      
      dispatch({
        type: AUTH_VERIFY_TOKEN_SUCCESS,
        payload: response
      });
      
      return response;
    } catch (error) {
      dispatch({
        type: AUTH_VERIFY_TOKEN_FAILURE,
        payload: error.message
      });
      
      // 验证失败，清除token
      localStorage.removeItem('token');
      localStorage.removeItem('refreshToken');
      dispatch({ type: AUTH_LOGOUT });
      
      throw error;
    }
  };
};

// 模拟登录（开发环境）
export const mockLogin = (userData) => {
  return async (dispatch) => {
    dispatch({ type: AUTH_LOGIN_REQUEST });
    
    try {
      // 模拟API调用
      const mockResponse = {
        user: {
          id: 1,
          nickname: userData.nickname || '测试用户',
          avatar: userData.avatar || '',
          phone: userData.phone || '',
          city: userData.city || '北京市'
        },
        token: 'mock-token-' + Date.now(),
        refreshToken: 'mock-refresh-token-' + Date.now(),
        isNewUser: false,
        expiresIn: 604800
      };
      
      // 保存到localStorage
      localStorage.setItem('token', mockResponse.token);
      localStorage.setItem('refreshToken', mockResponse.refreshToken);
      
      dispatch({
        type: AUTH_LOGIN_SUCCESS,
        payload: mockResponse
      });
      
      return mockResponse;
    } catch (error) {
      dispatch({
        type: AUTH_LOGIN_FAILURE,
        payload: error.message
      });
      throw error;
    }
  };
};
