# 慢慢回收小程序交互原型设计

## 🎯 交互设计原则

### 核心交互理念
- **直观易懂**：符合用户心理模型
- **快速响应**：操作反馈及时明确
- **容错性强**：减少用户操作错误
- **一致性好**：保持交互模式统一

## 📱 关键交互流程

### 1. 用户查价流程

```
用户进入首页
    ↓
点击"快速查价"
    ↓
选择输入方式
    ├── 手动输入产品名称
    ├── 拍照识别产品
    └── 扫码识别型号
    ↓
显示查询结果
    ├── 价格范围
    ├── 价格趋势
    └── 附近回收商
    ↓
用户选择操作
    ├── 联系回收商
    ├── 查看地图位置
    └── 收藏关注价格
```

### 2. 寻找回收站点流程

```
用户打开地图页面
    ↓
自动定位当前位置
    ↓
显示附近回收站点
    ↓
用户操作地图
    ├── 缩放查看更多站点
    ├── 点击站点查看信息
    └── 使用筛选功能
    ↓
选择目标站点
    ↓
查看站点详情
    ↓
选择联系方式
    ├── 拨打电话
    ├── 导航前往
    └── 在线咨询
```

### 3. 发布回收需求流程

```
用户点击"我要卖货"
    ↓
上传商品照片
    ↓
填写商品信息
    ├── 自动识别填充
    └── 手动补充信息
    ↓
选择回收方式
    ├── 上门回收
    ├── 到店回收
    └── 邮寄回收
    ↓
预约时间
    ↓
提交需求
    ↓
等待回收商响应
    ↓
确认交易详情
```

## 🎨 动效设计规范

### 页面转场动效
```css
/* 页面进入动画 */
.page-enter {
  transform: translateX(100%);
  opacity: 0;
}

.page-enter-active {
  transform: translateX(0);
  opacity: 1;
  transition: all 0.3s ease-out;
}

/* 页面退出动画 */
.page-exit {
  transform: translateX(0);
  opacity: 1;
}

.page-exit-active {
  transform: translateX(-100%);
  opacity: 0;
  transition: all 0.3s ease-in;
}
```

### 按钮交互动效
```css
/* 按钮点击效果 */
.btn-interactive {
  transition: all 0.2s ease;
  transform: scale(1);
}

.btn-interactive:active {
  transform: scale(0.95);
  opacity: 0.8;
}

/* 按钮加载状态 */
.btn-loading {
  position: relative;
  color: transparent;
}

.btn-loading::after {
  content: '';
  position: absolute;
  width: 20rpx;
  height: 20rpx;
  border: 2rpx solid #ffffff;
  border-top: 2rpx solid transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
```

### 卡片悬浮效果
```css
.card-hover {
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.card-hover:hover {
  transform: translateY(-4rpx);
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
}
```

## 📲 手势交互设计

### 地图交互手势
- **单指拖拽**：移动地图视角
- **双指缩放**：放大缩小地图
- **双击**：快速放大地图
- **长按**：显示详细地址信息

### 列表交互手势
- **下拉刷新**：更新数据内容
- **上拉加载**：加载更多数据
- **左滑操作**：快速操作菜单
- **长按选择**：多选模式

### 图片查看手势
- **单击**：查看大图
- **双击**：放大缩小图片
- **双指缩放**：自由缩放图片
- **左右滑动**：切换图片

## 🔔 反馈机制设计

### 操作反馈
```css
/* 成功提示 */
.toast-success {
  background: linear-gradient(135deg, #52C41A, #73D13D);
  color: white;
  border-radius: 8rpx;
  padding: 24rpx 32rpx;
  animation: slideInDown 0.3s ease-out;
}

/* 错误提示 */
.toast-error {
  background: linear-gradient(135deg, #FF4D4F, #FF7875);
  color: white;
  border-radius: 8rpx;
  padding: 24rpx 32rpx;
  animation: shake 0.5s ease-in-out;
}

/* 加载提示 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

@keyframes slideInDown {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-10rpx); }
  75% { transform: translateX(10rpx); }
}
```

### 状态指示
- **加载状态**：显示加载动画和进度
- **空状态**：友好的空数据提示
- **错误状态**：清晰的错误信息和解决方案
- **成功状态**：明确的成功确认信息

## 🎪 微交互设计

### 价格变化动效
```css
.price-change {
  transition: all 0.5s ease;
}

.price-up {
  color: #F5222D;
  animation: priceUp 0.8s ease-out;
}

.price-down {
  color: #52C41A;
  animation: priceDown 0.8s ease-out;
}

@keyframes priceUp {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); color: #FF4D4F; }
  100% { transform: scale(1); }
}

@keyframes priceDown {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); color: #73D13D; }
  100% { transform: scale(1); }
}
```

### 收藏动效
```css
.favorite-btn {
  transition: all 0.3s ease;
}

.favorite-active {
  color: #FF4D4F;
  animation: heartBeat 0.6s ease-in-out;
}

@keyframes heartBeat {
  0% { transform: scale(1); }
  14% { transform: scale(1.3); }
  28% { transform: scale(1); }
  42% { transform: scale(1.3); }
  70% { transform: scale(1); }
}
```

### 搜索输入动效
```css
.search-input {
  transition: all 0.3s ease;
  border: 2rpx solid #E8E8E8;
}

.search-input:focus {
  border-color: #52C41A;
  box-shadow: 0 0 0 4rpx rgba(82, 196, 26, 0.1);
  transform: scale(1.02);
}
```

## 📐 响应式设计

### 屏幕适配
```css
/* 小屏幕适配 */
@media (max-width: 375px) {
  .grid-4 {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .font-size-lg {
    font-size: 28rpx;
  }
}

/* 大屏幕适配 */
@media (min-width: 414px) {
  .container {
    max-width: 750rpx;
    margin: 0 auto;
  }
}
```

### 横屏适配
```css
@media (orientation: landscape) {
  .page-content {
    padding: var(--spacing-sm);
  }
  
  .grid {
    grid-template-columns: repeat(auto-fit, minmax(200rpx, 1fr));
  }
}
```

这套交互设计方案注重用户体验的流畅性和一致性，通过合理的动效和反馈机制，让用户在使用过程中感受到专业和贴心的服务。
