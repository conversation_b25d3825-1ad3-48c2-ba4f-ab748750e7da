@startuml 慢慢回收数据库ER关系图

!define PRIMARY_KEY(x) <b><color:#b8860b><&key></color> x</b>
!define FOREIGN_KEY(x) <color:#aaaaaa><&key></color> x
!define COLUMN(x) <color:#efefef><&media-record></color> x
!define TABLE(x) entity x << (T, #FFAAAA) >>

' 设置样式
skinparam linetype ortho
skinparam backgroundColor #FAFAFA
skinparam entity {
  BackgroundColor #E1F5FE
  BorderColor #0277BD
  FontSize 12
}

' 用户领域 (User Domain)
package "用户领域 (User Domain)" #E8F5E8 {
  TABLE(users) {
    PRIMARY_KEY(id) : int(11)
    --
    COLUMN(openid) : varchar(32) <<UK>>
    COLUMN(nickname) : varchar(20)
    COLUMN(avatar) : varchar(200)
    COLUMN(phone) : varchar(11)
    COLUMN(city) : varchar(20) <<IDX>>
    COLUMN(status) : tinyint(1)
    COLUMN(created_at) : timestamp
    COLUMN(updated_at) : timestamp
  }

  TABLE(user_addresses) {
    PRIMARY_KEY(id) : int(11)
    --
    FOREIGN_KEY(user_id) : int(11) <<FK>>
    COLUMN(name) : varchar(20)
    COLUMN(phone) : varchar(11)
    COLUMN(address) : varchar(100)
    COLUMN(lng) : decimal(9,6)
    COLUMN(lat) : decimal(9,6)
    COLUMN(is_default) : tinyint(1)
    COLUMN(created_at) : timestamp
  }
}

' 产品领域 (Product Domain)
package "产品领域 (Product Domain)" #FFF3E0 {
  TABLE(categories) {
    PRIMARY_KEY(id) : int(11)
    --
    COLUMN(parent_id) : int(11) <<IDX>>
    COLUMN(name) : varchar(20)
    COLUMN(icon) : varchar(10)
    COLUMN(sort) : int(11)
    COLUMN(status) : tinyint(1)
  }

  TABLE(products) {
    PRIMARY_KEY(id) : int(11)
    --
    FOREIGN_KEY(category_id) : int(11) <<FK>>
    COLUMN(name) : varchar(50) <<IDX>>
    COLUMN(brand) : varchar(20)
    COLUMN(model) : varchar(30)
    COLUMN(image) : varchar(200)
    COLUMN(keywords) : varchar(100)
    COLUMN(status) : tinyint(1)
    COLUMN(created_at) : timestamp
  }

  TABLE(prices) {
    PRIMARY_KEY(id) : int(11)
    --
    FOREIGN_KEY(product_id) : int(11) <<FK>>
    FOREIGN_KEY(station_id) : int(11) <<FK>>
    COLUMN(min_price) : decimal(8,2)
    COLUMN(max_price) : decimal(8,2)
    COLUMN(unit) : varchar(10)
    COLUMN(date) : date <<IDX>>
    COLUMN(created_at) : timestamp
  }
}

' 站点领域 (Station Domain)
package "站点领域 (Station Domain)" #F3E5F5 {
  TABLE(stations) {
    PRIMARY_KEY(id) : int(11)
    --
    COLUMN(name) : varchar(30)
    COLUMN(phone) : varchar(11)
    COLUMN(address) : varchar(100)
    COLUMN(lng) : decimal(9,6) <<IDX>>
    COLUMN(lat) : decimal(9,6) <<IDX>>
    COLUMN(city) : varchar(20) <<IDX>>
    COLUMN(hours) : varchar(50)
    COLUMN(image) : varchar(200)
    COLUMN(rating) : decimal(3,2) <<IDX>>
    COLUMN(review_count) : int(11)
    COLUMN(status) : tinyint(1)
    COLUMN(created_at) : timestamp
  }

  TABLE(station_services) {
    PRIMARY_KEY(id) : int(11)
    --
    FOREIGN_KEY(station_id) : int(11) <<FK>>
    FOREIGN_KEY(category_id) : int(11) <<FK>>
    COLUMN(min_price) : decimal(8,2)
    COLUMN(max_price) : decimal(8,2)
    COLUMN(unit) : varchar(10)
    COLUMN(status) : tinyint(1)
  }

  TABLE(reviews) {
    PRIMARY_KEY(id) : int(11)
    --
    FOREIGN_KEY(user_id) : int(11) <<FK>>
    FOREIGN_KEY(station_id) : int(11) <<FK>>
    COLUMN(rating) : tinyint(1)
    COLUMN(content) : varchar(200)
    COLUMN(images) : json
    COLUMN(status) : tinyint(1)
    COLUMN(created_at) : timestamp
  }
}

' 行为领域 (Behavior Domain)
package "行为领域 (Behavior Domain)" #E0F2F1 {
  TABLE(search_logs) {
    PRIMARY_KEY(id) : int(11)
    --
    FOREIGN_KEY(user_id) : int(11) <<FK>>
    COLUMN(keyword) : varchar(50) <<IDX>>
    COLUMN(type) : tinyint(1)
    COLUMN(result_count) : int(11)
    COLUMN(created_at) : timestamp
  }

  TABLE(favorites) {
    PRIMARY_KEY(id) : int(11)
    --
    FOREIGN_KEY(user_id) : int(11) <<FK>>
    COLUMN(type) : tinyint(1)
    COLUMN(target_id) : int(11)
    COLUMN(created_at) : timestamp
    --
    <<UK>> (user_id, type, target_id)
  }

  TABLE(browse_logs) {
    PRIMARY_KEY(id) : int(11)
    --
    FOREIGN_KEY(user_id) : int(11) <<FK>>
    COLUMN(type) : tinyint(1)
    COLUMN(target_id) : int(11)
    COLUMN(duration) : int(11)
    COLUMN(created_at) : timestamp
  }
}

' 系统领域 (System Domain)
package "系统领域 (System Domain)" #FFF8E1 {
  TABLE(configs) {
    PRIMARY_KEY(id) : int(11)
    --
    COLUMN(key) : varchar(50) <<UK>>
    COLUMN(value) : text
    COLUMN(desc) : varchar(100)
    COLUMN(type) : varchar(20)
    COLUMN(status) : tinyint(1)
    COLUMN(updated_at) : timestamp
  }
}

' 定义关系
' 用户领域关系
users ||--o{ user_addresses : "1:N"

' 产品领域关系
categories ||--o{ categories : "parent_id"
categories ||--o{ products : "1:N"
products ||--o{ prices : "1:N"

' 站点领域关系
stations ||--o{ station_services : "1:N"
stations ||--o{ reviews : "1:N"
stations ||--o{ prices : "1:N"
categories ||--o{ station_services : "1:N"

' 行为领域关系
users ||--o{ search_logs : "1:N"
users ||--o{ favorites : "1:N"
users ||--o{ browse_logs : "1:N"
users ||--o{ reviews : "1:N"

' 跨领域关系说明
note top of users : 用户是整个系统的核心\n所有行为数据都关联到用户
note top of categories : 分类支持树形结构\nparent_id指向父分类
note top of prices : 价格表连接产品和站点\nstation_id为空表示市场均价
note top of favorites : 统一收藏表设计\ntype区分收藏对象类型
note top of configs : 系统配置表\n支持动态配置管理

' 添加图例
legend top left
  |= 符号说明 |= 含义 |
  | <b><color:#b8860b><&key></color></b> | 主键 (Primary Key) |
  | <color:#aaaaaa><&key></color> | 外键 (Foreign Key) |
  | <color:#efefef><&media-record></color> | 普通字段 |
  | <<UK>> | 唯一索引 (Unique Key) |
  | <<IDX>> | 普通索引 (Index) |
  | <<FK>> | 外键约束 (Foreign Key) |
endlegend

' 添加标题和说明
title 慢慢回收小程序数据库ER关系图
center footer 版本: V2.0 | 创建日期: 2024-01-15 | 设计原则: 领域驱动 + 字段简化

@enduml
