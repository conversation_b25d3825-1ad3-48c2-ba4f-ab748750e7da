package com.c2crestore.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.c2crestore.entity.Product;
import com.c2crestore.vo.ProductVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 产品Mapper接口
 * 
 * <AUTHOR> Restore Team
 * @since 2024-01-15
 */
@Mapper
public interface ProductMapper extends BaseMapper<Product> {

    /**
     * 搜索产品
     * 
     * @param page 分页参数
     * @param keyword 关键词
     * @param categoryId 分类ID
     * @return 产品列表
     */
    @Select("<script>" +
            "SELECT p.*, c.name as category_name, c.icon as category_icon " +
            "FROM products p " +
            "LEFT JOIN categories c ON p.category_id = c.id " +
            "WHERE p.deleted = 0 AND p.status = 1 " +
            "<if test='keyword != null and keyword != \"\"'>" +
            "AND (p.name LIKE CONCAT('%', #{keyword}, '%') " +
            "OR p.brand LIKE CONCAT('%', #{keyword}, '%') " +
            "OR p.model LIKE CONCAT('%', #{keyword}, '%') " +
            "OR p.keywords LIKE CONCAT('%', #{keyword}, '%')) " +
            "</if>" +
            "<if test='categoryId != null'>" +
            "AND p.category_id = #{categoryId} " +
            "</if>" +
            "ORDER BY p.created_at DESC" +
            "</script>")
    IPage<ProductVO> searchProducts(
            Page<ProductVO> page,
            @Param("keyword") String keyword,
            @Param("categoryId") Long categoryId
    );

    /**
     * 获取产品详情
     * 
     * @param productId 产品ID
     * @return 产品详情
     */
    @Select("SELECT p.*, c.name as category_name, c.icon as category_icon " +
            "FROM products p " +
            "LEFT JOIN categories c ON p.category_id = c.id " +
            "WHERE p.id = #{productId} AND p.deleted = 0")
    ProductVO selectProductDetail(@Param("productId") Long productId);

    /**
     * 获取热门产品
     * 
     * @param categoryId 分类ID
     * @param limit 数量限制
     * @return 热门产品列表
     */
    @Select("<script>" +
            "SELECT p.*, c.name as category_name, c.icon as category_icon, " +
            "(SELECT COUNT(*) FROM favorites f WHERE f.type = 2 AND f.target_id = p.id AND f.deleted = 0) as favorite_count " +
            "FROM products p " +
            "LEFT JOIN categories c ON p.category_id = c.id " +
            "WHERE p.status = 1 AND p.deleted = 0 " +
            "<if test='categoryId != null'> AND p.category_id = #{categoryId} </if>" +
            "ORDER BY favorite_count DESC, p.created_at DESC " +
            "LIMIT #{limit}" +
            "</script>")
    List<ProductVO> selectHotProducts(@Param("categoryId") Long categoryId, @Param("limit") Integer limit);

    /**
     * 获取产品价格信息
     * 
     * @param productId 产品ID
     * @param stationId 站点ID
     * @param city 城市
     * @return 价格信息
     */
    @Select("<script>" +
            "SELECT pr.*, s.name as station_name, s.city as station_city " +
            "FROM prices pr " +
            "LEFT JOIN stations s ON pr.station_id = s.id " +
            "WHERE pr.product_id = #{productId} AND pr.deleted = 0 " +
            "<if test='stationId != null'> AND pr.station_id = #{stationId} </if>" +
            "<if test='city != null and city != \"\"'> AND s.city = #{city} </if>" +
            "ORDER BY pr.date DESC" +
            "</script>")
    List<Object> selectProductPrices(
            @Param("productId") Long productId,
            @Param("stationId") Long stationId,
            @Param("city") String city
    );
}
