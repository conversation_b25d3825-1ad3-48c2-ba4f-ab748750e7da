package com.manmanrecycle.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.manmanrecycle.entity.RealTimePrice;
import com.manmanrecycle.vo.PriceVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.util.List;

/**
 * 实时价格Mapper接口
 * 
 * <AUTHOR> Team
 * @since 2024-01-15
 */
@Mapper
public interface RealTimePriceMapper extends BaseMapper<RealTimePrice> {

    /**
     * 获取实时价格列表
     * 
     * @param productIds 产品ID列表
     * @param stationIds 站点ID列表
     * @param priceType 价格类型
     * @param city 城市
     * @param qualityGrade 品质等级
     * @return 价格列表
     */
    List<PriceVO> selectRealTimePrices(
            @Param("productIds") List<Long> productIds,
            @Param("stationIds") List<Long> stationIds,
            @Param("priceType") Integer priceType,
            @Param("city") String city,
            @Param("qualityGrade") String qualityGrade
    );

    /**
     * 获取产品当前价格
     * 
     * @param productId 产品ID
     * @param priceType 价格类型
     * @return 当前价格
     */
    @Select("SELECT * FROM real_time_prices " +
            "WHERE product_id = #{productId} AND price_type = #{priceType} " +
            "AND station_id IS NULL AND deleted = 0 " +
            "ORDER BY last_updated DESC LIMIT 1")
    RealTimePrice selectCurrentPrice(@Param("productId") Long productId, @Param("priceType") Integer priceType);

    /**
     * 获取站点产品价格
     * 
     * @param productId 产品ID
     * @param stationId 站点ID
     * @param priceType 价格类型
     * @return 站点价格
     */
    @Select("SELECT * FROM real_time_prices " +
            "WHERE product_id = #{productId} AND station_id = #{stationId} " +
            "AND price_type = #{priceType} AND deleted = 0 " +
            "ORDER BY last_updated DESC LIMIT 1")
    RealTimePrice selectStationPrice(@Param("productId") Long productId, 
                                   @Param("stationId") Long stationId, 
                                   @Param("priceType") Integer priceType);

    /**
     * 获取价格变化率
     * 
     * @param productId 产品ID
     * @param priceType 价格类型
     * @param hours 小时数
     * @return 变化率
     */
    @Select("SELECT " +
            "CASE WHEN prev.current_price > 0 THEN " +
            "((curr.current_price - prev.current_price) / prev.current_price) * 100 " +
            "ELSE 0 END as change_rate " +
            "FROM real_time_prices curr " +
            "LEFT JOIN real_time_prices prev ON curr.product_id = prev.product_id " +
            "AND prev.last_updated <= DATE_SUB(curr.last_updated, INTERVAL #{hours} HOUR) " +
            "WHERE curr.product_id = #{productId} AND curr.price_type = #{priceType} " +
            "AND curr.station_id IS NULL AND curr.deleted = 0 " +
            "ORDER BY curr.last_updated DESC, prev.last_updated DESC " +
            "LIMIT 1")
    BigDecimal selectPriceChangeRate(@Param("productId") Long productId, 
                                   @Param("priceType") Integer priceType, 
                                   @Param("hours") Integer hours);

    /**
     * 获取热门产品价格
     * 
     * @param city 城市
     * @param limit 数量限制
     * @return 热门产品价格列表
     */
    List<PriceVO> selectHotProductPrices(@Param("city") String city, @Param("limit") Integer limit);

    /**
     * 获取价格异常数据
     * 
     * @param deviationThreshold 偏差阈值
     * @param hours 时间范围(小时)
     * @return 异常价格列表
     */
    List<PriceVO> selectAnomalyPrices(@Param("deviationThreshold") BigDecimal deviationThreshold, 
                                    @Param("hours") Integer hours);

    /**
     * 批量更新价格
     * 
     * @param prices 价格列表
     * @return 影响行数
     */
    int batchUpdatePrices(@Param("prices") List<RealTimePrice> prices);

    /**
     * 清理过期价格数据
     * 
     * @param days 保留天数
     * @return 删除行数
     */
    @Select("DELETE FROM real_time_prices WHERE last_updated < DATE_SUB(NOW(), INTERVAL #{days} DAY)")
    int cleanExpiredPrices(@Param("days") Integer days);
}
