# 🌟 c2cRecycle - C2C回收平台后端服务

基于Spring Boot + MyBatis Plus构建的C2C回收平台后端API服务。

## 🚀 快速开始

### 环境要求
- **JDK 8+**
- **Maven 3.6+**
- **MySQL 8.0+**
- **Redis 6.0+**

### 启动步骤

#### 1. 克隆项目
```bash
git clone <repository-url>
cd c2cRecycle
```

#### 2. 数据库准备
```sql
-- 创建数据库
CREATE DATABASE c2c_recycle;

-- 执行表结构脚本
SOURCE src/main/resources/sql/schema.sql;

-- 执行数据初始化脚本  
SOURCE src/main/resources/sql/data.sql;
```

#### 3. 配置修改
编辑 `src/main/resources/application.yml`：
```yaml
spring:
  datasource:
    url: ***************************************
    username: your_username
    password: your_password
  redis:
    host: localhost
    port: 6379
```

#### 4. 启动应用
```bash
# Maven方式启动
mvn spring-boot:run

# 或者编译后启动
mvn clean package
java -jar target/c2c-recycle-api.jar
```

#### 5. 访问应用
- **API文档**: http://localhost:8080/api/doc.html
- **Swagger**: http://localhost:8080/api/swagger-ui/
- **监控面板**: http://localhost:8080/api/actuator
- **数据库监控**: http://localhost:8080/api/druid

## 📋 项目结构

```
c2cRecycle/
├── src/main/java/com/c2crecycle/
│   ├── C2cRecycleApplication.java          # 主启动类
│   ├── controller/                         # 控制器层
│   ├── service/                           # 服务层
│   ├── mapper/                            # 数据访问层
│   ├── entity/                            # 实体类
│   ├── dto/                               # 数据传输对象
│   ├── vo/                                # 视图对象
│   ├── config/                            # 配置类
│   ├── common/                            # 公共组件
│   └── util/                              # 工具类
├── src/main/resources/
│   ├── application.yml                     # 应用配置
│   ├── mapper/                            # MyBatis XML
│   └── sql/                               # 数据库脚本
├── pom.xml                                # Maven配置
└── README.md                              # 项目说明
```

## 🔧 技术栈

### 核心框架
- **Spring Boot 2.7.8** - 应用框架
- **Spring Web** - Web框架
- **Spring Security** - 安全框架
- **Spring Data Redis** - Redis集成

### 数据访问
- **MyBatis Plus 3.5.3** - ORM框架
- **Druid 1.2.15** - 数据库连接池
- **MySQL 8.0.32** - 数据库驱动

### 工具库
- **Hutool 5.8.11** - Java工具类库
- **Lombok** - 代码简化
- **Jackson** - JSON处理
- **JWT** - Token认证

### 文档和监控
- **Swagger 3.0** - API文档
- **Knife4j 3.0.3** - 增强文档
- **Actuator** - 应用监控

## 📊 API接口

### 认证模块
- `POST /api/auth/wechat-login` - 微信登录
- `POST /api/auth/refresh-token` - 刷新Token
- `POST /api/auth/logout` - 用户登出
- `GET /api/auth/verify` - Token验证

### 产品模块
- `GET /api/products/categories` - 获取产品分类
- `GET /api/products/search` - 搜索产品
- `GET /api/products/{id}` - 获取产品详情
- `GET /api/products/hot` - 获取热门产品
- `GET /api/products/{id}/prices` - 获取产品价格

### 站点模块
- `GET /api/stations/nearby` - 获取附近站点
- `GET /api/stations/search` - 搜索站点
- `GET /api/stations/{id}` - 获取站点详情
- `GET /api/stations/hot` - 获取热门站点

### 用户模块
- `GET /api/user/profile` - 获取用户信息
- `PUT /api/user/profile` - 更新用户信息
- `GET /api/user/addresses` - 获取地址列表
- `POST /api/user/addresses` - 添加地址
- `PUT /api/user/addresses/{id}` - 更新地址
- `DELETE /api/user/addresses/{id}` - 删除地址
- `GET /api/user/favorites` - 获取收藏列表
- `POST /api/user/favorites` - 添加收藏
- `DELETE /api/user/favorites/{id}` - 取消收藏

## 🗄️ 数据库设计

### 核心表结构
- **users** - 用户表
- **categories** - 产品分类表
- **products** - 产品表
- **stations** - 回收站点表
- **prices** - 价格表
- **user_addresses** - 用户地址表
- **favorites** - 收藏表

### 特性
- ✅ 完整的外键约束
- ✅ 逻辑删除支持
- ✅ 自动时间戳
- ✅ 索引优化
- ✅ 地理位置支持

## 🔒 安全特性

- **JWT认证** - 无状态Token认证
- **权限控制** - 基于角色的访问控制
- **参数验证** - 请求参数自动验证
- **SQL注入防护** - MyBatis参数化查询
- **跨域支持** - CORS配置

## 📈 性能优化

- **连接池** - Druid高性能连接池
- **缓存** - Redis缓存热点数据
- **分页** - MyBatis Plus分页插件
- **索引** - 数据库查询优化
- **监控** - 完整的性能监控

## 🚀 部署

### 开发环境
```bash
mvn spring-boot:run
```

### 生产环境
```bash
# 打包
mvn clean package -Pprod

# 启动
java -jar target/c2c-recycle-api.jar --spring.profiles.active=prod
```

### Docker部署
```dockerfile
FROM openjdk:8-jre-alpine
COPY target/c2c-recycle-api.jar app.jar
EXPOSE 8080
ENTRYPOINT ["java", "-jar", "/app.jar"]
```

## 📝 开发规范

### 代码规范
- 使用Lombok简化代码
- 统一的异常处理
- 完整的API文档注解
- 规范的包结构

### 数据库规范
- 统一的命名规范
- 完整的字段注释
- 合理的索引设计
- 逻辑删除机制

## 🤝 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 📄 许可证

MIT License

---

**🎉 c2cRecycle - 让回收更简单！**
