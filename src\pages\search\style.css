.searchBox {
    padding: 40px;
    height: 1050px;
    background-color: #fff;
    border: 1px solid #d3d3d3;
    border-width: 0 1px;
    margin: 0 auto;
}

.searchBox .search {
    display: flex;
    justify-content: center;
}
.searchBox .search  .ant-input-search {
    border-radius: 8px;
    height: 42px;
    border-color: #c9c9c9;
}

.searchBox .searchContent .searchInfo {
    margin: 28px 0 17px;
    color: #999;
}
.searchBox .searchContent .searchInfo .searchAmount {
    color: #c20c0c;
}
.searchBox .searchContent .searchCategory {
    display: flex;
    height: 39px;
    border: 1px solid #ccc;
    border-width: 0 1px;
    background: url(../../static/images/tab.png) repeat-x 0 0;
}
.searchBox .searchContent .searchCategory .searchRouteItem {
    width: 112px;
    height: 37px;
    text-align: center;
    font-size: 14px;
}
.searchBox .searchContent .searchCategory .active {
    background: url(../../static/images/tab.png) repeat-x left -90px;
    border-right: solid 1px #ccc;
}
.searchBox .searchContent .searchCategory .searchRouteItem:hover {
    background: url(../../static/images/tab.png) repeat-x left -90px;
    border-right: solid 1px #ccc;
    text-decoration: none;
}
.searchBox .searchContent .searchCategory .searchRouteItem em {
    display: block;
    width: 108px;
    padding: 2px 2px 0 0;
    line-height: 37px;
    cursor: pointer;
    text-align: center;
}