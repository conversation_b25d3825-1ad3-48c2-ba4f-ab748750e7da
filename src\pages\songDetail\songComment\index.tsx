import React, { memo, useEffect, useState, createElement, useCallback } from 'react'
import { shallowEqual, useDispatch, useSelector } from 'react-redux'
import { Comment, Tooltip, Avatar, message } from 'antd'
import { LikeFilled, LikeOutlined } from '@ant-design/icons'
import Pagination from '../../../components/pagination'
import ThemeHeader from '../../../components/themeHeader'
import { changeCurrentCommentTotal, getHotCommentAction } from '../../../components/playerBar/store/actionCreators'
import { changeIsVisible } from '../../../components/themeLogin/store'
import { getCount } from '../../../utils/formatUtils'
import { getSongComment, sendSongComment } from '../../../request/player'
import ThemeComment from '../../../components/themeComment'
import { sendLikeComment } from '../../../request/songList'
import './style.css'

function SongComment() {
  const [songComment, setSongComment] = useState([])
  const [currentPage, setCurrentPage] = useState(1)
  const [total, setTotal] = useState(0)
  const [flag, setFlag] = useState(false)
  const [liked, setLiked]: any = useState([]) // 歌曲的点赞状态

  const dispatch = useDispatch()
  const { hotComments, currentSongId, isLogin, cookie, avatarUrl } = useSelector(
    (state: any) => ({
      hotComments: state.getIn(['player', 'hotComments']),
      currentSongId: state.getIn(['player', 'currentSong', 'id']),
      isLogin: state.getIn(['loginState', 'isLogin']),
      cookie: state.getIn(['loginState', 'cookie']),
      avatarUrl: state.getIn(['loginState', 'profile', 'avatarUrl']),
    }),
    shallowEqual
  )

  // other hooks
  useEffect(() => {
    dispatch(getHotCommentAction(currentSongId))
    getSongComment(currentSongId).then((res: any) => {
      setSongComment(res.comments)
      setTotal(res.total)
      dispatch(changeCurrentCommentTotal(res.total))
    })
  }, [dispatch, currentSongId])

  // other handle
  function formatDate(time = +new Date()) {
    var date = new Date(time + 8 * 3600 * 1000) // 增加8小时
    return date.toJSON().substr(0, 19).replace('T', ' ')
  }
  // 点赞评论
  const likeComment = (index: any, data: any) => {
    if (!isLogin) { // 没登陆
      dispatch(changeIsVisible(true))
    }
    if (!flag) {
      liked[index].liked = true
      liked[index].count += 1
      setLiked(liked)
      /* 调点赞接口 */
      sendLikeComment(currentSongId, data.commentId, 1, cookie).then((res: any) => {
        if (res.code === 200) message.success('点赞成功')
        else message.success('请稍后再试')
      })
    } else {
      liked[index].liked = false
      liked[index].count -= 1
      setLiked(liked)
      setFlag(true)
      /* 调取消点赞接口 */
      sendLikeComment(currentSongId, data.commentId, 0, cookie).then((res: any) => {
        if (res.code === 200) message.success('取消点赞成功')
        else message.success('取消点赞成功')
      })
    }
    setFlag(!flag)
  }

  // 分页
  const changePage = useCallback(
    (currentPage) => {
      setCurrentPage(currentPage)
      // offset=(当前页数-1)*limit
      const targePageCount = (currentPage - 1) * 20
      getSongComment(currentSongId, 20, targePageCount).then((res: any) => {
        setSongComment(res.comments)
        setTotal(res.total)
      })
    },
    [currentSongId]
  )

  // 点赞HTML
  const getLikeTemplateAction = (item: any, index: any) => {
    liked.push({
      liked: item.liked,
      count: item.likedCount,
    })
    return [
      <Tooltip key="commentlike" title="Like" className="songCommentLike">
        <span onClick={() => likeComment(index, item)}>
          {createElement(
            liked[index].liked === true ? LikeFilled : LikeOutlined
          )}
          <span className="songCommentAction">{getCount(liked[index].count)}</span>
        </span>
      </Tooltip>,
    ]
  }
  // 评论歌曲校验(获取焦点)
  const commentSongcheckout = () => {
    // 没登录
    if (!isLogin) dispatch(changeIsVisible(true))
  }

  // 评论成功
  const commentCallbackOk = (value: any) => {
    sendSongComment(currentSongId, value, cookie).then((res: any) => {
      if (res.code === 200) message.success('评论成功').then(() => {
        getSongComment(currentSongId).then((res: any) => {
          setSongComment(res.comments)
          setTotal(res.total)
        })
      })
    })
  }

  return (
    <div className="songCommentBox">
      <ThemeHeader title="评论" />
      {/* 评论内容 */}
      <ThemeComment
        onFocus={() => commentSongcheckout()}
        callbackOk={(value: any) => commentCallbackOk(value)}
        isLogin={isLogin}
        photo={avatarUrl}
      />
      {/* 精彩评论 */}
      <div className="songCommentWonderful">
        <div className="songCommentHeader">精彩评论</div>
        {hotComments &&
          hotComments.map((item: any, index: any) => {
            return (
              <Comment
                // actions={getLikeTemplateAction(item, index)}
                key={item.commentId}
                author={item.user.nickname}
                avatar={<Avatar src={item.user.avatarUrl} alt="KK" />}
                content={<p>{item.content}</p>}
                datetime={
                  <Tooltip title={formatDate(item.time)}>
                    {formatDate(item.time).slice(
                      0,
                      formatDate(item.time).indexOf(' ')
                    )}
                  </Tooltip>
                }
              />
            )
          })}
      </div>
      {/* 最新评论 */}
      <div className="songCommentNew">
        <div className="header-comment">最新评论</div>
        {songComment &&
          songComment.map((item: any, index: any) => {
            return (
              <Comment
                actions={getLikeTemplateAction(item, index)}
                key={item.commentId}
                author={item.user.nickname}
                avatar={<Avatar src={item.user.avatarUrl} alt="Han Solo" />}
                content={<p>{item.content}</p>}
                datetime={
                  <Tooltip title={formatDate(item.time)}>
                    {formatDate(item.time).slice(0, parseInt(formatDate(item.time)))}
                  </Tooltip>
                }
              />
            )
          })}</div>
      {/* 分页 */}
      <Pagination
        currentPage={currentPage}
        pageSize={20}
        total={total}
        onPageChange={(currentPage: any) => changePage(currentPage)}
      />
    </div>
  )
}

export default memo(SongComment)
