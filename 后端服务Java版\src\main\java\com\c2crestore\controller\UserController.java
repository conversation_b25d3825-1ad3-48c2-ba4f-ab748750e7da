package com.c2crestore.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.c2crestore.common.result.Result;
import com.c2crestore.dto.AddressDTO;
import com.c2crestore.dto.FavoriteDTO;
import com.c2crestore.dto.UpdateUserDTO;
import com.c2crestore.entity.UserAddress;
import com.c2crestore.service.UserService;
import com.c2crestore.vo.FavoriteVO;
import com.c2crestore.vo.UserProfileVO;
import io.swagger.annotations.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;

/**
 * 用户控制器
 * 
 * <AUTHOR> Restore Team
 * @since 2024-01-15
 */
@Slf4j
@RestController
@RequestMapping("/user")
@RequiredArgsConstructor
@Api(tags = "👤 用户模块", description = "用户信息和个人中心相关接口")
public class UserController {

    private final UserService userService;

    /**
     * 获取用户信息
     */
    @GetMapping("/profile")
    @ApiOperation(value = "获取用户信息", notes = "获取当前登录用户的个人信息")
    @ApiResponses({
        @ApiResponse(code = 200, message = "获取成功"),
        @ApiResponse(code = 401, message = "未授权访问"),
        @ApiResponse(code = 404, message = "用户不存在")
    })
    public Result<UserProfileVO> getUserProfile(HttpServletRequest request) {
        Long userId = (Long) request.getAttribute("userId");
        log.info("获取用户信息，userId: {}", userId);
        
        UserProfileVO profile = userService.getUserProfile(userId);
        
        return Result.success("获取用户信息成功", profile);
    }

    /**
     * 更新用户信息
     */
    @PutMapping("/profile")
    @ApiOperation(value = "更新用户信息", notes = "更新当前登录用户的个人信息")
    @ApiResponses({
        @ApiResponse(code = 200, message = "更新成功"),
        @ApiResponse(code = 400, message = "请求参数错误"),
        @ApiResponse(code = 401, message = "未授权访问")
    })
    public Result<Void> updateUserProfile(
            @ApiParam("用户信息更新参数") @Valid @RequestBody UpdateUserDTO updateDTO,
            HttpServletRequest request) {
        
        Long userId = (Long) request.getAttribute("userId");
        log.info("更新用户信息，userId: {}", userId);
        
        userService.updateUserProfile(userId, updateDTO);
        
        return Result.success("更新用户信息成功");
    }

    /**
     * 获取用户地址列表
     */
    @GetMapping("/addresses")
    @ApiOperation(value = "获取地址列表", notes = "获取当前用户的所有地址")
    @ApiResponses({
        @ApiResponse(code = 200, message = "获取成功"),
        @ApiResponse(code = 401, message = "未授权访问")
    })
    public Result<List<UserAddress>> getUserAddresses(HttpServletRequest request) {
        Long userId = (Long) request.getAttribute("userId");
        log.info("获取用户地址列表，userId: {}", userId);
        
        List<UserAddress> addresses = userService.getUserAddresses(userId);
        
        return Result.success("获取地址列表成功", addresses);
    }

    /**
     * 添加用户地址
     */
    @PostMapping("/addresses")
    @ApiOperation(value = "添加地址", notes = "为当前用户添加新地址")
    @ApiResponses({
        @ApiResponse(code = 200, message = "添加成功"),
        @ApiResponse(code = 400, message = "请求参数错误"),
        @ApiResponse(code = 401, message = "未授权访问")
    })
    public Result<Void> addUserAddress(
            @ApiParam("地址信息") @Valid @RequestBody AddressDTO addressDTO,
            HttpServletRequest request) {
        
        Long userId = (Long) request.getAttribute("userId");
        log.info("添加用户地址，userId: {}", userId);
        
        userService.addUserAddress(userId, addressDTO);
        
        return Result.success("添加地址成功");
    }

    /**
     * 更新用户地址
     */
    @PutMapping("/addresses/{id}")
    @ApiOperation(value = "更新地址", notes = "更新指定的用户地址")
    @ApiImplicitParam(name = "id", value = "地址ID", required = true, dataType = "long", paramType = "path")
    @ApiResponses({
        @ApiResponse(code = 200, message = "更新成功"),
        @ApiResponse(code = 400, message = "请求参数错误"),
        @ApiResponse(code = 401, message = "未授权访问"),
        @ApiResponse(code = 404, message = "地址不存在")
    })
    public Result<Void> updateUserAddress(
            @PathVariable Long id,
            @ApiParam("地址信息") @Valid @RequestBody AddressDTO addressDTO,
            HttpServletRequest request) {
        
        Long userId = (Long) request.getAttribute("userId");
        log.info("更新用户地址，userId: {}, addressId: {}", userId, id);
        
        userService.updateUserAddress(userId, id, addressDTO);
        
        return Result.success("更新地址成功");
    }

    /**
     * 删除用户地址
     */
    @DeleteMapping("/addresses/{id}")
    @ApiOperation(value = "删除地址", notes = "删除指定的用户地址")
    @ApiImplicitParam(name = "id", value = "地址ID", required = true, dataType = "long", paramType = "path")
    @ApiResponses({
        @ApiResponse(code = 200, message = "删除成功"),
        @ApiResponse(code = 401, message = "未授权访问"),
        @ApiResponse(code = 404, message = "地址不存在")
    })
    public Result<Void> deleteUserAddress(
            @PathVariable Long id,
            HttpServletRequest request) {
        
        Long userId = (Long) request.getAttribute("userId");
        log.info("删除用户地址，userId: {}, addressId: {}", userId, id);
        
        userService.deleteUserAddress(userId, id);
        
        return Result.success("删除地址成功");
    }

    /**
     * 获取用户收藏
     */
    @GetMapping("/favorites")
    @ApiOperation(value = "获取收藏列表", notes = "获取当前用户的收藏列表")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "type", value = "收藏类型", dataType = "int", paramType = "query", allowableValues = "1,2"),
        @ApiImplicitParam(name = "page", value = "页码", dataType = "int", paramType = "query", defaultValue = "1"),
        @ApiImplicitParam(name = "size", value = "每页数量", dataType = "int", paramType = "query", defaultValue = "20")
    })
    @ApiResponses({
        @ApiResponse(code = 200, message = "获取成功"),
        @ApiResponse(code = 401, message = "未授权访问")
    })
    public Result<IPage<FavoriteVO>> getUserFavorites(
            @RequestParam(required = false) Integer type,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            HttpServletRequest request) {
        
        Long userId = (Long) request.getAttribute("userId");
        log.info("获取用户收藏，userId: {}, type: {}, page: {}, size: {}", userId, type, page, size);
        
        IPage<FavoriteVO> favorites = userService.getUserFavorites(userId, type, page, size);
        
        return Result.success("获取收藏列表成功", favorites);
    }

    /**
     * 添加收藏
     */
    @PostMapping("/favorites")
    @ApiOperation(value = "添加收藏", notes = "收藏站点或产品")
    @ApiResponses({
        @ApiResponse(code = 200, message = "收藏成功"),
        @ApiResponse(code = 400, message = "请求参数错误"),
        @ApiResponse(code = 401, message = "未授权访问"),
        @ApiResponse(code = 409, message = "已收藏该项目")
    })
    public Result<Void> addFavorite(
            @ApiParam("收藏参数") @Valid @RequestBody FavoriteDTO favoriteDTO,
            HttpServletRequest request) {
        
        Long userId = (Long) request.getAttribute("userId");
        log.info("添加收藏，userId: {}, type: {}, targetId: {}", 
                userId, favoriteDTO.getType(), favoriteDTO.getTargetId());
        
        userService.addFavorite(userId, favoriteDTO);
        
        return Result.success("收藏成功");
    }

    /**
     * 取消收藏
     */
    @DeleteMapping("/favorites/{id}")
    @ApiOperation(value = "取消收藏", notes = "取消收藏指定项目")
    @ApiImplicitParam(name = "id", value = "收藏ID", required = true, dataType = "long", paramType = "path")
    @ApiResponses({
        @ApiResponse(code = 200, message = "取消收藏成功"),
        @ApiResponse(code = 401, message = "未授权访问"),
        @ApiResponse(code = 404, message = "收藏不存在")
    })
    public Result<Void> removeFavorite(
            @PathVariable Long id,
            HttpServletRequest request) {
        
        Long userId = (Long) request.getAttribute("userId");
        log.info("取消收藏，userId: {}, favoriteId: {}", userId, id);
        
        userService.removeFavorite(userId, id);
        
        return Result.success("取消收藏成功");
    }
}
