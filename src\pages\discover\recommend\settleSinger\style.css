.settleSinger {
    margin-top: 15px;
}
.settleSinger .settleSingerHeader {
    display: flex;
    justify-content: space-between;
    height: 24px;
    margin: 0 20px;
    border-bottom: 1px solid #ccc;
}
.settleSinger .settleSingerHeader .settleSingerMore {
    font-weight: bold;
    color: #333;
}
.settleSinger .settleSingerHeader .settleSingerTitle {
    color: #666;
}

.settleSinger .settleSingerList {
    margin: 6px 0 14px 20px;
}

.settleSinger .settleSingerList .settleSingerItem {
    display: flex;
    margin-top: 14px;
    width: 210px;
    height: 62px;
    background: #fafafa;
}
.settleSinger .settleSingerList .settleSingerItem:hover {
    text-decoration: none;
}
.settleSinger .settleSingerList .settleSingerItem .settleSingerInfo  {
    width: 100%;
    height: 100%;
    padding: 4px 9px;
    border: 1px solid #e9e9e9;
    border-left: none;
}
.settleSinger .settleSingerList .settleSingerItem .settleSingerInfo .settleSingerInfoEnglishName {
    font-weight: bold;
    font-size: 14px;
    color: #333;
    margin-top: 4px;
    margin-bottom: 4px;
}
.settleSinger .settleSingerList .settleSingerItem .settleSingerInfo .settleSingerInfoDetails {
    margin-top: 5px;
}
.settleSinger .applyfor {
    margin: 12px 20px 0;
    
}
.settleSinger .applyfor a {
    color: #333;
    font-weight: 700;
    text-align: center;
    display: block;
    height: 31px;
    line-height: 31px;
    border-radius: 4px;
    background-color: #fafafa;
    border: 1px solid #c3c3c3;
    text-decoration: none;
}