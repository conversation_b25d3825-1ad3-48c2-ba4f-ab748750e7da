import React, { memo, useEffect, useState } from 'react'
import { shallowEqual, useDispatch, useSelector } from 'react-redux'
import { getSearchSongListAction } from '../store/actionCreators'
import { formatMinuteSecond } from '../../../utils/handleData'
import './style.css'
import SingleSongItem from './singleItem'
import { useSearchParams } from 'react-router-dom'
import KKPagination from '../../../components/pagination'
export default memo(function Single(props) {
  // props/state
  const [currentPage, setCurrentPage] = useState(1)
  const [searchParams, setSearchParams] = useSearchParams();
  const onPageChange = (page: any) => {
    setCurrentPage(page);
    dispatch(getSearchSongListAction(song, 20, page))
  }
  const type: any = searchParams.get('type');
  const song = searchParams.get('song');
  // redux hook
  const dispatch = useDispatch()
  const { singleSongList } = useSelector(
    (state: any) => ({
      singleSongList: state.getIn(['search', 'singleSongList']),
    }),
    shallowEqual
  )
  // (根据歌曲名字发送网络请求)
  useEffect(() => {
    // 传递歌曲发送网络请求
    if (song) dispatch(getSearchSongListAction(song, 20, currentPage, type))
  }, [dispatch, song, type, currentPage, props])

  return (
    <div className="singleBox">
      {singleSongList && singleSongList.map((item: any) => {
        return (
          <SingleSongItem
            key={item.id}
            songId={item.id}
            songName={item.name}
            singer={item.artists[0].name}
            album={item.album.name}
            duration={formatMinuteSecond(item.duration)}
          />
        )
      })}
      <KKPagination currentPage={currentPage}
        total={500}
        onPageChange={onPageChange} />
    </div>
  )
})
