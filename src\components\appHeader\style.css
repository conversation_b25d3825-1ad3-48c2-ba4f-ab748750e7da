.appHeaderBox {
    width: 100%;
    background-color: #242221;
}

.appHeaderBox .appHeaderContent {
    display: flex;
    justify-content: space-between;
    color: #fff;
}

.appHeaderBox .appHeaderRedLine {
    height: 5px;
    background-color: #c20b0b;
}


.appHeaderLeft {
    display: flex;
}

.appHeaderLeft .appHeaderLogo {
    display: inline-block;
    text-align: center;
    width: 176px;
    height: 70px;
    background-image: url('../../static/images/sprite_01.png');
    background-position: 0 0;
}

.appHeaderLeft .appHeaderGroup {
    display: flex;
}

.appHeaderLeft .appHeaderGroup a {
    font-size: 14px;
    display: flex;
    color: rgb(204, 204, 204);
}

.appHeaderLeft .appHeaderGroup .appHeaderItem {
    position: relative;
    padding: 0 19px;
    height: 80px;
    text-align: center;
    line-height: 80px;
}
.appHeaderLeft .appHeaderGroup .appHeaderItem:hover {
    text-decoration: none;
    background-color: #000;
}


.appHeaderLeft .appHeaderGroup .appHeaderItem:last-of-type {
    position: relative;
}

.appHeaderLeft .appHeaderGroup .appHeaderItem:last-of-type::after {
    position: absolute;
    content: '';
    width: 28px;
    height: 19px;
    background-image: url('../../static/images/sprite_01.png');
    background-position: -192px 0;
    top: 20px;
    right: -20px;
    
}

.appHeaderLeft .appHeaderGroup .active {
    color: rgb(255, 255, 255);
    background-color: #000;
}
.appHeaderLeft .appHeaderGroup .active .appHeaderItemIcon {
    position: absolute;
    width: 12px;
    height: 7px;
    bottom: -1px;
    left: 50%;
    transform: translate(-50%, 0);
    background-image: url('../../static/images/sprite_01.png');
    background-position: 254px 0;
}

.appHeaderRight {
  display: flex;
  align-items: center;
  color: #ccc;
  font-size: 12px;
}
.appHeaderRight  .appHeaderSearchBox {
    position: relative;
}

.appHeaderRight  .appHeaderSearchBox .appHeaderSearch {
    border-radius: 16px;
}     
.appHeaderRight  .appHeaderSearchBox .appHeaderSearch input {
    font-size: 14px;
    font-family: '微软雅黑';
}
.appHeaderRight  .appHeaderSearchBox .appHeaderSearch input::placeholder {
    font-size: 12px;
}
.appHeaderRight  .appHeaderSearchBox .appHeaderSearchDownSlider {
    position: absolute;
    top: 36px;
    left: 0;
    right: 0;
    width: 237px;
    z-index: 999;
    border: 1px solid #bebebe;
    border-radius: 4px;
    background: #fff;
    box-shadow: 0 4px 7px #555;
    text-shadow: 0 1px 0 rgba(255, 255, 255, 0.9);
}
.appHeaderRight  .appHeaderSearchBox .appHeaderSearchDownSlider .appHeaderSearchHeader {
    height: 35px;
}

.appHeaderRight  .appHeaderSearchBox .appHeaderSearchDownSlider .appHeaderSearchHeader .appHeaderSearchDiscover {
    display: inline-block;
    padding-top: 10px;
    padding-left: 10px;
}

.appHeaderRight  .appHeaderSearchBox .appHeaderSearchDownSlider .appHeaderSearchContent {
    display: flex;
    border: 1px solid rgb(183, 183, 187);
}
.appHeaderRight  .appHeaderSearchBox .appHeaderSearchDownSlider .appHeaderSearchContent .appHeaderSearchContentLeft {
    width: 65px;
    padding-top: 10px;
    border-bottom: none;
}
.appHeaderRight  .appHeaderSearchBox .appHeaderSearchDownSlider .appHeaderSearchContent .appHeaderSearchContentLeft .appHeaderSearchSong {
    color: #ccc;
    margin-left: 28px;
}

.appHeaderRight  .appHeaderSearchBox .appHeaderSearchDownSlider .appHeaderSearchContent .appHeaderSearchContentMain {
    display: inline-block;
    font-size: 13px;
    line-height: 28px;
}
.appHeaderRight  .appHeaderSearchBox .appHeaderSearchDownSlider .appHeaderSearchContent .appHeaderSearchContentMain .appHeaderSearchContentMainItem {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 168px;
    cursor: pointer;
    height: 35px;
    line-height: 35px;
    color: #000;
    text-indent: 8px;
}
.appHeaderRight  .appHeaderSearchBox .appHeaderSearchDownSlider .appHeaderSearchContent .appHeaderSearchContentMain .appHeaderSearchContentMainItem:hover {
    background-color: #ecf0f1;
    border-radius: 5%;
    color: #c20b0b;
}
.appHeaderRight  .appHeaderSearchBox .appHeaderSearchDownSlider .appHeaderSearchContent .appHeaderSearchContentMain .active {
    background-color: #ecf0f1;
    color: #c20b0b;
}
.appHeaderRight .appHeaderCenter {
    width: 75px;
    height: 32px;
    line-height: 32px;
    text-align: center;
    border: 1px solid #666;
    border-radius: 16px;
    margin: 0 13px;
    background-color: transparent;
}

.appHeaderRight .appHeaderCenter:hover {
    cursor: pointer;
    border-color: #fff;
    color: #fff;
}
  
.appHeaderRight .appHeaderLogin {
    color: #666;
}
.appHeaderRight .appHeaderLogin:hover {
    cursor: pointer;
    text-decoration: underline;
}
.appHeaderRight .appHeaderProfileImg{
    width: 35px;
    height: auto;
    border-radius: 50%;
}