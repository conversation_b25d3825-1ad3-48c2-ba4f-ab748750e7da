import React, { memo, useRef, useState } from 'react'
import { Button, message, Modal } from 'antd'
import Draggable from 'react-draggable'
import { shallowEqual, useDispatch, useSelector } from 'react-redux'
import { changeIsVisible } from './store'
import { PhoneOutlined } from '@ant-design/icons'
import LoginIcon from '../themeLoginIcon'
import ThemeLoginForm from '../themeLoginForm'
import './style.css'

/**
 * 登录页面(模态框)
 */
function ThemeLogin() {
  // state/props
  const [disabled, setDisabled] = useState(true)
  const [loginState, setLoginState] = useState('default') // 默认状态显示
  const [bounds, setBounds] = useState({ left: 0, top: 0, bottom: 0, right: 0 })
  const draggleRef: any = useRef()

  // redux
  const dispatch = useDispatch()
  const { isVisible } = useSelector(
    (state: any) => ({
      isVisible: state.getIn(['loginState', 'isVisible']),
    }),
    shallowEqual
  )

  // 取消
  const handleCancel = () => {
    // 关闭模态框
    dispatch(changeIsVisible(false))
    // 延迟返回初始化状态
    setTimeout(() => {
      setLoginState('default')
    }, 100)
  }
  // 拖拽

  const onStart = (event: any, uiData: any) => {
  //  console.log('---->拖拽')
    const { clientWidth, clientHeight } = window?.document?.documentElement
    const targetRect = draggleRef?.current?.getBoundingClientRect()
    setBounds({
      left: -targetRect?.left + uiData?.x,
      right: clientWidth - (targetRect?.right - uiData?.x),
      top: -targetRect?.top + uiData?.y,
      bottom: clientHeight - (targetRect?.bottom - uiData?.y),
    })
  }

  const handleLogin = (loginMode: any) => {
    switch (loginMode) {
      case 'phone':
        setLoginState('phone')
        break
      case 'email':
        setLoginState('email')
        break
      case 'register':
        setLoginState('register')
        break
      default:
    }
  }

  const defaultContent = (
    <div className="loginBox">
      <div className="loginLeft">
        <div className="loginContent">
          <div className="loginBg"></div>
          <Button
            type="ghost"
            onClick={() => handleLogin('register')}
            shape="round"
            icon={<PhoneOutlined />}
            className="loginFirstButton"
          >
            注册
          </Button>
          <Button
            type="primary"
            shape="round"
            icon={<PhoneOutlined />}
            onClick={() => handleLogin('phone')}
          >
            手机号登录
          </Button>
        </div>
      </div>
      <div className="loginRight">
        <LoginIcon
          onClick={() => message.warn('暂不做')}
          position="-150px -670px"
          description="微信登录"
        />
        <LoginIcon
          onClick={() => message.warn('暂不做')}
          position="-190px -670px"
          description="QQ登录"
        />
        <LoginIcon
          onClick={() => message.warn('暂不做')}
          position="-231px -670px"
          description="微博登录"
        />
        <LoginIcon
          onClick={() => handleLogin('email')}
          position="-271px -670px"
          description="网易邮箱登录"
        />
      </div>
    </div>
  )

  const phoneLogin = (loginState: any) => {
    return (
      <div className="phoneLoginModal">
        <ThemeLoginForm loginState={loginState} />
      </div>
    )
  }

  return (
    <Draggable>
      <Modal
        centered
        footer={null}
        title={
          <div
            style={{ width: '100%', cursor: 'move', }}
            onMouseOver={() => {
              if (disabled) setDisabled(false)
            }}
            onMouseOut={() => { setDisabled(true) }}
            onFocus={() => { }}
            onBlur={() => { }}
          >
            {loginState === 'register' ? '注册' : '登录'}
          </div>
        }
        visible={isVisible}
        onCancel={handleCancel}
        modalRender={(modal) => (
          <Draggable
            disabled={disabled}
            bounds={bounds}
            onStart={(event, uiData) => onStart(event, uiData)}
          >
            <div ref={draggleRef}>{modal}</div>
          </Draggable>
        )}
      >

        {/* 登录 */}
        {loginState === 'default' ? defaultContent : null}
        {loginState === 'phone' ? phoneLogin('phone') : undefined}
        {loginState === 'email' ? phoneLogin('email') : undefined}
        {loginState === 'register' ? phoneLogin('register') : undefined}
        <em style={{ display: loginState !== 'default' ? 'block' : 'none', cursor: 'pointer', color: '#1890ff', fontSize: '12px' }} onClick={() => setLoginState('default')}>&lt;其他登陆方式</em>
      </Modal>
    </Draggable>
  )
}

export default memo(ThemeLogin)
