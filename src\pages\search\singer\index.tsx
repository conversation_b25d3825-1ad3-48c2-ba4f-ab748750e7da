import React, { memo, useEffect } from 'react'
import SingerItem from './singerItem'
import './style.css'
import { shallowEqual, useDispatch, useSelector } from 'react-redux'
import { getSearchSingerListAction } from '../store/actionCreators'
import { useSearchParams } from 'react-router-dom'

export default memo(function Singer() {
  // props/state
  const [searchParams, setSearchParams] = useSearchParams();
  const type = searchParams.get('type');
  const song = searchParams.get('song');
  // redux hook
  const dispatch = useDispatch()
  const { singerList } = useSelector(
    (state: any) => ({
      singerList: state.getIn(['search', 'singerList']),
    }),
    shallowEqual
  )

  useEffect(() => {
    dispatch(getSearchSingerListAction(song, 20, type))
  }, [dispatch, song, type])

  return (
      <div className="singerBox">
        {singerList &&
          singerList.map((item: any) => {
            return (
              <SingerItem
                key={item.id}
                coverPic={item.picUrl}
                singer={item.name}
              />
            )
          })}
      </div>
  )
})
