import * as actionTypes from './actionTypes';
import { PER_PAGE_NUMBER } from './actionTypes';

import {
  getSongCategory,
  getSongCategoryList
} from "../../../../request/songList";
import { 
  handleSongsCategory
} from "../../../../utils/handleData";

const changeCategoryAction = (res: any) => ({
  type: actionTypes.CHANGE_CATEGORY,
  category: res
})

const changeSongListAction = (res: any) => ({
  type: actionTypes.CHANGE_CATEGORY_SONGS,
  categorySongs: res
})

export const changeCategoryIsVisible = (visibleState: any) => ({
  type: actionTypes.CHANGE_CATEGORY_IS_VISIBLE_STATE,
  categoryIsVisible: visibleState
})

export const changeCurrentCategoryAction = (name: any) => ({
  type: actionTypes.CHANGE_CURRENT_CATEGORY,
  currentCategory: name
})

export const getCategory = () => {
  return (dispatch: any) => {
    getSongCategory().then(res => {
      const categoryData = handleSongsCategory(res);
      dispatch(changeCategoryAction(categoryData))
    })
  }
}

export const getSongList = (page: any) => {
  return (dispatch: any, getState: any) => {
    // 1.获取currentCategory
    const name = getState().getIn(["songList", "currentCategory"]);

    // 2.获取数据
    getSongCategoryList(name, page * PER_PAGE_NUMBER, 35).then(res => {
      dispatch(changeSongListAction(res));
    })
  }
}
