.recommendToplistBox {
    width: 230px;
}

.recommendToplistBox .recommendToplistHeader {
    display: flex;
    height: 120px;
    padding: 20px 0 0 19px;
}
.recommendToplistBox .recommendToplistHeader .recommendToplistImage {
    position: relative;
    height: 80px;
}
.recommendToplistBox .recommendToplistHeader .recommendToplistImage .recommendToplistImageCover{
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    background: url(../../static/images/sprite_cover.png) no-repeat -145px -57px;
}    
.recommendToplistBox .recommendToplistHeader .recommendToplistTitle {
    width: 116px;
    margin: 6px 0 0 10px;
}
.recommendToplistBox .recommendToplistHeader .recommendToplistTitle h3 {
    font-weight: bold;
}

.recommendToplistBox .recommendToplistHeader .recommendToplistTitle .recommendToplistButton {
    display: flex;
    margin-top: 8px;
}
.recommendToplistBox .recommendToplistHeader .recommendToplistTitle .recommendToplistButton .recommendToplistButtonPlay,
.recommendToplistBox .recommendToplistHeader .recommendToplistTitle .recommendToplistButton .recommendToplistButtonFavourite {
    width: 22px;
    height: 22px;
    cursor: pointer;
}

.recommendToplistBox .recommendToplistHeader .recommendToplistTitle .recommendToplistButton .recommendToplistButtonPlay {
    background: url(../../static/images/sprite_02.png);
    background-position: -267px -205px;
    margin-right: 8px;
}
.recommendToplistBox .recommendToplistHeader .recommendToplistTitle .recommendToplistButton .recommendToplistButtonPlay:hover {
    background-position: -267px -235px;
}
        

.recommendToplistBox .recommendToplistHeader .recommendToplistTitle .recommendToplistButton .recommendToplistButtonFavourite {
    background: url(../../static/images/sprite_02.png);
    background-position: -300px -205px;
}
.recommendToplistBox .recommendToplistHeader .recommendToplistTitle .recommendToplistButton .recommendToplistButtonFavourite:hover {
    background-position: -300px -235px;
}
    

.recommendToplistBox .recommendToplist {
    padding-left: 10px;
}

.recommendToplistBox .recommendToplist .recommendToplistItem {
    display: flex;
    height: 32px;
    line-height: 32px;
}
.recommendToplistBox .recommendToplist .recommendToplistItem .recommendToplistItemNumber {
    width: 35px;
    text-align: center;
}

.recommendToplistBox .recommendToplist .recommendToplistItem .recommendToplistItemSongName {
    width: 185px;
    font-size: 12px;
}

.recommendToplistBox .recommendToplist .recommendToplistItem:hover .recommendToplistItemSongName {
    width: 96px;
}

.recommendToplistBox .recommendToplist .recommendToplistItem:hover .recommendToplistItemOper {
    visibility: visible;
    width: 93px;
}

.recommendToplistBox .recommendToplist .recommendToplistItem .recommendToplistItemOper {
    display: flex;
    align-items: center;
    visibility: hidden;
    width: 0;
    text-indent: -9999px;
}
.recommendToplistBox .recommendToplist .recommendToplistItem .recommendToplistItemOper .recommendToplistItemOperButton {
    width: 17px;
    height: 17px;
    margin-left: 8px;
    
    cursor: pointer;
}

.recommendToplistBox .recommendToplist .recommendToplistItem .recommendToplistItemOper .recommendToplistItemOperPlay {
    background: url(../../static/images/sprite_02.png);
    background-position: -267px -268px;
}

.recommendToplistBox .recommendToplist .recommendToplistItem .recommendToplistItemOper .recommendToplistItemOperAddto {
    position: relative;
    top: 2px;
    background: url(../../static/images/sprite_icon2.png);
    background-position: 0 -700px;
}

.recommendToplistBox .recommendToplist .recommendToplistItem .recommendToplistItemOper .recommendToplistItemOperFavourite {
    background: url(../../static/images/sprite_02.png);
    background-position: -297px -268px;
    cursor: pointer;
}

.recommendToplistBox .recommendToplistFooter{
    height: 33px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
}
.recommendToplistBox .recommendToplistFooter .recommendToplistShowAll {
    margin-right: 15px;
}

