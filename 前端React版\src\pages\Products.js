import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { 
  Row, 
  Col, 
  Card, 
  Input, 
  Select, 
  Button, 
  List,
  Avatar,
  Space,
  Pagination,
  Empty,
  Spin,
  Tag
} from 'antd';
import { SearchOutlined, ShoppingOutlined } from '@ant-design/icons';
import { fetchCategories, searchProducts } from '../store/actions/productActions';

const { Search } = Input;
const { Option } = Select;

const Products = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  
  const { 
    categories,
    searchResults,
    searchTotal,
    searchCurrent,
    searchSize,
    searchLoading
  } = useSelector(state => state.product);

  const [searchForm, setSearchForm] = useState({
    keyword: searchParams.get('keyword') || '',
    categoryId: searchParams.get('categoryId') || '',
    page: parseInt(searchParams.get('page')) || 1,
    size: parseInt(searchParams.get('size')) || 12
  });

  useEffect(() => {
    // 获取分类
    dispatch(fetchCategories());
  }, [dispatch]);

  useEffect(() => {
    // 执行搜索
    handleSearch();
  }, [searchForm]);

  // 处理搜索
  const handleSearch = () => {
    const params = {
      ...searchForm,
      categoryId: searchForm.categoryId || undefined
    };
    
    dispatch(searchProducts(params));
    
    // 更新URL参数
    const newSearchParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value) {
        newSearchParams.set(key, value);
      }
    });
    setSearchParams(newSearchParams);
  };

  // 处理表单变化
  const handleFormChange = (field, value) => {
    setSearchForm(prev => ({
      ...prev,
      [field]: value,
      page: field !== 'page' ? 1 : value // 非分页字段变化时重置页码
    }));
  };

  // 处理分页
  const handlePageChange = (page, pageSize) => {
    setSearchForm(prev => ({
      ...prev,
      page,
      size: pageSize
    }));
  };

  return (
    <div className="products-page">
      {/* 搜索区域 */}
      <Card style={{ marginBottom: 24 }}>
        <Row gutter={[16, 16]} align="middle">
          <Col xs={24} sm={12} md={8}>
            <Search
              placeholder="搜索产品名称"
              value={searchForm.keyword}
              onChange={(e) => handleFormChange('keyword', e.target.value)}
              onSearch={() => handleSearch()}
              enterButton={<SearchOutlined />}
              size="large"
            />
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Select
              placeholder="选择分类"
              value={searchForm.categoryId}
              onChange={(value) => handleFormChange('categoryId', value)}
              allowClear
              size="large"
              style={{ width: '100%' }}
            >
              {categories.map(category => (
                <Option key={category.id} value={category.id}>
                  {category.name}
                </Option>
              ))}
            </Select>
          </Col>
          <Col xs={24} sm={24} md={4}>
            <Button 
              type="primary" 
              icon={<SearchOutlined />}
              onClick={handleSearch}
              size="large"
              block
            >
              搜索
            </Button>
          </Col>
        </Row>
      </Card>

      {/* 搜索结果 */}
      <Card 
        title={
          <Space>
            <span>搜索结果</span>
            {searchTotal > 0 && (
              <Tag color="blue">共 {searchTotal} 个产品</Tag>
            )}
          </Space>
        }
      >
        {searchLoading ? (
          <div style={{ textAlign: 'center', padding: 60 }}>
            <Spin size="large" tip="搜索中..." />
          </div>
        ) : searchResults.length > 0 ? (
          <>
            <List
              grid={{
                gutter: 16,
                xs: 1,
                sm: 2,
                md: 3,
                lg: 4,
                xl: 4,
                xxl: 6
              }}
              dataSource={searchResults}
              renderItem={product => (
                <List.Item>
                  <Card
                    hoverable
                    cover={
                      <div style={{ 
                        height: 160, 
                        background: '#f5f5f5',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center'
                      }}>
                        {product.image ? (
                          <img 
                            src={product.image} 
                            alt={product.name}
                            style={{ 
                              maxWidth: '100%', 
                              maxHeight: '100%',
                              objectFit: 'cover'
                            }}
                          />
                        ) : (
                          <ShoppingOutlined style={{ fontSize: 48, color: '#ccc' }} />
                        )}
                      </div>
                    }
                    onClick={() => navigate(`/products/${product.id}`)}
                  >
                    <Card.Meta
                      title={
                        <div style={{ 
                          whiteSpace: 'nowrap', 
                          overflow: 'hidden', 
                          textOverflow: 'ellipsis' 
                        }}>
                          {product.name}
                        </div>
                      }
                      description={
                        <div>
                          <div style={{ 
                            color: '#f50', 
                            fontSize: 16, 
                            fontWeight: 'bold',
                            marginBottom: 4
                          }}>
                            ¥{product.minPrice}-{product.maxPrice}
                            <span style={{ 
                              color: '#999', 
                              fontSize: 12, 
                              fontWeight: 'normal' 
                            }}>
                              /{product.unit}
                            </span>
                          </div>
                          {product.categoryName && (
                            <Tag size="small">{product.categoryName}</Tag>
                          )}
                        </div>
                      }
                    />
                  </Card>
                </List.Item>
              )}
            />
            
            {/* 分页 */}
            {searchTotal > searchSize && (
              <div style={{ textAlign: 'center', marginTop: 24 }}>
                <Pagination
                  current={searchCurrent}
                  total={searchTotal}
                  pageSize={searchSize}
                  showSizeChanger
                  showQuickJumper
                  showTotal={(total, range) => 
                    `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
                  }
                  onChange={handlePageChange}
                />
              </div>
            )}
          </>
        ) : (
          <Empty 
            description="暂无产品数据"
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          />
        )}
      </Card>
    </div>
  );
};

export default Products;
