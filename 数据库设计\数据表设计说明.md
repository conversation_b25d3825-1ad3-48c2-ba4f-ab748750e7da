# 🗄️ 慢慢回收小程序 MVP版本数据表设计说明

## 📋 设计概述

### 设计原则
- **最小可行产品**：只包含MVP版本必需的核心表
- **扩展性**：预留后续功能扩展的空间
- **性能优化**：合理的索引设计和字段类型选择
- **数据完整性**：适当的外键约束和数据验证

### 技术规范
- **数据库**：MySQL 8.0+
- **字符集**：utf8mb4（支持emoji和特殊字符）
- **存储引擎**：InnoDB（支持事务和外键）
- **命名规范**：下划线命名法，表名复数形式

---

## 📊 数据表结构详解

### 1. 用户相关表 👥

#### 1.1 users - 用户基础信息表
**功能**：存储用户的基本信息和登录状态

| 字段名 | 类型 | 说明 | 索引 |
|--------|------|------|------|
| id | bigint(20) | 用户唯一标识 | 主键 |
| openid | varchar(64) | 微信openid | 唯一索引 |
| unionid | varchar(64) | 微信unionid | - |
| nickname | varchar(50) | 用户昵称 | - |
| avatar_url | varchar(255) | 头像URL | - |
| phone | varchar(20) | 手机号 | 普通索引 |
| gender | tinyint(1) | 性别 0-未知 1-男 2-女 | - |
| city | varchar(50) | 城市 | 普通索引 |
| status | tinyint(1) | 状态 0-禁用 1-正常 | - |
| last_login_time | datetime | 最后登录时间 | - |

**设计要点**：
- openid作为微信用户唯一标识，设置唯一索引
- phone和city设置索引，便于用户查找和地域统计
- 预留unionid字段，支持多平台用户统一

#### 1.2 user_addresses - 用户地址表
**功能**：存储用户的收货地址信息

| 字段名 | 类型 | 说明 | 索引 |
|--------|------|------|------|
| id | bigint(20) | 地址唯一标识 | 主键 |
| user_id | bigint(20) | 用户ID | 外键索引 |
| name | varchar(50) | 联系人姓名 | - |
| phone | varchar(20) | 联系电话 | - |
| address | varchar(200) | 详细地址 | - |
| longitude | decimal(10,7) | 经度 | - |
| latitude | decimal(10,7) | 纬度 | - |
| is_default | tinyint(1) | 是否默认地址 | - |

**设计要点**：
- 支持用户多地址管理
- 存储经纬度坐标，支持地理位置计算
- 级联删除，用户删除时自动删除地址

### 2. 回收站点相关表 🏪

#### 2.1 recycle_stations - 回收站点表
**功能**：存储回收站点的基本信息和状态

| 字段名 | 类型 | 说明 | 索引 |
|--------|------|------|------|
| id | bigint(20) | 站点唯一标识 | 主键 |
| name | varchar(100) | 站点名称 | - |
| phone | varchar(20) | 联系电话 | - |
| address | varchar(200) | 详细地址 | - |
| longitude | decimal(10,7) | 经度 | 复合索引 |
| latitude | decimal(10,7) | 纬度 | 复合索引 |
| city | varchar(50) | 城市 | 普通索引 |
| business_hours | varchar(100) | 营业时间 | - |
| images | json | 站点图片JSON数组 | - |
| rating | decimal(3,2) | 评分 0-5.00 | 普通索引 |
| review_count | int(11) | 评价数量 | - |
| status | tinyint(1) | 状态 0-停业 1-营业 2-休息 | 普通索引 |
| features | json | 服务特色JSON数组 | - |

**设计要点**：
- longitude和latitude设置复合索引，支持地理位置查询
- 使用JSON字段存储图片数组和服务特色，提高灵活性
- rating和status设置索引，支持按评分和状态筛选

#### 2.2 station_services - 回收站点服务类型表
**功能**：定义各站点支持的回收类型和价格范围

| 字段名 | 类型 | 说明 | 索引 |
|--------|------|------|------|
| id | bigint(20) | 服务唯一标识 | 主键 |
| station_id | bigint(20) | 站点ID | 外键索引 |
| category_id | bigint(20) | 分类ID | 普通索引 |
| min_price | decimal(10,2) | 最低价格 | - |
| max_price | decimal(10,2) | 最高价格 | - |
| unit | varchar(20) | 单位 kg/台/个 | - |
| is_active | tinyint(1) | 是否启用 | - |

**设计要点**：
- 关联站点和分类，支持多对多关系
- 存储价格区间，便于用户了解大概价位
- 支持启用/禁用状态管理

### 3. 产品分类和价格相关表 💰

#### 3.1 recycle_categories - 回收分类表
**功能**：定义回收物品的分类体系

| 字段名 | 类型 | 说明 | 索引 |
|--------|------|------|------|
| id | bigint(20) | 分类唯一标识 | 主键 |
| parent_id | bigint(20) | 父分类ID | 普通索引 |
| name | varchar(50) | 分类名称 | - |
| icon | varchar(100) | 分类图标 | - |
| description | varchar(200) | 分类描述 | - |
| sort_order | int(11) | 排序权重 | 普通索引 |
| is_active | tinyint(1) | 是否启用 | - |

**设计要点**：
- 支持树形结构，parent_id=0为顶级分类
- sort_order支持自定义排序
- 预置emoji图标，提升用户体验

#### 3.2 products - 产品信息表
**功能**：存储具体的回收产品信息

| 字段名 | 类型 | 说明 | 索引 |
|--------|------|------|------|
| id | bigint(20) | 产品唯一标识 | 主键 |
| category_id | bigint(20) | 分类ID | 外键索引 |
| name | varchar(100) | 产品名称 | 普通索引 |
| brand | varchar(50) | 品牌 | 普通索引 |
| model | varchar(100) | 型号 | - |
| specifications | text | 规格参数 | - |
| images | json | 产品图片JSON数组 | - |
| keywords | varchar(200) | 搜索关键词 | - |
| is_active | tinyint(1) | 是否启用 | - |

**设计要点**：
- name和brand设置索引，支持快速搜索
- keywords字段支持模糊搜索优化
- specifications使用text类型，支持详细描述

#### 3.3 product_prices - 产品价格表
**功能**：存储产品的历史价格和实时价格

| 字段名 | 类型 | 说明 | 索引 |
|--------|------|------|------|
| id | bigint(20) | 价格唯一标识 | 主键 |
| product_id | bigint(20) | 产品ID | 外键索引 |
| station_id | bigint(20) | 站点ID（可空） | 外键索引 |
| min_price | decimal(10,2) | 最低价格 | - |
| max_price | decimal(10,2) | 最高价格 | - |
| avg_price | decimal(10,2) | 平均价格 | - |
| unit | varchar(20) | 单位 | - |
| condition_desc | varchar(100) | 成色要求 | - |
| price_date | date | 价格日期 | 普通索引 |

**设计要点**：
- station_id为空表示市场均价
- price_date支持历史价格查询和趋势分析
- 存储价格区间，更贴近实际交易情况

### 4. 用户行为相关表 📊

#### 4.1 user_search_logs - 用户搜索记录表
**功能**：记录用户的搜索行为，用于优化搜索和推荐

| 字段名 | 类型 | 说明 | 索引 |
|--------|------|------|------|
| id | bigint(20) | 记录唯一标识 | 主键 |
| user_id | bigint(20) | 用户ID | 外键索引 |
| keyword | varchar(100) | 搜索关键词 | 普通索引 |
| search_type | tinyint(1) | 搜索类型 1-产品 2-站点 | - |
| result_count | int(11) | 结果数量 | - |
| created_at | datetime | 创建时间 | 普通索引 |

**设计要点**：
- 支持搜索历史和热门搜索统计
- 区分产品搜索和站点搜索
- 记录结果数量，用于搜索质量分析

#### 4.2 user_favorites - 用户收藏表
**功能**：存储用户收藏的站点和产品

| 字段名 | 类型 | 说明 | 索引 |
|--------|------|------|------|
| id | bigint(20) | 收藏唯一标识 | 主键 |
| user_id | bigint(20) | 用户ID | 外键索引 |
| target_type | tinyint(1) | 收藏类型 1-站点 2-产品 | - |
| target_id | bigint(20) | 目标ID | - |
| created_at | datetime | 创建时间 | - |

**设计要点**：
- 统一的收藏表设计，支持多种类型收藏
- 设置唯一索引防止重复收藏
- 支持收藏时间排序

#### 4.3 user_browse_logs - 用户浏览记录表
**功能**：记录用户的浏览行为，用于个性化推荐

| 字段名 | 类型 | 说明 | 索引 |
|--------|------|------|------|
| id | bigint(20) | 记录唯一标识 | 主键 |
| user_id | bigint(20) | 用户ID | 外键索引 |
| target_type | tinyint(1) | 浏览类型 1-站点 2-产品 | - |
| target_id | bigint(20) | 目标ID | - |
| duration | int(11) | 浏览时长(秒) | - |
| created_at | datetime | 创建时间 | 普通索引 |

**设计要点**：
- 记录浏览时长，分析用户兴趣度
- 支持用户行为分析和推荐算法
- 按时间索引，支持时间范围查询

### 5. 评价反馈相关表 ⭐

#### 5.1 station_reviews - 站点评价表
**功能**：存储用户对回收站点的评价和反馈

| 字段名 | 类型 | 说明 | 索引 |
|--------|------|------|------|
| id | bigint(20) | 评价唯一标识 | 主键 |
| user_id | bigint(20) | 用户ID | 外键索引 |
| station_id | bigint(20) | 站点ID | 外键索引 |
| rating | tinyint(1) | 总评分 1-5星 | 普通索引 |
| content | text | 评价内容 | - |
| images | json | 评价图片JSON数组 | - |
| service_rating | tinyint(1) | 服务评分 | - |
| price_rating | tinyint(1) | 价格评分 | - |
| environment_rating | tinyint(1) | 环境评分 | - |
| is_anonymous | tinyint(1) | 是否匿名 | - |
| status | tinyint(1) | 状态 0-隐藏 1-显示 | - |

**设计要点**：
- 支持总评分和分项评分
- 支持图片评价，提升评价质量
- 支持匿名评价和状态管理

### 6. 系统配置相关表 ⚙️

#### 6.1 system_configs - 系统配置表
**功能**：存储系统的配置参数

| 字段名 | 类型 | 说明 | 索引 |
|--------|------|------|------|
| id | bigint(20) | 配置唯一标识 | 主键 |
| config_key | varchar(100) | 配置键 | 唯一索引 |
| config_value | text | 配置值 | - |
| config_desc | varchar(200) | 配置描述 | - |
| config_type | varchar(50) | 配置类型 | - |
| is_active | tinyint(1) | 是否启用 | - |

**设计要点**：
- 灵活的键值对配置系统
- 支持不同数据类型的配置
- 支持配置的启用/禁用管理

---

## 🔗 表关系图

```
users (用户表)
├── user_addresses (用户地址)
├── user_search_logs (搜索记录)
├── user_favorites (收藏记录)
├── user_browse_logs (浏览记录)
└── station_reviews (站点评价)

recycle_stations (回收站点)
├── station_services (站点服务)
└── station_reviews (站点评价)

recycle_categories (回收分类)
├── products (产品信息)
└── station_services (站点服务)

products (产品信息)
└── product_prices (产品价格)
```

---

## 📈 性能优化建议

### 索引优化
1. **地理位置查询**：longitude + latitude 复合索引
2. **用户行为查询**：user_id + created_at 复合索引
3. **价格查询**：product_id + price_date 复合索引
4. **搜索优化**：name、brand、keywords 全文索引

### 数据分区
1. **日志表分区**：按月分区用户行为日志表
2. **价格表分区**：按年分区产品价格历史表

### 缓存策略
1. **热点数据**：站点信息、分类数据缓存
2. **用户数据**：用户基本信息和偏好缓存
3. **价格数据**：实时价格数据缓存

---

## 🚀 扩展规划

### 后续版本扩展表
1. **AI路线规划**：route_plans、route_points
2. **订单交易**：orders、order_items
3. **消息通知**：notifications、message_templates
4. **社交功能**：user_follows、user_posts
5. **积分系统**：user_points、point_logs

### 数据迁移策略
1. **版本控制**：使用数据库迁移脚本
2. **向后兼容**：保持API接口兼容性
3. **数据备份**：升级前完整数据备份
4. **灰度发布**：分批次数据迁移

---

**📝 此数据表设计为MVP版本的核心数据结构，后续将根据功能需求逐步扩展和优化。**
