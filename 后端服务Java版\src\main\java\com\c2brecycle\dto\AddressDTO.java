package com.c2brecycle.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.*;
import java.math.BigDecimal;

/**
 * 地址信息DTO
 * 
 * <AUTHOR> Recycle Team
 * @since 2024-01-15
 */
@Data
@ApiModel("地址信息参数")
public class AddressDTO {

    @ApiModelProperty(value = "联系人", required = true, example = "张三")
    @NotBlank(message = "联系人不能为空")
    @Size(min = 1, max = 20, message = "联系人长度为1-20个字符")
    private String contact;

    @ApiModelProperty(value = "电话", required = true, example = "13800138000")
    @NotBlank(message = "电话不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;

    @ApiModelProperty(value = "省份", required = true, example = "北京市")
    @NotBlank(message = "省份不能为空")
    @Size(max = 20, message = "省份名称最多20个字符")
    private String province;

    @ApiModelProperty(value = "城市", required = true, example = "北京市")
    @NotBlank(message = "城市不能为空")
    @Size(max = 20, message = "城市名称最多20个字符")
    private String city;

    @ApiModelProperty(value = "区县", required = true, example = "朝阳区")
    @NotBlank(message = "区县不能为空")
    @Size(max = 20, message = "区县名称最多20个字符")
    private String district;

    @ApiModelProperty(value = "详细地址", required = true, example = "三里屯街道1号")
    @NotBlank(message = "详细地址不能为空")
    @Size(min = 5, max = 100, message = "详细地址长度为5-100个字符")
    private String address;

    @ApiModelProperty(value = "经度", example = "116.407400")
    @DecimalMin(value = "-180.0", message = "经度范围为-180到180")
    @DecimalMax(value = "180.0", message = "经度范围为-180到180")
    private BigDecimal lng;

    @ApiModelProperty(value = "纬度", example = "39.904200")
    @DecimalMin(value = "-90.0", message = "纬度范围为-90到90")
    @DecimalMax(value = "90.0", message = "纬度范围为-90到90")
    private BigDecimal lat;

    @ApiModelProperty(value = "是否设为默认地址", example = "true")
    private Boolean isDefault = false;
}
