import axios from 'axios'
import NProgress from 'nprogress'

export default function request(config: any) {
  const instance = axios.create({
    baseURL: 'https://kkapi-ten.vercel.app/',
    timeout: 50000,
    headers: {},
    withCredentials: true
  })

  instance.interceptors.request.use(
    (config) => {
      // 1.发送网络请求时, 在界面的中间位置显示Loading的组件
      NProgress.start(); // 启动滚动条
      return config
    },
    (err) => {}
  )
  instance.interceptors.response.use(
    (res) => {
      NProgress.done()// 关闭滚动条
      return res.data
    },
    (err) => {
      if (err && err.response) {
        switch (err.response.status) {
          case 400:
            console.log('请求错误')
            break
          case 401:
            console.log('未授权访问')
            break
          case 404:
            console.log('未找到')
            break
          default:
            console.log('其他错误信息')
        }
      }
      return err
    }
  )
  return instance(config);
}
