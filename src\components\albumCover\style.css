.albumItem {
    font-size: 12px;
}
.albumItem .albumIcon {
    position: relative;
    overflow: hidden;
    margin-top: 15px;
}


.albumItem .albumIcon .albumCover {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    background-image: url(../../static/images/sprite_cover.png);
    /* background-position: 0 -845px; */
}

.albumItem .albumInfo .albumName {
    display: block;
    font-size: 14px;
    color: #333;
    margin-top: 6px;
    margin-bottom: 0px;
    cursor: pointer;
}
.albumItem .albumInfo .albumNickNameBox .albumNickName {
    font-size: 12px;
    color: #666;
    cursor: pointer;
}

.albumItem .albumInfo .albumName:hover,
.albumItem .albumInfo .albumNickNameBox .albumNickName:hover {
    text-decoration: underline;
}

