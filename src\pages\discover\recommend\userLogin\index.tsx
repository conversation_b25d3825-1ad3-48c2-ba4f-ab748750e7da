import React, { memo } from 'react'
import { changeIsVisible } from '../../../../components/themeLogin/store'
import { useDispatch, useSelector } from 'react-redux'
import './style.css'
export default memo(function UserLogin() {
    // redux
    const dispatch = useDispatch()
    const { isLogin } = useSelector((state: any) => ({
        isLogin: state.getIn(['loginState', 'isLogin']),
    }))
    // handle function

    const handleLogin = () => {
        if (!isLogin) dispatch(changeIsVisible(true))
    }
    return (
        <div className="userLoginBox" style={{ display: isLogin ? 'none' : 'block' }}>
            <div className="userLoginProfileInfo">
                <p className="userLoginDetail">
                    登录网易云音乐，可以享受无限收藏的乐趣，并且无限同步到手机
                </p>
                <button className="userLoginButton" onClick={() => handleLogin()}>
                    用户登录
                </button>
            </div>
        </div>
    )
})
