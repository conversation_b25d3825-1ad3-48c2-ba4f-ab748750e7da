// app.js
App({
  globalData: {
    userInfo: null,
    token: null,
    baseUrl: 'https://api.manmanrecycle.com',
    version: '1.0.0'
  },

  onLaunch() {
    // 小程序启动时执行
    console.log('慢慢回收小程序启动')
    
    // 检查更新
    this.checkUpdate()
    
    // 初始化用户信息
    this.initUserInfo()
    
    // 获取系统信息
    this.getSystemInfo()
  },

  onShow() {
    // 小程序显示时执行
    console.log('小程序显示')
  },

  onHide() {
    // 小程序隐藏时执行
    console.log('小程序隐藏')
  },

  // 检查小程序更新
  checkUpdate() {
    if (wx.canIUse('getUpdateManager')) {
      const updateManager = wx.getUpdateManager()
      
      updateManager.onCheckForUpdate((res) => {
        if (res.hasUpdate) {
          updateManager.onUpdateReady(() => {
            wx.showModal({
              title: '更新提示',
              content: '新版本已经准备好，是否重启应用？',
              success: (res) => {
                if (res.confirm) {
                  updateManager.applyUpdate()
                }
              }
            })
          })
        }
      })
    }
  },

  // 初始化用户信息
  initUserInfo() {
    const token = wx.getStorageSync('token')
    const userInfo = wx.getStorageSync('userInfo')
    
    if (token) {
      this.globalData.token = token
    }
    
    if (userInfo) {
      this.globalData.userInfo = userInfo
    }
  },

  // 获取系统信息
  getSystemInfo() {
    wx.getSystemInfo({
      success: (res) => {
        this.globalData.systemInfo = res
        console.log('系统信息:', res)
      }
    })
  },

  // 用户登录
  login() {
    return new Promise((resolve, reject) => {
      wx.login({
        success: (res) => {
          if (res.code) {
            // 发送 res.code 到后台换取 openId, sessionKey, unionId
            this.request({
              url: '/auth/login',
              method: 'POST',
              data: {
                code: res.code
              }
            }).then(result => {
              if (result.success) {
                this.globalData.token = result.data.token
                wx.setStorageSync('token', result.data.token)
                resolve(result.data)
              } else {
                reject(result.message)
              }
            }).catch(reject)
          } else {
            reject('登录失败：' + res.errMsg)
          }
        },
        fail: reject
      })
    })
  },

  // 网络请求封装
  request(options) {
    return new Promise((resolve, reject) => {
      wx.request({
        url: this.globalData.baseUrl + options.url,
        method: options.method || 'GET',
        data: options.data || {},
        header: {
          'Content-Type': 'application/json',
          'Authorization': this.globalData.token ? `Bearer ${this.globalData.token}` : '',
          ...options.header
        },
        success: (res) => {
          if (res.statusCode === 200) {
            resolve(res.data)
          } else if (res.statusCode === 401) {
            // token过期，重新登录
            this.login().then(() => {
              // 重新发起请求
              this.request(options).then(resolve).catch(reject)
            }).catch(reject)
          } else {
            reject(res.data || '请求失败')
          }
        },
        fail: reject
      })
    })
  },

  // 显示加载提示
  showLoading(title = '加载中...') {
    wx.showLoading({
      title,
      mask: true
    })
  },

  // 隐藏加载提示
  hideLoading() {
    wx.hideLoading()
  },

  // 显示提示信息
  showToast(title, icon = 'none') {
    wx.showToast({
      title,
      icon,
      duration: 2000
    })
  }
})
