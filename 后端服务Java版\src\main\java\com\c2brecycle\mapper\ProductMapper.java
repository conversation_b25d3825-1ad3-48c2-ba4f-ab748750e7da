package com.c2brecycle.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.c2brecycle.entity.Product;
import com.c2brecycle.vo.ProductDetailVO;
import com.c2brecycle.vo.ProductVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 产品Mapper接口
 * 
 * <AUTHOR> Recycle Team
 * @since 2024-01-15
 */
@Mapper
public interface ProductMapper extends BaseMapper<Product> {

    /**
     * 搜索产品
     * 
     * @param page 分页参数
     * @param keyword 关键词
     * @param categoryId 分类ID
     * @param city 城市
     * @return 产品列表
     */
    IPage<ProductVO> searchProducts(Page<ProductVO> page, 
                                   @Param("keyword") String keyword, 
                                   @Param("categoryId") Long categoryId, 
                                   @Param("city") String city);

    /**
     * 获取产品详情
     * 
     * @param productId 产品ID
     * @param userId 用户ID
     * @return 产品详情
     */
    ProductDetailVO selectProductDetail(@Param("productId") Long productId, 
                                       @Param("userId") Long userId);

    /**
     * 获取热门产品
     * 
     * @param categoryId 分类ID
     * @param limit 数量限制
     * @return 热门产品列表
     */
    @Select("SELECT p.*, c.name as category_name " +
            "FROM products p " +
            "LEFT JOIN categories c ON p.category_id = c.id " +
            "WHERE p.status = 1 AND p.deleted = 0 " +
            "AND (#{categoryId} IS NULL OR p.category_id = #{categoryId}) " +
            "ORDER BY p.search_count DESC, p.created_at DESC " +
            "LIMIT #{limit}")
    List<ProductVO> selectHotProducts(@Param("categoryId") Long categoryId, 
                                     @Param("limit") Integer limit);

    /**
     * 获取产品价格信息
     * 
     * @param productId 产品ID
     * @param stationId 站点ID
     * @param city 城市
     * @return 价格信息
     */
    Object selectProductPrices(@Param("productId") Long productId, 
                              @Param("stationId") Long stationId, 
                              @Param("city") String city);

    /**
     * 增加产品搜索次数
     * 
     * @param productId 产品ID
     * @return 影响行数
     */
    @Select("UPDATE products SET search_count = search_count + 1 WHERE id = #{productId}")
    int incrementSearchCount(@Param("productId") Long productId);
}
