import React, { memo } from 'react'
import { shallowEqual, useDispatch, useSelector } from 'react-redux'
import propTypes from 'prop-types'
import { NavLink } from 'react-router-dom'
import { useAddPlaylist } from '../../../../hooks/changeMusic'
import { getSizeImage } from '../../../../utils/formatUtils'
import { getSongDetailAction } from '../../../../components/playerBar/store'
import { PlayCircleOutlined } from '@ant-design/icons'
import { message } from 'antd'
import './style.css'

function SongItem(props: any) {
  // props/state
  const {
    currentRanking,
    coverPic,
    duration,
    singer,
    singerId,
    songId,
    songName,
  } = props

  // redux hook
  const dispatch = useDispatch()
  const { playlist } = useSelector((state: any) => ({
    playlist: state.getIn(['player', 'playList'])
  }), shallowEqual)

  // other function
  const playMusic = (e: any, isTo = false) => {
    // 如果不跳转,那么组织超链接的默认行为
    if (!isTo) e.preventDefault()
    dispatch(getSongDetailAction(songId));
    // 播放音乐
    (document.getElementById('audio') as any).autoplay = true
  }

  const addPlaylist = useAddPlaylist(playlist, message)

  return (
    <div className='songItemBox'>
      <div className="songItemRankCount">{currentRanking}</div>
      {coverPic && (
        <NavLink to={`/song?id=${songId}`} className="songItemImg" onClick={(e) => playMusic(e, true)}>
          <img src={getSizeImage(coverPic, 50)} alt="" />
        </NavLink>
      )}
      <div className="songItemInfo" style={{ width: coverPic ? '258px' : '328px' }}>
        <PlayCircleOutlined className="songItemName" onClick={(e) => playMusic(e)} />
        <a href={`#/song?id=${songId}`} onClick={(e) => playMusic(e, true)} className="textNowrap">
          {songName}
        </a>
        <button className="songItemButton" onClick={e => addPlaylist(e, songId)}></button>
      </div>
      <div className="songItemDuration">{duration}</div>
      <NavLink to={`/artist?id=${singerId}`} onClick={(e) => playMusic(e, true)}>
        {singer}
      </NavLink>
    </div>
  )
}

SongItem.propTypes = {
  currentRanking: propTypes.number.isRequired,
  coverPic: propTypes.string,
  duration: propTypes.string.isRequired,
  singer: propTypes.string.isRequired,
  songId: propTypes.number.isRequired,
  singerId: propTypes.number.isRequired,
  songName: propTypes.string.isRequired,
}

export default memo(SongItem)
