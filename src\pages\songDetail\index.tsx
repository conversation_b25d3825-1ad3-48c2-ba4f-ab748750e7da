import React, { memo, useEffect, useState } from 'react'
import SongInfo from './songInfo'
import SimiSongItem from './simiSongItem'
import SongComment from './songComment'
import './style.css'
import { getSimilaritySong } from '../../request/player'
import { shallowEqual, useSelector } from 'react-redux'
import { formatMinuteSecond } from '../../utils/handleData'

// 歌曲详情页面
export default memo(function SongDetail() {
  // 之后根路id发送请求,数据保存在redux当中
  const [songlist, setSonglist] = useState([])
  // redux
  let { currentSongId } = useSelector(
    (state: any) => ({
      currentSongId: state.getIn(['player', 'currentSong', 'id']),
    }),
    shallowEqual
  )
  // other hook
  useEffect(() => {
    getSimilaritySong(currentSongId).then((res: any) => {
      setSonglist(res.songs)
    })
  }, [currentSongId])

  // custom hook
  return (
    <div className="songDetailBox">
      <div className="songDetailContent width980">
        <div className="songDetailLeft">
          <SongInfo />
          <SongComment />
        </div>
        <div className="songDetailRight">
          <div className='simiSongLine'>相似歌曲</div>
          {songlist &&
            songlist.map((item: any) => {
              return (
                <SimiSongItem
                  key={item.id}
                  coverPic={item.album.blurPicUrl}
                  className="simiSongItemBox"
                  duration={formatMinuteSecond(item.dt)}
                  songName={item.name}
                  singer={item.artists[0].name}
                  songId={item.id}
                />
              )
            })}</div>
      </div>
    </div>

  )
})
