import { Map } from "immutable";
import * as actionTypes from "./actionTypes";

const defaultState = Map({
  categoryIsVisible: false,
  category: [],
  currentCategory: "全部",
  categorySongs: {}
})

export default function reducer(state = defaultState, action: any) {
  switch(action.type) {
    case actionTypes.CHANGE_CATEGORY_IS_VISIBLE_STATE:
      return state.set('categoryIsVisible', action.categoryIsVisible);
    case actionTypes.CHANGE_CATEGORY:
      return state.set("category", action.category);
    case actionTypes.CHANGE_CURRENT_CATEGORY:
      return state.set("currentCategory", action.currentCategory);
    case actionTypes.CHANGE_CATEGORY_SONGS:
      return state.set("categorySongs", action.categorySongs);
    default:
      return state;
  }
}