import React, { memo } from 'react'

import { hotRadios } from '../../../../common/localData'
import { getSizeImage } from '../../../../utils/formatUtils'
import './style.css'

export default memo(function HotAnchor() {
  return (
    <div className="hotAnchor">
      <div className="hotAnchorHeader">
        <div className="hotAnchorTitle">热门主播</div>
      </div>
      <div className="hotAnchorContainer">
        {hotRadios.map((item: any) => {
          return (<div className="hotAnchorCover"  key={item.picUrl}>
            <div className="hotAnchorImage">
              <a href={item.url}>
                <img src={getSizeImage(item.picUrl, 40)} alt="" />
              </a>

            </div>
            <div className="hotAnchorInfo">
              <a href={item.url} className="hotAnchorName">{item.name}</a>
              <em className="hotAnchorDetail textNowrap">{item.details}</em>
            </div></div>
          )
        })}
      </div>
    </div>
  )
})
