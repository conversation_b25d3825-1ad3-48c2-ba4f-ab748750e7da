@startuml 慢慢回收数据库领域架构图

!theme plain
skinparam backgroundColor #FAFAFA
skinparam roundcorner 15

' 定义领域样式
skinparam component {
  BackgroundColor #E3F2FD
  BorderColor #1976D2
  FontSize 14
  FontStyle bold
}

skinparam package {
  BackgroundColor #F5F5F5
  BorderColor #757575
  FontSize 16
  FontStyle bold
}

' 定义箭头样式
skinparam arrow {
  Color #424242
  FontSize 12
}

title 慢慢回收小程序数据库领域架构图

' 核心领域
package "核心业务领域" as CoreDomain {
  
  component "👥 用户领域\nUser Domain" as UserDomain #E8F5E8 {
    [users] as u_users
    [user_addresses] as u_addresses
    
    u_users ||--o{ u_addresses : "1:N"
  }
  
  component "💰 产品领域\nProduct Domain" as ProductDomain #FFF3E0 {
    [categories] as p_categories
    [products] as p_products
    [prices] as p_prices
    
    p_categories ||--o{ p_categories : "树形"
    p_categories ||--o{ p_products : "1:N"
    p_products ||--o{ p_prices : "1:N"
  }
  
  component "🏪 站点领域\nStation Domain" as StationDomain #F3E5F5 {
    [stations] as s_stations
    [station_services] as s_services
    [reviews] as s_reviews
    
    s_stations ||--o{ s_services : "1:N"
    s_stations ||--o{ s_reviews : "1:N"
  }
}

' 支撑领域
package "支撑领域" as SupportDomain {
  
  component "📊 行为领域\nBehavior Domain" as BehaviorDomain #E0F2F1 {
    [search_logs] as b_search
    [favorites] as b_favorites
    [browse_logs] as b_browse
  }
  
  component "⚙️ 系统领域\nSystem Domain" as SystemDomain #FFF8E1 {
    [configs] as sys_configs
  }
}

' 定义领域间关系
UserDomain --> BehaviorDomain : "产生行为数据"
UserDomain --> StationDomain : "评价站点"
ProductDomain --> StationDomain : "站点服务"
SystemDomain --> CoreDomain : "配置支撑"
SystemDomain --> SupportDomain : "配置支撑"

' 添加详细说明
note top of UserDomain
  <b>职责：</b>
  • 用户身份管理
  • 用户基础信息
  • 用户地址管理
  
  <b>核心表：</b>
  • users (用户表)
  • user_addresses (地址表)
end note

note top of ProductDomain
  <b>职责：</b>
  • 产品分类管理
  • 产品信息维护
  • 价格数据管理
  
  <b>核心表：</b>
  • categories (分类表)
  • products (产品表)
  • prices (价格表)
end note

note top of StationDomain
  <b>职责：</b>
  • 回收站点管理
  • 站点服务定义
  • 用户评价管理
  
  <b>核心表：</b>
  • stations (站点表)
  • station_services (服务表)
  • reviews (评价表)
end note

note bottom of BehaviorDomain
  <b>职责：</b>
  • 用户行为记录
  • 数据分析支撑
  • 个性化推荐
  
  <b>核心表：</b>
  • search_logs (搜索记录)
  • favorites (收藏记录)
  • browse_logs (浏览记录)
end note

note bottom of SystemDomain
  <b>职责：</b>
  • 系统配置管理
  • 全局参数设置
  • 运行时配置
  
  <b>核心表：</b>
  • configs (配置表)
end note

' 添加数据流说明
note as DataFlow
  <b>数据流向：</b>
  1. 用户注册登录 → 用户领域
  2. 用户搜索查价 → 产品领域 + 行为领域
  3. 用户查看站点 → 站点领域 + 行为领域
  4. 用户评价站点 → 站点领域
  5. 系统配置 → 所有领域
end note

' 添加设计原则
note as DesignPrinciples
  <b>设计原则：</b>
  • <color:#52C41A>领域驱动</color>：按业务领域划分
  • <color:#52C41A>职责单一</color>：每个领域职责明确
  • <color:#52C41A>松耦合</color>：领域间通过外键关联
  • <color:#52C41A>高内聚</color>：领域内表关系紧密
  • <color:#52C41A>易扩展</color>：支持独立扩展
end note

DataFlow -[hidden]-> DesignPrinciples

center footer 🌿 慢慢回收 | 领域驱动设计 | 数据库架构 V2.0

@enduml
