const express = require('express');
const { validate, commonRules } = require('../middleware/validation');
const { optionalAuth, authenticateToken } = require('../middleware/auth');
const { asyncHandler } = require('../middleware/errorHandler');
const Response = require('../utils/response');
const logger = require('../utils/logger');
const priceService = require('../services/priceService');
const Joi = require('joi');

const router = express.Router();

/**
 * 实时价格查询验证模式
 */
const realTimePricesSchema = Joi.object({
  productIds: Joi.array().items(commonRules.id).max(50).optional(),
  stationIds: Joi.array().items(commonRules.id).max(20).optional(),
  priceType: Joi.number().integer().valid(1, 2, 3).default(1).messages({
    'any.only': '价格类型只能是1(收购价)、2(零售价)、3(批发价)'
  }),
  city: commonRules.city.optional(),
  qualityGrade: Joi.string().max(20).optional(),
  includeHistory: Joi.boolean().default(false),
  historyDays: Joi.number().integer().min(1).max(90).default(7)
});

/**
 * 价格趋势查询验证模式
 */
const priceTrendsSchema = Joi.object({
  productId: commonRules.id,
  timeRange: Joi.string().pattern(/^\d+[dwmy]$/).default('30d').messages({
    'string.pattern.base': '时间范围格式错误，如: 7d, 4w, 3m, 1y'
  }),
  granularity: Joi.string().valid('hourly', 'daily', 'weekly', 'monthly').default('daily'),
  includeVolume: Joi.boolean().default(true),
  includePrediction: Joi.boolean().default(true),
  city: commonRules.city.optional()
});

/**
 * 价格预警验证模式
 */
const priceAlertSchema = Joi.object({
  productId: commonRules.id,
  alertType: Joi.number().integer().valid(1, 2, 3).required().messages({
    'any.only': '预警类型只能是1(价格上涨)、2(价格下跌)、3(达到目标价)'
  }),
  targetPrice: commonRules.price.when('alertType', {
    is: 3,
    then: Joi.required(),
    otherwise: Joi.optional()
  }),
  thresholdRate: Joi.number().min(0.1).max(100).default(5.0).messages({
    'number.min': '阈值百分比至少0.1%',
    'number.max': '阈值百分比最多100%'
  }),
  notificationMethods: Joi.array().items(
    Joi.string().valid('push', 'sms', 'email')
  ).min(1).default(['push']),
  validUntil: Joi.date().iso().greater('now').optional()
});

/**
 * 批量价格更新验证模式
 */
const batchUpdateSchema = Joi.object({
  source: Joi.string().valid('official_market', 'station_reports', 'user_feedback', 'crawler_data').required(),
  timestamp: Joi.date().iso().required(),
  prices: Joi.array().items(
    Joi.object({
      productId: commonRules.id,
      stationId: commonRules.optionalId,
      priceType: Joi.number().integer().valid(1, 2, 3).required(),
      price: commonRules.price.required(),
      volume: Joi.number().integer().min(0).default(0),
      qualityGrade: Joi.string().max(20).optional(),
      changeReason: Joi.string().max(100).optional()
    })
  ).min(1).max(1000).required()
});

/**
 * @route GET /api/prices/realtime
 * @desc 获取实时价格行情
 * @access Public
 */
router.get('/realtime',
  validate(realTimePricesSchema, 'query'),
  optionalAuth,
  asyncHandler(async (req, res) => {
    const params = req.query;

    try {
      // 记录用户查询行为
      if (req.user && params.productIds) {
        await db.insert('search_logs', {
          user_id: req.user.id,
          keyword: `价格查询:${params.productIds.join(',')}`,
          type: 1,
          result_count: params.productIds.length,
          created_at: new Date()
        });
      }

      const priceData = await priceService.getRealTimePrices(params);

      return Response.success(res, priceData, '获取实时价格成功');

    } catch (error) {
      logger.error('获取实时价格失败:', error);
      return Response.internalError(res, '获取价格信息失败');
    }
  })
);

/**
 * @route GET /api/prices/trends
 * @desc 获取价格趋势分析
 * @access Public
 */
router.get('/trends',
  validate(priceTrendsSchema, 'query'),
  optionalAuth,
  asyncHandler(async (req, res) => {
    const params = req.query;

    try {
      const trendData = await priceService.getPriceTrends(params);

      return Response.success(res, trendData, '获取价格趋势成功');

    } catch (error) {
      logger.error('获取价格趋势失败:', error);
      return Response.internalError(res, '获取趋势分析失败');
    }
  })
);

/**
 * @route GET /api/prices/compare
 * @desc 价格比较
 * @access Public
 */
router.get('/compare',
  validate(Joi.object({
    productIds: Joi.array().items(commonRules.id).min(2).max(10).required(),
    location: Joi.object({
      lng: commonRules.longitude.required(),
      lat: commonRules.latitude.required(),
      radius: Joi.number().min(0.1).max(50).default(10)
    }).optional(),
    sortBy: Joi.string().valid('price_asc', 'price_desc', 'distance', 'rating').default('price_desc'),
    includeDistance: Joi.boolean().default(true)
  }), 'query'),
  optionalAuth,
  asyncHandler(async (req, res) => {
    const { productIds, location, sortBy, includeDistance } = req.query;

    try {
      // 获取产品价格信息
      const compareData = await priceService.comparePrices({
        productIds,
        location,
        sortBy,
        includeDistance
      });

      return Response.success(res, compareData, '价格比较成功');

    } catch (error) {
      logger.error('价格比较失败:', error);
      return Response.internalError(res, '价格比较失败');
    }
  })
);

/**
 * @route GET /api/prices/volatility-alerts
 * @desc 获取价格波动提醒
 * @access Public
 */
router.get('/volatility-alerts',
  optionalAuth,
  asyncHandler(async (req, res) => {
    try {
      const volatilityData = await priceService.getVolatilityAlerts();

      return Response.success(res, volatilityData, '获取波动提醒成功');

    } catch (error) {
      logger.error('获取波动提醒失败:', error);
      return Response.internalError(res, '获取波动信息失败');
    }
  })
);

/**
 * @route POST /api/prices/alerts
 * @desc 创建价格预警
 * @access Private
 */
router.post('/alerts',
  authenticateToken,
  validate(priceAlertSchema),
  asyncHandler(async (req, res) => {
    const alertData = {
      ...req.body,
      user_id: req.user.id
    };

    try {
      // 检查是否已存在相同的预警
      const existingAlert = await db.query(
        'SELECT id FROM price_alerts WHERE user_id = ? AND product_id = ? AND alert_type = ? AND status = 1',
        [req.user.id, alertData.productId, alertData.alertType]
      );

      if (existingAlert.length > 0) {
        return Response.conflict(res, '该产品已设置相同类型的价格预警');
      }

      const alertId = await db.insert('price_alerts', {
        user_id: alertData.user_id,
        product_id: alertData.productId,
        alert_type: alertData.alertType,
        target_price: alertData.targetPrice || 0,
        threshold_rate: alertData.thresholdRate,
        status: 1,
        created_at: new Date()
      });

      return Response.created(res, { alertId }, '价格预警创建成功');

    } catch (error) {
      logger.error('创建价格预警失败:', error);
      return Response.internalError(res, '创建预警失败');
    }
  })
);

/**
 * @route GET /api/prices/alerts
 * @desc 获取用户价格预警列表
 * @access Private
 */
router.get('/alerts',
  authenticateToken,
  validate(Joi.object({
    status: Joi.number().valid(0, 1).optional(),
    page: commonRules.page,
    limit: commonRules.limit
  }), 'query'),
  asyncHandler(async (req, res) => {
    const { status, page, limit } = req.query;

    try {
      let whereConditions = ['pa.user_id = ?'];
      let queryParams = [req.user.id];

      if (status !== undefined) {
        whereConditions.push('pa.status = ?');
        queryParams.push(status);
      }

      const whereClause = whereConditions.join(' AND ');

      const sql = `
        SELECT 
          pa.*,
          p.name as product_name,
          p.brand,
          p.model,
          c.name as category_name,
          rtp.current_price
        FROM price_alerts pa
        JOIN products p ON pa.product_id = p.id
        JOIN categories c ON p.category_id = c.id
        LEFT JOIN real_time_prices rtp ON pa.product_id = rtp.product_id AND rtp.station_id IS NULL
        WHERE ${whereClause}
        ORDER BY pa.created_at DESC
      `;

      const result = await db.paginate(sql, queryParams, page, limit);

      const alerts = result.data.map(alert => ({
        id: alert.id,
        productId: alert.product_id,
        productName: alert.product_name,
        brand: alert.brand,
        model: alert.model,
        category: alert.category_name,
        alertType: alert.alert_type,
        targetPrice: alert.target_price,
        currentPrice: alert.current_price,
        thresholdRate: alert.threshold_rate,
        isTriggered: alert.is_triggered === 1,
        triggeredAt: alert.triggered_at,
        status: alert.status,
        createdAt: alert.created_at
      }));

      return Response.paginate(res, alerts, result.pagination, '获取预警列表成功');

    } catch (error) {
      logger.error('获取价格预警列表失败:', error);
      return Response.internalError(res, '获取预警列表失败');
    }
  })
);

/**
 * @route PUT /api/prices/alerts/:id
 * @desc 更新价格预警
 * @access Private
 */
router.put('/alerts/:id',
  authenticateToken,
  validate(Joi.object({ id: commonRules.id }), 'params'),
  validate(Joi.object({
    targetPrice: commonRules.price.optional(),
    thresholdRate: Joi.number().min(0.1).max(100).optional(),
    status: Joi.number().valid(0, 1).optional()
  })),
  asyncHandler(async (req, res) => {
    const { id } = req.params;
    const updateData = req.body;

    try {
      // 验证预警是否属于当前用户
      const alert = await db.query(
        'SELECT id FROM price_alerts WHERE id = ? AND user_id = ?',
        [id, req.user.id]
      );

      if (alert.length === 0) {
        return Response.notFound(res, '价格预警不存在');
      }

      const affectedRows = await db.update('price_alerts', updateData, 'id = ?', [id]);

      if (affectedRows === 0) {
        return Response.notFound(res, '更新失败');
      }

      return Response.success(res, null, '价格预警更新成功');

    } catch (error) {
      logger.error('更新价格预警失败:', error);
      return Response.internalError(res, '更新预警失败');
    }
  })
);

/**
 * @route DELETE /api/prices/alerts/:id
 * @desc 删除价格预警
 * @access Private
 */
router.delete('/alerts/:id',
  authenticateToken,
  validate(Joi.object({ id: commonRules.id }), 'params'),
  asyncHandler(async (req, res) => {
    const { id } = req.params;

    try {
      const affectedRows = await db.delete(
        'price_alerts',
        'id = ? AND user_id = ?',
        [id, req.user.id]
      );

      if (affectedRows === 0) {
        return Response.notFound(res, '价格预警不存在');
      }

      return Response.success(res, null, '价格预警删除成功');

    } catch (error) {
      logger.error('删除价格预警失败:', error);
      return Response.internalError(res, '删除预警失败');
    }
  })
);

/**
 * @route POST /api/prices/batch-update
 * @desc 批量更新价格 (内部接口)
 * @access Private (需要管理员权限)
 */
router.post('/batch-update',
  authenticateToken,
  require('../middleware/auth').requireAdmin,
  validate(batchUpdateSchema),
  asyncHandler(async (req, res) => {
    const updateData = req.body;

    try {
      const result = await priceService.batchUpdatePrices(updateData);

      return Response.success(res, result, '批量更新价格成功');

    } catch (error) {
      logger.error('批量更新价格失败:', error);
      return Response.internalError(res, '批量更新失败');
    }
  })
);

/**
 * @route GET /api/prices/product/:id/history
 * @desc 获取产品价格历史
 * @access Public
 */
router.get('/product/:id/history',
  validate(Joi.object({ id: commonRules.id }), 'params'),
  validate(Joi.object({
    days: Joi.number().integer().min(1).max(365).default(30),
    stationId: commonRules.optionalId,
    granularity: Joi.string().valid('hourly', 'daily', 'weekly').default('daily')
  }), 'query'),
  optionalAuth,
  asyncHandler(async (req, res) => {
    const { id } = req.params;
    const { days, stationId, granularity } = req.query;

    try {
      const historyData = await priceService.getProductPriceHistory({
        productId: id,
        days,
        stationId,
        granularity
      });

      return Response.success(res, historyData, '获取价格历史成功');

    } catch (error) {
      logger.error('获取价格历史失败:', error);
      return Response.internalError(res, '获取价格历史失败');
    }
  })
);

module.exports = router;
