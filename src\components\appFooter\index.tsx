import React, { Fragment, memo } from 'react'
import { footerLinks, footerImages } from '../../common/localData'
import './style.css'

type CopyRightItem = {
  title: string;
  link: string;
}

type UnitsItem = {
  link: string;
}
export default memo(function AppFooter() {
  const showCopyRight = (item: CopyRightItem) => {
    return (
      <Fragment key={item.title}>
        <a href={item.link}>{item.title}</a>
        <span className="appFooterLine">|</span>
      </Fragment>
    )
  }
  // 底部右侧
  const showUnits = (item: UnitsItem) => {
    return (
      <li key={item.link} className="appFooterRightItem">
        <a href={item.link} rel="noopener noreferrer" target="_blank" className="appFooterRightLink"></a>
        <span className="appFooterRightTitle"></span>
      </li>
    )
  }
  return (
    <div className='footerBox' >
      <div className="footerContent width980">
        <div className='footerLeft'>
          <p className="footerCopyRight">{footerLinks.map(item => showCopyRight(item))}</p>
          <p>
            <span className="footerCompany">网易公司版权所有©1997-2020</span>
            <span>杭州乐读科技有限公司运营：</span>
            <a rel="noopener noreferrer" target="_blank" href="https://p5.music.126.net/obj/wo3DlcOGw6DClTvDisK1/8282703158/452a/ca0c/3a10/caad83bc8ffaa850a9dc1613d74824fc.png">浙网文[2018]3506-263号</a>
          </p>
          <p>
            <span className="footerAlert">违法和不良信息举报电话：0571-89853516</span>
            <span>举报邮箱：<EMAIL></span>
          </p>
          <p>
            <a rel="noopener noreferrer" target="_blank" href="https://beian.miit.gov.cn/#/Integrated/index" className="footerManageSystem">粤B2-20090191-18  工业和信息化部备案管理系统网站</a>
            <span>浙公网安备</span>
            <a rel="noopener noreferrer" target="_blank" href="https://www.beian.gov.cn/portal/registerSystemInfo?recordcode=33010902002564"> 33010902002564号</a>
          </p>
        </div>
        <div className="footerRight">
          {footerImages.map(item => showUnits(item))}
        </div>
      </div>
    </div>
  )
})
