import {Map} from 'immutable'
import { defaultMusic, MusicDetail } from '../../../models/music'
import * as actionTypes from './actionTypes'

const defaultState = Map({
  searchSongList: [defaultMusic],
  focusState: false
})

type actionType = {
  type: string,
  searchSongList: Array<MusicDetail>,
  focusState: boolean,
  index: number
}

function reducer(state = defaultState, action: actionType) {
  switch(action.type) {
    case actionTypes.CHANGE_SEARCH_SONG_LIST:
      return state.set('searchSongList', action.searchSongList)
    case actionTypes.CHANGE_FOCUS_STATE:
      return state.set('focusState', action.focusState)
    default:
      return state
  }
}

export default reducer