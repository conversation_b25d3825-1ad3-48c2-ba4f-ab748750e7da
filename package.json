{"name": "kkmusic", "version": "0.1.0", "private": true, "homepage": "./", "dependencies": {"@testing-library/jest-dom": "^5.16.1", "@testing-library/react": "^12.1.2", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.0.3", "@types/node": "^16.11.14", "@types/react": "^17.0.37", "@types/react-dom": "^17.0.11", "antd": "^4.17.3", "axios": "^0.24.0", "from": "^0.1.7", "import": "^0.0.6", "nprogress": "^0.2.0", "qs": "^6.10.2", "query-string": "^7.0.1", "react": "^17.0.2", "react-dom": "^17.0.2", "react-draggable": "^4.4.4", "react-scripts": "5.0.0", "react-transition-group": "^4.4.2", "redux-devtools-extension": "^2.13.9", "redux-immutable": "^4.0.0", "redux-thunk": "^2.4.1", "sortablejs": "^1.14.0", "styled-components": "^5.3.3", "tdesign-react": "^0.21.0", "typescript": "^4.5.4", "web-vitals": "^2.1.2"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/nprogress": "^0.2.0", "@types/react-redux": "^7.1.20", "@types/react-router-config": "^5.0.3", "@types/react-transition-group": "^4.4.4", "@types/redux-immutable": "^4.0.1", "@types/sortablejs": "^1.10.7", "@types/styled-components": "^5.1.19", "react-redux": "^7.2.6", "react-router-config": "^5.1.1", "react-router-dom": "^6.2.1"}}