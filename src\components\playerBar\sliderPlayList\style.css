.sliderPlayListBox {
    position: absolute;
    left: -445%;
    bottom: 51px;
    width: 986px;
    height: 301px;
    border-radius: 7px;
    cursor: default;
}

.sliderPlayListHeader {
    display: flex;
    height: 40px;
    background: url(../../../static/images/playlist_bg.png) 0 0;
}

.sliderPlayListHeader .sliderPlayListHeaderLeft {
    width: 553px;
    display: flex;
    justify-content: space-between;
    line-height: 39px;
    padding: 0 10px 0 15px;
}
.sliderPlayListHeader .sliderPlayListHeaderLeft .sliderPlayListHeaderTitle {
    font-size: 14px;
    font-weight: bold;
    color: #e2e2e2;
}

.sliderPlayListHeader .sliderPlayListHeaderLeft .sliderPlayListHeaderIcon {
    padding: 5px;
    line-height: 33px;
    color: #ccc;
}
.sliderPlayListHeader .sliderPlayListHeaderLeft .sliderPlayListHeaderIcon span {
    margin-left: 5px;
}

.sliderPlayListHeader .sliderPlayListHeaderRight {
    display: flex;
    align-items: center;
    width: 432px;
    line-height: 40px;
    color: #fff;
    text-align: center;
}
.sliderPlayListHeader .sliderPlayListHeaderRight .sliderPlayListHeaderSongName {
    width: 94%;
    text-align: center;
}

.sliderPlayListHeader .sliderPlayListHeaderRight .sliderPlayListHeaderClose {
    width: 5%;
    cursor: pointer;
}



.sliderPlayListMainBox{
  display: flex;
  height: 260px;
  background: url(../../../static/images/playlist_bg.png);
  background-position: -1014px 0;
  background-repeat: repeat-y;
}

.sliderPlayListMainBox .sliderPlayListMain{
    width: 555px;
    padding-left: 2px;
    overflow: auto;
}
    /* 滚动条 */
.sliderPlayListMainBox .sliderPlayListMain::-webkit-scrollbar-thumb:horizontal {
    /*水平滚动条的样式*/
    width: 4px;
    background-color: #9f9f9f;
    -webkit-border-radius: 4px;
 }

.sliderPlayListMainBox .sliderPlayListMain::-webkit-scrollbar-track-piece {
    background-color: #1a1a1a; /*滚动条的背景颜色*/
    -webkit-border-radius: 0; /*滚动条的圆角宽度*/

}
.sliderPlayListMainBox .sliderPlayListMain::-webkit-scrollbar {
    width: 8px; /*滚动条的宽度*/
    height: 6px; /*滚动条的高度*/
}
.sliderPlayListMainBox .sliderPlayListMain::-webkit-scrollbar-thumb:vertical {
    /*垂直滚动条的样式*/
    height: 50px;
    background-color: #9f9f9f;
    -webkit-border-radius: 4px;
    /* outline: 2px solid #000; */
    /* outline-offset: -2px; */
    border: 2px solid #9f9f9f;
}

.sliderPlayListMainBox .sliderPlayListMain .active {
    background-color: rgba(0, 0, 0, 0.4);
}

.sliderPlayListMainBox .sliderPlayListMainLine {
    width: 6px;
    height: 258px;
    background: #000;
    margin-top: 2px;
}


.lyric-css .anticon {
    display: none;
}