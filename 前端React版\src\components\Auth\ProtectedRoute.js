import React, { useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { Navigate, useLocation } from 'react-router-dom';
import { Spin } from 'antd';
import { verifyToken } from '../../store/actions/authActions';

const ProtectedRoute = ({ children }) => {
  const dispatch = useDispatch();
  const location = useLocation();
  const { isAuthenticated, token, loading } = useSelector(state => state.auth);

  useEffect(() => {
    // 如果有token但未认证，尝试验证token
    if (token && !isAuthenticated && !loading) {
      dispatch(verifyToken()).catch(() => {
        // 验证失败会在action中处理，这里不需要额外处理
      });
    }
  }, [dispatch, token, isAuthenticated, loading]);

  // 正在验证token
  if (token && !isAuthenticated && loading) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100vh' 
      }}>
        <Spin size="large" tip="验证登录状态..." />
      </div>
    );
  }

  // 未认证，重定向到登录页
  if (!isAuthenticated) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // 已认证，渲染子组件
  return children;
};

export default ProtectedRoute;
