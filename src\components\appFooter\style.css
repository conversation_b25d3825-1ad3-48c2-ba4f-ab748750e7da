.footerBox {
  width: 100%;
  height: 160px;
  border-top: 1px solid #d3d3d3;
  background-color: #f2f2f2;
  margin-bottom: 53px;
}
.footerBox .footerContent {
    display: flex;
    justify-content: space-between;
    height: 100%;
}

.footerLeft{
  width: 520px;
  padding-top: 15px;
  line-height: 24px;
}

.footerLeft .footerCopyRight > a {
    color: #999;
}

.footerLeft .appFooterLine {
    margin: 0 8px 0 10px;
    color: #c2c2c2;
}
.footerLeft .appFooterLine:last-child{
    display: none;
}

.footerLeft .footerCompany,
.footerLeft .footerAlert, 
.footerLeft .footerManageSystem{
    margin-right: 10px;
}
.footerRight {
  display: flex;
  width: 420px;
  margin-top: 33px;
}

.footerRight .appFooterRightItem {
    display: flex;
    flex-direction: column;
    margin-right: 40px;
}
.footerRight .appFooterRightItem .appFooterRightLink {
    display: block;
    width: 50px;
    height: 45px;
    background-image: url(../../static/images/sprite_footer_02.png);
    background-size: 110px 450px;
}

.footerRight .appFooterRightItem:nth-child(1) .appFooterRightLink {
    background-position: -60px -101px;
}

.footerRight .appFooterRightItem:nth-child(2) .appFooterRightLink {
    background-position: 0 0;
}

.footerRight .appFooterRightItem:nth-child(3) .appFooterRightLink {
    background-position: -60px -50px;
}

.footerRight .appFooterRightItem:nth-child(4) .appFooterRightLink {
    background-position: 0 -101px;
}

.footerRight .appFooterRightItem .appFooterRightTitle {
    margin-top: 5px;
    display: block;
    width: 52px;
    height: 10px;
    background-image: url(../../static/images/sprite_footer_01.png);
    background-size: 180px 100px;
}

.footerRight .appFooterRightItem:nth-child(1) .appFooterRightTitle {
    background-position: -1px -90px;
}

.footerRight .appFooterRightItem:nth-child(2) .appFooterRightTitle {
    background-position: 0 0;
    margin-top: 6px;
}

.footerRight .appFooterRightItem:nth-child(3) .appFooterRightTitle {
    background-position: 0 -54px;
    margin-top: 6px;
}

.footerRight .appFooterRightItem:nth-child(4) .appFooterRightTitle {
    background-position: -1px -72px;
    margin-top: 6px;
}

