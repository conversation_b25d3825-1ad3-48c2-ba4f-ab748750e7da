
import { address } from "../common/localData"
export function getSizeImage(imgUrl: any, size: number) {
  return `${imgUrl}?param=${size}x${size}`
}

export function getCount(count: number) {
  if (count < 0) return
  if (count < 10000) {
    return count
  } else if (Math.floor(count / 10000) < 10000) {
    return Math.floor(count / 1000) / 10 + '万'
  } else {
    return Math.floor(count / 10000000) / 10 + '亿'
  }
}


// 函数防抖: 解决refresh频繁刷新
export function debounce(func: any, delay: number) {
  let timer: any = null;
  return () => {
    if (timer !== null) {
      clearTimeout(timer)
    }
    timer = setTimeout(func, delay)
  }
}

/**
 *
 * @param {String} loginState 登录模式
 */
export function getParseLoginState(loginState: String) {
  let loginMode = ''
  switch (loginState) {
    case 'phone':
      loginMode = '手机号'
      break
    case 'email':
      loginMode = '邮箱'
      break
    default:
      loginMode = '手机号'
      break
  }
  return loginMode
}

/**
 * 根据不同登录方式,返回匹配对应的正则
 * @param {String} loginState 登录状态
 */
export function getMatchReg(loginState: String) {
  switch (loginState) {
    case 'phone':
      return /^(?:(?:\+|00)86)?1[3-9]\d{9}$/
    case 'email':
      return /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
    default:
      return ''
  }
}

export const getRandomNumber = (num: number) => {
  return Math.floor(Math.random() * num)
}

export const getFindIdIndex = (arr: any, findId: any) => {
  return arr.findIndex((song: any) => song.id === findId)
}

export function getCity(id: any) {
  return address[id]
}
