import request from './request'
/* 手机号登录 */
export function gotoPhoneLogin(phone: any, password: any, countrycode: any) {
  return request({
    url: '/login/cellphone',
    method: 'get',
    params: {
      phone,
      password,
      countrycode
    },
  })
}

/* 邮箱登录 */
export function gotoEmailLogin(email: any, password: any) {
  return request({
    url: '/login',
    method: 'get',
    params: {
      email,
      password
    },
  })
}

// 发送验证码
export function sendRegisterCode(phone: any) {
  return request({
    url: '/captcha/sent',
    method: 'get',
    params: {
      phone,
    },
  })
}

/* 注册 */
export function sendRegister(captcha: any, phone: any, password: any, nickname: any) {
  return request({
    url: '/register/cellphone',
    method: 'get',
    params: {
      captcha,
      phone,
      password,
      nickname,
    },
  })
}
