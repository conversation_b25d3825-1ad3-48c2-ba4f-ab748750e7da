import React, { memo, useEffect, useRef, useCallback, useState } from 'react'
import { shallowEqual, useDispatch, useSelector } from 'react-redux'
import { getTopBannersAction } from '../store/actionCreators'
import { Carousel } from 'antd';
import './style.css'
import { getSongDetailAction } from '../../../../components/playerBar/store/actionCreators';
export default memo(function TopBanner() {
    const [currentIndex, setCurrentIndex] = useState(0)

    const dispatch = useDispatch()
    const { topBanners } = useSelector((state: any) => ({
        topBanners: state.getIn(["recommend", "topBanners"])
    }), shallowEqual)

    useEffect(() => {
        dispatch(getTopBannersAction())
    }, [dispatch])

    const bannerRef: any = useRef()

    const bannerChange = useCallback((from, to) => {
        setCurrentIndex(to)
    }, [])


    const bgImage: string = topBanners[currentIndex] && (topBanners[currentIndex].imageUrl + "?imageView&blur=40x20")

    return (
        <div className='topBannerBox' style={{ backgroundImage: `url(${bgImage})` }}>
            <div className="topBanner width980">
                <div className="topBannerLeft">
                    <Carousel effect="fade" autoplay ref={bannerRef} beforeChange={bannerChange}>
                        {
                            topBanners.map((item: any) => {

                                return (
                                    <div className="topBannerItem" key={item.imageUrl} >
                                        <a href={`#/song?id=${item.encodeId}`} onClick={()=> dispatch(getSongDetailAction(parseInt(item.encodeId)))}>
                                            <img className="topBannerImage" src={item.imageUrl} alt={item.typeTitle} />
                                        </a>
                                    </div>
                                )
                            })
                        }
                    </Carousel>
                </div>

                <div className='topBannerRight'>
                    <a href='https://d1.music.126.net/dmusic/cloudmusicsetup2.8.0.198822.exe' target='_blank' rel="noreferrer">PC 安卓 iPhone WP iPad Mac 六大客户端</a>
                </div>
                <div className="topBannerControl">
                    <button className="topBannerButton left" onClick={() => bannerRef.current.prev()}></button>
                    <button className="topBannerButton right" onClick={() => bannerRef.current.next()}></button>
                </div>
            </div>
        </div>
    )

})
