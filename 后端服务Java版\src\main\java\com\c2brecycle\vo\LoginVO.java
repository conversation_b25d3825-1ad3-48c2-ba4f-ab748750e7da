package com.c2brecycle.vo;

import com.c2brecycle.entity.User;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

/**
 * 登录响应VO
 * 
 * <AUTHOR> Recycle Team
 * @since 2024-01-15
 */
@Data
@Builder
@ApiModel("登录响应")
public class LoginVO {

    @ApiModelProperty("访问令牌")
    private String token;

    @ApiModelProperty("刷新令牌")
    private String refreshToken;

    @ApiModelProperty("用户信息")
    private User user;

    @ApiModelProperty("是否新用户")
    private Boolean isNewUser;

    @ApiModelProperty("令牌过期时间(秒)")
    private Long expiresIn;
}
