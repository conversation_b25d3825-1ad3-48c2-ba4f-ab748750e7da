# 服务器配置
server:
  port: 8080
  servlet:
    context-path: /api
    encoding:
      charset: UTF-8
      enabled: true
      force: true

# Spring配置
spring:
  application:
    name: c2b-recycle-api
  
  # 环境配置
  profiles:
    active: dev
  
  # 数据源配置
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **********************************************************************************************************************************************************************************
    username: root
    password: 123456
    
    # Druid连接池配置
    druid:
      initial-size: 5
      min-idle: 5
      max-active: 20
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1 FROM DUAL
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      # 监控配置
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        login-username: admin
        login-password: admin123
      filter:
        stat:
          enabled: true
          log-slow-sql: true
          slow-sql-millis: 2000
  
  # Redis配置
  redis:
    host: localhost
    port: 6379
    password: 
    database: 0
    timeout: 3000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0
        max-wait: -1ms
  
  # Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    default-property-inclusion: non_null
    serialization:
      write-dates-as-timestamps: false
  
  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 50MB

# MyBatis Plus配置
mybatis-plus:
  # 实体类扫描路径
  type-aliases-package: com.c2brecycle.entity
  # Mapper XML文件路径
  mapper-locations: classpath*:mapper/*.xml
  # 全局配置
  global-config:
    db-config:
      # 主键类型
      id-type: auto
      # 逻辑删除字段
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
      # 字段策略
      insert-strategy: not_null
      update-strategy: not_null
      select-strategy: not_empty
  # 配置
  configuration:
    # 开启驼峰命名转换
    map-underscore-to-camel-case: true
    # 开启缓存
    cache-enabled: true
    # 日志
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

# 日志配置
logging:
  level:
    com.c2brecycle: debug
    com.c2brecycle.mapper: debug
    org.springframework.web: info
  pattern:
    console: '%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n'
    file: '%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n'
  file:
    name: logs/c2b-recycle.log
    max-size: 100MB
    max-history: 30

# 应用配置
app:
  # JWT配置
  jwt:
    secret: c2c-restore-jwt-secret-key-2024
    expiration: 604800 # 7天，单位秒
    refresh-expiration: 2592000 # 30天，单位秒
    header: Authorization
    prefix: Bearer
  
  # 微信小程序配置
  wechat:
    app-id: your_wechat_app_id
    app-secret: your_wechat_app_secret
    login-url: https://api.weixin.qq.com/sns/jscode2session
  
  # 文件上传配置
  upload:
    path: /data/uploads/
    max-size: 10485760 # 10MB
    allowed-types: jpg,jpeg,png,gif,pdf,doc,docx
  
  # 价格配置
  price:
    # 价格更新间隔（分钟）
    update-interval: 5
    # 价格异常阈值（百分比）
    anomaly-threshold: 30
    # 历史数据保留天数
    history-retention-days: 90
  
  # 限流配置
  rate-limit:
    # 每分钟请求次数
    requests-per-minute: 60
    # 登录接口每小时请求次数
    login-requests-per-hour: 10

# Actuator监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized
  metrics:
    export:
      prometheus:
        enabled: true

# Swagger文档配置
springfox:
  documentation:
    swagger-ui:
      enabled: true
    swagger:
      v2:
        path: /api-docs

---
# 开发环境配置
spring:
  profiles: dev
  
  datasource:
    url: ****************************************************************************************************************************************************
    username: root
    password: 123456

logging:
  level:
    root: info
    com.c2crestore: debug

---
# 测试环境配置
spring:
  profiles: test
  
  datasource:
    url: ******************************************************************************************************************************************************
    username: test_user
    password: test_password

logging:
  level:
    root: warn
    com.c2crestore: info

---
# 生产环境配置
spring:
  profiles: prod
  
  datasource:
    url: *************************************************************************************************************************************************
    username: ${DB_USERNAME:prod_user}
    password: ${DB_PASSWORD:prod_password}
  
  redis:
    host: ${REDIS_HOST:prod-redis}
    password: ${REDIS_PASSWORD:}

app:
  jwt:
    secret: ${JWT_SECRET:your-production-jwt-secret}
  wechat:
    app-id: ${WECHAT_APP_ID:your_prod_app_id}
    app-secret: ${WECHAT_APP_SECRET:your_prod_app_secret}

logging:
  level:
    root: warn
    com.c2crestore: info
  file:
    name: /var/log/c2c-restore/app.log
