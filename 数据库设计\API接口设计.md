# 🔌 慢慢回收小程序 API接口设计

## 📋 接口概述

### 基础信息
- **Base URL**: `https://api.manmanrecycle.com/v1`
- **协议**: HTTPS
- **数据格式**: JSON
- **字符编码**: UTF-8
- **认证方式**: JWT Token

### 通用响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": {},
  "timestamp": 1642234567890
}
```

### 状态码说明
- `200`: 成功
- `400`: 请求参数错误
- `401`: 未授权
- `403`: 禁止访问
- `404`: 资源不存在
- `500`: 服务器内部错误

---

## 🔐 1. 用户认证接口

### 1.1 微信登录
**接口**: `POST /auth/wechat-login`

**请求参数**:
```json
{
  "code": "微信授权码",
  "encryptedData": "加密数据",
  "iv": "初始向量"
}
```

**响应数据**:
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "JWT_TOKEN",
    "userInfo": {
      "id": 1,
      "openid": "wx_openid_001",
      "nickname": "环保小达人",
      "avatar": "https://example.com/avatar.jpg",
      "phone": "138****8888"
    }
  }
}
```

### 1.2 刷新Token
**接口**: `POST /auth/refresh-token`

**请求头**: `Authorization: Bearer {token}`

**响应数据**:
```json
{
  "code": 200,
  "message": "Token刷新成功",
  "data": {
    "token": "NEW_JWT_TOKEN",
    "expiresIn": 7200
  }
}
```

---

## 🗺️ 2. 地图和站点接口

### 2.1 获取附近回收站点
**接口**: `GET /stations/nearby`

**请求参数**:
```
longitude: 116.4074 (经度)
latitude: 39.9042 (纬度)
radius: 5 (搜索半径，单位km，默认5)
category: 1 (分类ID，可选)
status: 1 (站点状态，可选)
page: 1 (页码，默认1)
limit: 20 (每页数量，默认20)
```

**响应数据**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "total": 50,
    "page": 1,
    "limit": 20,
    "stations": [
      {
        "id": 1,
        "name": "绿色回收站",
        "phone": "010-12345678",
        "address": "北京市朝阳区建国门外大街100号",
        "longitude": 116.4074,
        "latitude": 39.9042,
        "distance": 0.5,
        "rating": 4.8,
        "reviewCount": 156,
        "status": 1,
        "businessHours": "08:00-18:00",
        "features": ["现金结算", "免费停车", "专业称重"],
        "services": [
          {
            "categoryId": 5,
            "categoryName": "电脑配件",
            "priceRange": "50-1500元/台"
          }
        ]
      }
    ]
  }
}
```

### 2.2 获取站点详情
**接口**: `GET /stations/{id}`

**响应数据**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "id": 1,
    "name": "绿色回收站",
    "phone": "010-12345678",
    "address": "北京市朝阳区建国门外大街100号",
    "longitude": 116.4074,
    "latitude": 39.9042,
    "businessHours": "08:00-18:00",
    "description": "专业回收电子产品和金属类废品",
    "images": ["https://example.com/station1.jpg"],
    "rating": 4.8,
    "reviewCount": 156,
    "status": 1,
    "features": ["现金结算", "免费停车", "专业称重"],
    "services": [
      {
        "categoryId": 5,
        "categoryName": "电脑配件",
        "minPrice": 50.00,
        "maxPrice": 1500.00,
        "unit": "台"
      }
    ],
    "reviews": [
      {
        "id": 1,
        "userName": "环保小达人",
        "userAvatar": "https://example.com/avatar.jpg",
        "rating": 5,
        "content": "服务很好，价格公道！",
        "createdAt": "2024-01-15 10:30:00"
      }
    ]
  }
}
```

### 2.3 搜索回收站点
**接口**: `GET /stations/search`

**请求参数**:
```
keyword: "绿色回收" (搜索关键词)
city: "北京市" (城市，可选)
category: 1 (分类ID，可选)
page: 1
limit: 20
```

---

## 💰 3. 产品和价格接口

### 3.1 获取产品分类
**接口**: `GET /categories`

**响应数据**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": 1,
      "name": "电子产品",
      "icon": "💻",
      "description": "电脑、手机、数码产品等",
      "children": [
        {
          "id": 5,
          "name": "电脑配件",
          "icon": "💻",
          "description": "CPU、显卡、主板等"
        }
      ]
    }
  ]
}
```

### 3.2 搜索产品
**接口**: `GET /products/search`

**请求参数**:
```
keyword: "iPhone" (搜索关键词)
category: 6 (分类ID，可选)
page: 1
limit: 20
```

**响应数据**:
```json
{
  "code": 200,
  "message": "搜索成功",
  "data": {
    "total": 10,
    "products": [
      {
        "id": 4,
        "name": "iPhone手机",
        "brand": "Apple",
        "model": "iPhone 13 Pro",
        "image": "https://example.com/iphone.jpg",
        "categoryName": "手机数码",
        "priceRange": "3000-4500元/台",
        "avgPrice": 3750.00
      }
    ]
  }
}
```

### 3.3 获取产品价格
**接口**: `GET /products/{id}/prices`

**请求参数**:
```
stationId: 1 (站点ID，可选)
days: 7 (天数，默认7天)
```

**响应数据**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "productInfo": {
      "id": 4,
      "name": "iPhone手机",
      "brand": "Apple",
      "model": "iPhone 13 Pro"
    },
    "currentPrice": {
      "minPrice": 3000.00,
      "maxPrice": 4500.00,
      "avgPrice": 3750.00,
      "unit": "台",
      "updateTime": "2024-01-15 10:00:00"
    },
    "priceHistory": [
      {
        "date": "2024-01-15",
        "avgPrice": 3750.00
      },
      {
        "date": "2024-01-14",
        "avgPrice": 3720.00
      }
    ],
    "stationPrices": [
      {
        "stationId": 1,
        "stationName": "绿色回收站",
        "minPrice": 3000.00,
        "maxPrice": 4500.00,
        "avgPrice": 3750.00
      }
    ]
  }
}
```

---

## 👤 4. 用户相关接口

### 4.1 获取用户信息
**接口**: `GET /user/profile`

**请求头**: `Authorization: Bearer {token}`

**响应数据**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "id": 1,
    "nickname": "环保小达人",
    "avatar": "https://example.com/avatar.jpg",
    "phone": "138****8888",
    "city": "北京市",
    "joinDate": "2024-01-01",
    "stats": {
      "recycleCount": 15,
      "totalEarnings": 328.50,
      "carbonReduction": 12.5
    }
  }
}
```

### 4.2 更新用户信息
**接口**: `PUT /user/profile`

**请求参数**:
```json
{
  "nickname": "新昵称",
  "avatar": "https://example.com/new_avatar.jpg",
  "phone": "13800138001"
}
```

### 4.3 获取用户收藏
**接口**: `GET /user/favorites`

**请求参数**:
```
type: 1 (收藏类型: 1-站点 2-产品)
page: 1
limit: 20
```

### 4.4 添加收藏
**接口**: `POST /user/favorites`

**请求参数**:
```json
{
  "targetType": 1,
  "targetId": 1
}
```

### 4.5 取消收藏
**接口**: `DELETE /user/favorites/{id}`

---

## 📊 5. 数据统计接口

### 5.1 获取价格行情
**接口**: `GET /market/trends`

**请求参数**:
```
category: 1 (分类ID，可选)
city: "北京市" (城市，可选)
days: 30 (天数，默认30)
```

**响应数据**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "hotProducts": [
      {
        "productId": 4,
        "productName": "iPhone手机",
        "priceChange": 5.2,
        "changeType": "up",
        "currentPrice": 3750.00
      }
    ],
    "categoryTrends": [
      {
        "categoryId": 6,
        "categoryName": "手机数码",
        "avgPrice": 2500.00,
        "priceChange": 3.5,
        "changeType": "up"
      }
    ]
  }
}
```

### 5.2 获取用户统计
**接口**: `GET /user/statistics`

**请求头**: `Authorization: Bearer {token}`

**请求参数**:
```
period: "month" (统计周期: week/month/year)
```

---

## 📝 6. 评价反馈接口

### 6.1 提交站点评价
**接口**: `POST /stations/{id}/reviews`

**请求参数**:
```json
{
  "rating": 5,
  "content": "服务很好，价格公道！",
  "serviceRating": 5,
  "priceRating": 5,
  "environmentRating": 4,
  "images": ["https://example.com/review1.jpg"],
  "isAnonymous": false
}
```

### 6.2 获取站点评价
**接口**: `GET /stations/{id}/reviews`

**请求参数**:
```
page: 1
limit: 20
rating: 5 (评分筛选，可选)
```

---

## 🔍 7. 搜索相关接口

### 7.1 综合搜索
**接口**: `GET /search`

**请求参数**:
```
keyword: "iPhone" (搜索关键词)
type: "all" (搜索类型: all/product/station)
city: "北京市" (城市，可选)
page: 1
limit: 20
```

### 7.2 获取搜索建议
**接口**: `GET /search/suggestions`

**请求参数**:
```
keyword: "iP" (关键词前缀)
limit: 10
```

### 7.3 获取热门搜索
**接口**: `GET /search/hot-keywords`

**响应数据**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    "iPhone",
    "笔记本电脑",
    "废铜价格",
    "显卡回收",
    "冰箱"
  ]
}
```

---

## 🛠️ 8. 工具接口

### 8.1 地址解析
**接口**: `GET /tools/geocoding`

**请求参数**:
```
address: "北京市朝阳区建国门外大街100号"
```

**响应数据**:
```json
{
  "code": 200,
  "message": "解析成功",
  "data": {
    "longitude": 116.4074,
    "latitude": 39.9042,
    "province": "北京市",
    "city": "北京市",
    "district": "朝阳区"
  }
}
```

### 8.2 距离计算
**接口**: `GET /tools/distance`

**请求参数**:
```
fromLng: 116.4074
fromLat: 39.9042
toLng: 116.3112
toLat: 39.9991
```

---

## 📋 接口调用示例

### JavaScript示例
```javascript
// 获取附近回收站点
const response = await fetch('/api/v1/stations/nearby?longitude=116.4074&latitude=39.9042&radius=5', {
  method: 'GET',
  headers: {
    'Authorization': 'Bearer ' + token,
    'Content-Type': 'application/json'
  }
});

const data = await response.json();
console.log(data);
```

### 错误处理示例
```javascript
if (data.code !== 200) {
  switch(data.code) {
    case 401:
      // 重新登录
      break;
    case 404:
      // 资源不存在
      break;
    default:
      // 其他错误
      console.error(data.message);
  }
}
```

---

**📝 此API接口设计为MVP版本的核心接口，后续将根据功能需求逐步扩展和优化。**
