# 慢慢回收小程序组件库

## 🧩 基础组件

### 1. 按钮组件 (<PERSON><PERSON>)

```css
/* 主要按钮 */
.btn-primary {
  background: linear-gradient(135deg, #52C41A 0%, #73D13D 100%);
  color: #FFFFFF;
  border: none;
  border-radius: 8rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
  font-weight: 500;
  text-align: center;
  box-shadow: 0 4rpx 12rpx rgba(82, 196, 26, 0.3);
  transition: all 0.3s ease;
}

.btn-primary:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(82, 196, 26, 0.3);
}

/* 次要按钮 */
.btn-secondary {
  background: #FFFFFF;
  color: #52C41A;
  border: 2rpx solid #52C41A;
  border-radius: 8rpx;
  padding: 22rpx 46rpx;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn-secondary:active {
  background: #F6FFED;
  border-color: #389E0D;
  color: #389E0D;
}

/* 文字按钮 */
.btn-text {
  background: transparent;
  color: #52C41A;
  border: none;
  padding: 12rpx 24rpx;
  font-size: 28rpx;
  font-weight: 500;
}

/* 小尺寸按钮 */
.btn-small {
  padding: 16rpx 32rpx;
  font-size: 24rpx;
  border-radius: 6rpx;
}

/* 大尺寸按钮 */
.btn-large {
  padding: 32rpx 64rpx;
  font-size: 32rpx;
  border-radius: 12rpx;
}

/* 块级按钮 */
.btn-block {
  width: 100%;
  display: block;
}

/* 禁用状态 */
.btn-disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}
```

### 2. 卡片组件 (Card)

```css
.card {
  background: #FFFFFF;
  border-radius: 12rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  border: 1rpx solid #F0F0F0;
  transition: all 0.3s ease;
}

.card:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

/* 卡片头部 */
.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid #F0F0F0;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #262626;
}

.card-extra {
  font-size: 24rpx;
  color: #8C8C8C;
}

/* 卡片内容 */
.card-content {
  font-size: 28rpx;
  color: #595959;
  line-height: 1.6;
}

/* 卡片底部 */
.card-footer {
  margin-top: 24rpx;
  padding-top: 16rpx;
  border-top: 1rpx solid #F0F0F0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
```

### 3. 标签组件 (Tag)

```css
.tag {
  display: inline-flex;
  align-items: center;
  padding: 8rpx 16rpx;
  border-radius: 6rpx;
  font-size: 20rpx;
  font-weight: 500;
  line-height: 1;
}

/* 成功标签 */
.tag-success {
  background: #F6FFED;
  color: #52C41A;
  border: 1rpx solid #B7EB8F;
}

/* 信息标签 */
.tag-info {
  background: #E6F7FF;
  color: #1890FF;
  border: 1rpx solid #91D5FF;
}

/* 警告标签 */
.tag-warning {
  background: #FFF7E6;
  color: #FAAD14;
  border: 1rpx solid #FFD666;
}

/* 错误标签 */
.tag-error {
  background: #FFF2F0;
  color: #FF4D4F;
  border: 1rpx solid #FFADD2;
}

/* 默认标签 */
.tag-default {
  background: #FAFAFA;
  color: #595959;
  border: 1rpx solid #D9D9D9;
}

/* 小尺寸标签 */
.tag-small {
  padding: 4rpx 12rpx;
  font-size: 18rpx;
}

/* 大尺寸标签 */
.tag-large {
  padding: 12rpx 20rpx;
  font-size: 24rpx;
}
```

## 🎯 业务组件

### 4. 价格显示组件 (PriceDisplay)

```css
.price-display {
  display: flex;
  align-items: baseline;
  gap: 8rpx;
}

.price-value {
  font-size: 36rpx;
  font-weight: 600;
  color: #262626;
}

.price-unit {
  font-size: 24rpx;
  color: #8C8C8C;
}

.price-range {
  font-size: 28rpx;
  color: #595959;
}

.price-trend {
  display: flex;
  align-items: center;
  gap: 4rpx;
  font-size: 20rpx;
  font-weight: 500;
}

.price-up {
  color: #F5222D;
}

.price-down {
  color: #52C41A;
}

.price-stable {
  color: #8C8C8C;
}

.price-trend-icon {
  width: 16rpx;
  height: 16rpx;
}
```

### 5. 回收站点卡片 (StationCard)

```css
.station-card {
  background: #FFFFFF;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  border: 1rpx solid #F0F0F0;
}

.station-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.station-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #262626;
}

.station-rating {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.rating-stars {
  color: #FAAD14;
  font-size: 24rpx;
}

.rating-score {
  font-size: 24rpx;
  color: #595959;
}

.station-info {
  margin-bottom: 16rpx;
}

.station-address {
  font-size: 24rpx;
  color: #8C8C8C;
  margin-bottom: 8rpx;
}

.station-distance {
  font-size: 24rpx;
  color: #52C41A;
  font-weight: 500;
}

.station-status {
  display: inline-flex;
  align-items: center;
  gap: 8rpx;
  font-size: 20rpx;
  font-weight: 500;
}

.status-open {
  color: #52C41A;
}

.status-closed {
  color: #8C8C8C;
}

.status-busy {
  color: #FAAD14;
}

.station-services {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
  margin-bottom: 16rpx;
}

.service-tag {
  background: #F6FFED;
  color: #52C41A;
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
  font-size: 18rpx;
}

.station-actions {
  display: flex;
  gap: 16rpx;
}

.action-btn {
  flex: 1;
  padding: 16rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
  text-align: center;
  border: 1rpx solid #D9D9D9;
  background: #FFFFFF;
  color: #595959;
}

.action-btn-primary {
  background: #52C41A;
  color: #FFFFFF;
  border-color: #52C41A;
}
```

### 6. 产品分类卡片 (CategoryCard)

```css
.category-card {
  background: #FFFFFF;
  border-radius: 12rpx;
  padding: 24rpx;
  text-align: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  border: 1rpx solid #F0F0F0;
  transition: all 0.3s ease;
}

.category-card:active {
  transform: scale(0.95);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.category-icon {
  width: 96rpx;
  height: 96rpx;
  margin: 0 auto 16rpx;
  background: linear-gradient(135deg, #52C41A, #73D13D);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48rpx;
}

.category-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #262626;
  margin-bottom: 8rpx;
}

.category-price {
  font-size: 24rpx;
  color: #52C41A;
  font-weight: 500;
}

.category-desc {
  font-size: 20rpx;
  color: #8C8C8C;
  margin-top: 8rpx;
}
```

## 🎨 表单组件

### 7. 输入框组件 (Input)

```css
.input-group {
  margin-bottom: 32rpx;
}

.input-label {
  font-size: 28rpx;
  color: #262626;
  margin-bottom: 12rpx;
  font-weight: 500;
}

.input-required::after {
  content: '*';
  color: #FF4D4F;
  margin-left: 4rpx;
}

.input {
  width: 100%;
  padding: 24rpx 32rpx;
  border: 2rpx solid #D9D9D9;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #262626;
  background: #FFFFFF;
  transition: all 0.3s ease;
}

.input:focus {
  border-color: #52C41A;
  box-shadow: 0 0 0 4rpx rgba(82, 196, 26, 0.1);
  outline: none;
}

.input-error {
  border-color: #FF4D4F;
}

.input-error:focus {
  border-color: #FF4D4F;
  box-shadow: 0 0 0 4rpx rgba(255, 77, 79, 0.1);
}

.input-disabled {
  background: #F5F5F5;
  color: #BFBFBF;
  cursor: not-allowed;
}

.input-help {
  font-size: 24rpx;
  color: #8C8C8C;
  margin-top: 8rpx;
}

.input-error-msg {
  font-size: 24rpx;
  color: #FF4D4F;
  margin-top: 8rpx;
}
```

### 8. 搜索框组件 (SearchBox)

```css
.search-box {
  position: relative;
  margin-bottom: 24rpx;
}

.search-input {
  width: 100%;
  padding: 24rpx 80rpx 24rpx 32rpx;
  border: 2rpx solid #E8E8E8;
  border-radius: 50rpx;
  font-size: 28rpx;
  background: #FAFAFA;
  transition: all 0.3s ease;
}

.search-input:focus {
  border-color: #52C41A;
  background: #FFFFFF;
  box-shadow: 0 0 0 4rpx rgba(82, 196, 26, 0.1);
}

.search-icon {
  position: absolute;
  right: 32rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 32rpx;
  height: 32rpx;
  color: #8C8C8C;
}

.search-suggestions {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: #FFFFFF;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  z-index: 100;
  max-height: 400rpx;
  overflow-y: auto;
}

.suggestion-item {
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #F0F0F0;
  font-size: 28rpx;
  color: #595959;
}

.suggestion-item:last-child {
  border-bottom: none;
}

.suggestion-item:active {
  background: #F6FFED;
}
```

这套组件库涵盖了小程序的主要UI需求，每个组件都有完整的样式定义和交互状态，可以直接在项目中使用。组件设计遵循了一致的设计语言，确保整体视觉效果的统一性。
