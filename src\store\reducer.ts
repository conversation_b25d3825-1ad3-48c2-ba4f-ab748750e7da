import { combineReducers } from 'redux-immutable'


import { reducer as recommendReducer } from '../pages/discover/recommend/store';
import { reducer as albumReducer } from "../pages/discover/album/store";
import { reducer as djRadioReducer } from "../pages/discover/djRadio/store";
import { reducer as songListReducer } from '../pages/discover/songList/store';
import { reducer as toplistReducer } from '../pages/discover/toplist/store';
import { reducer as artistReducer } from "../pages/discover/artist/store";
import { reducer as searchReducer } from '../pages/search/store'
import { reducer as themeHeaderReducer } from '../components/appHeader/store';
import { reducer as loginReducer } from '../components/themeLogin/store'
import { reducer as playerReducer } from '../components/playerBar/store';


const KKReducer = combineReducers({
    recommend: recommendReducer,
    album: albumReducer,
    player: playerReducer,
    djRadio: djRadioReducer,
    songList: songListReducer,
    artist: artistReducer,
    loginState: loginReducer,
    toplist: toplistReducer,
    themeHeader: themeHeaderReducer,
    search: searchReducer,
})

export default KKReducer;