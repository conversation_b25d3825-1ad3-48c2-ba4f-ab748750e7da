import React, { useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { Card, Spin, Result } from 'antd';
import { fetchProductDetail } from '../store/actions/productActions';

const ProductDetail = () => {
  const { id } = useParams();
  const dispatch = useDispatch();
  const { productDetail, productDetailLoading, productDetailError } = useSelector(state => state.product);

  useEffect(() => {
    if (id) {
      dispatch(fetchProductDetail(id));
    }
  }, [dispatch, id]);

  if (productDetailLoading) {
    return (
      <div style={{ textAlign: 'center', padding: 60 }}>
        <Spin size="large" tip="加载产品详情..." />
      </div>
    );
  }

  if (productDetailError) {
    return (
      <Result
        status="error"
        title="加载失败"
        subTitle={productDetailError}
      />
    );
  }

  return (
    <div className="product-detail-page">
      <Card title={`产品详情 - ${productDetail?.name || '加载中...'}`}>
        <p>产品详情页面开发中...</p>
        <p>产品ID: {id}</p>
        {productDetail && (
          <pre>{JSON.stringify(productDetail, null, 2)}</pre>
        )}
      </Card>
    </div>
  );
};

export default ProductDetail;
