// 页面切换功能
function showPage(pageId) {
    // 隐藏所有页面
    const pages = document.querySelectorAll('.page');
    pages.forEach(page => {
        page.classList.remove('active');
    });
    
    // 显示选中的页面
    const targetPage = document.getElementById(pageId);
    if (targetPage) {
        targetPage.classList.add('active');
    }
    
    // 更新导航按钮状态
    const navBtns = document.querySelectorAll('.nav-btn');
    navBtns.forEach(btn => {
        btn.classList.remove('active');
    });
    
    // 激活当前按钮
    event.target.classList.add('active');
}

// 模拟交互效果
document.addEventListener('DOMContentLoaded', function() {
    // 为所有卡片添加点击效果
    const cards = document.querySelectorAll('.action-card, .category-card, .station-card, .setting-card');
    cards.forEach(card => {
        card.addEventListener('click', function() {
            // 添加点击动画
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = '';
            }, 150);
        });
    });
    
    // 搜索框交互
    const searchInputs = document.querySelectorAll('.search-input');
    searchInputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.style.transform = 'scale(1.02)';
        });
        
        input.addEventListener('blur', function() {
            this.parentElement.style.transform = '';
        });
    });
    
    // 按钮悬停效果
    const buttons = document.querySelectorAll('.btn');
    buttons.forEach(button => {
        button.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
        });
        
        button.addEventListener('mouseleave', function() {
            this.style.transform = '';
        });
        
        button.addEventListener('click', function() {
            // 模拟按钮点击反馈
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = 'translateY(-2px)';
            }, 100);
            
            // 显示操作提示
            showToast('功能演示中...');
        });
    });
    
    // 地图标记动画
    const markers = document.querySelectorAll('.map-marker');
    markers.forEach((marker, index) => {
        marker.addEventListener('click', function() {
            showToast(`回收站点 ${index + 1} 详情`);
        });
    });
    
    // 设置卡片切换
    const settingCards = document.querySelectorAll('.setting-card');
    settingCards.forEach(card => {
        card.addEventListener('click', function() {
            // 移除其他卡片的激活状态
            settingCards.forEach(c => c.classList.remove('active'));
            // 激活当前卡片
            this.classList.add('active');
        });
    });
    
    // 分类项目点击
    const categoryItems = document.querySelectorAll('.category-item');
    categoryItems.forEach(item => {
        item.addEventListener('click', function() {
            const categoryName = this.querySelector('.item-name').textContent;
            showToast(`查看 ${categoryName} 详情`);
        });
    });
    
    // 站点卡片点击
    const stationCards = document.querySelectorAll('.station-card');
    stationCards.forEach(card => {
        card.addEventListener('click', function() {
            const stationName = this.querySelector('.station-name').textContent;
            showToast(`查看 ${stationName} 详情`);
        });
    });
    
    // 路线步骤点击
    const routeSteps = document.querySelectorAll('.route-step');
    routeSteps.forEach(step => {
        step.addEventListener('click', function() {
            const stepHeader = this.querySelector('.step-header').textContent;
            showToast(`查看 ${stepHeader} 详情`);
        });
    });
});

// 显示提示消息
function showToast(message) {
    // 创建提示元素
    const toast = document.createElement('div');
    toast.className = 'toast';
    toast.textContent = message;
    toast.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 12px 24px;
        border-radius: 25px;
        font-size: 14px;
        z-index: 10000;
        animation: fadeInOut 2s ease-in-out;
    `;
    
    // 添加动画样式
    const style = document.createElement('style');
    style.textContent = `
        @keyframes fadeInOut {
            0% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
            20% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
            80% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
            100% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
        }
    `;
    document.head.appendChild(style);
    
    // 添加到页面
    document.body.appendChild(toast);
    
    // 2秒后移除
    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
        if (style.parentNode) {
            style.parentNode.removeChild(style);
        }
    }, 2000);
}

// 模拟数据加载
function simulateDataLoading() {
    const loadingElements = document.querySelectorAll('.station-card, .category-card');
    loadingElements.forEach((element, index) => {
        element.style.opacity = '0';
        element.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            element.style.transition = 'all 0.5s ease';
            element.style.opacity = '1';
            element.style.transform = 'translateY(0)';
        }, index * 100);
    });
}

// 页面加载完成后执行动画
window.addEventListener('load', function() {
    simulateDataLoading();
    
    // 添加页面加载动画
    const phoneContainer = document.querySelector('.phone-mockup');
    if (phoneContainer) {
        phoneContainer.style.transform = 'scale(0.9) translateY(20px)';
        phoneContainer.style.opacity = '0';
        
        setTimeout(() => {
            phoneContainer.style.transition = 'all 0.8s ease';
            phoneContainer.style.transform = 'scale(1) translateY(0)';
            phoneContainer.style.opacity = '1';
        }, 300);
    }
});

// 添加滚动效果
function addScrollEffects() {
    const scrollableElements = document.querySelectorAll('.route-details');
    scrollableElements.forEach(element => {
        element.addEventListener('scroll', function() {
            const scrollTop = this.scrollTop;
            const scrollHeight = this.scrollHeight - this.clientHeight;
            const scrollPercent = scrollTop / scrollHeight;
            
            // 根据滚动位置添加阴影效果
            if (scrollPercent > 0) {
                this.style.boxShadow = 'inset 0 10px 10px -10px rgba(0,0,0,0.1)';
            } else {
                this.style.boxShadow = 'none';
            }
        });
    });
}

// 初始化滚动效果
document.addEventListener('DOMContentLoaded', addScrollEffects);

// 添加键盘导航支持
document.addEventListener('keydown', function(event) {
    const currentPage = document.querySelector('.page.active');
    if (!currentPage) return;
    
    const pageId = currentPage.id;
    const pages = ['home', 'category', 'ai-route', 'market', 'profile'];
    const currentIndex = pages.indexOf(pageId);
    
    if (event.key === 'ArrowLeft' && currentIndex > 0) {
        const prevBtn = document.querySelectorAll('.nav-btn')[currentIndex - 1];
        prevBtn.click();
    } else if (event.key === 'ArrowRight' && currentIndex < pages.length - 1) {
        const nextBtn = document.querySelectorAll('.nav-btn')[currentIndex + 1];
        nextBtn.click();
    }
});

// 添加触摸手势支持（移动端）
let touchStartX = 0;
let touchEndX = 0;

document.addEventListener('touchstart', function(event) {
    touchStartX = event.changedTouches[0].screenX;
});

document.addEventListener('touchend', function(event) {
    touchEndX = event.changedTouches[0].screenX;
    handleSwipe();
});

function handleSwipe() {
    const swipeThreshold = 50;
    const diff = touchStartX - touchEndX;
    
    if (Math.abs(diff) > swipeThreshold) {
        const currentPage = document.querySelector('.page.active');
        if (!currentPage) return;
        
        const pageId = currentPage.id;
        const pages = ['home', 'category', 'ai-route', 'market', 'profile'];
        const currentIndex = pages.indexOf(pageId);
        
        if (diff > 0 && currentIndex < pages.length - 1) {
            // 向左滑动，显示下一页
            const nextBtn = document.querySelectorAll('.nav-btn')[currentIndex + 1];
            nextBtn.click();
        } else if (diff < 0 && currentIndex > 0) {
            // 向右滑动，显示上一页
            const prevBtn = document.querySelectorAll('.nav-btn')[currentIndex - 1];
            prevBtn.click();
        }
    }
}
