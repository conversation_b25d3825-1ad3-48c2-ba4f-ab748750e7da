package com.manmanrecycle.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.manmanrecycle.common.result.Result;
import com.manmanrecycle.dto.AddressDTO;
import com.manmanrecycle.dto.FavoriteDTO;
import com.manmanrecycle.dto.UpdateUserDTO;
import com.manmanrecycle.entity.UserAddress;
import com.manmanrecycle.service.UserService;
import com.manmanrecycle.vo.FavoriteVO;
import com.manmanrecycle.vo.UserProfileVO;
import io.swagger.annotations.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

/**
 * 用户控制器
 * 
 * <AUTHOR> Team
 * @since 2024-01-15
 */
@Slf4j
@RestController
@RequestMapping("/user")
@RequiredArgsConstructor
@Api(tags = "👤 用户模块", description = "用户信息管理相关接口")
public class UserController {

    private final UserService userService;

    /**
     * 获取用户信息
     */
    @GetMapping("/profile")
    @ApiOperation(value = "获取用户信息", notes = "获取当前登录用户的详细信息")
    @ApiImplicitParam(name = "Authorization", value = "Bearer Token", required = true, dataType = "string", paramType = "header")
    @ApiResponses({
        @ApiResponse(code = 200, message = "获取成功"),
        @ApiResponse(code = 401, message = "未授权访问")
    })
    public Result<UserProfileVO> getUserProfile(HttpServletRequest request) {
        Long userId = (Long) request.getAttribute("userId");
        log.info("获取用户信息，userId: {}", userId);
        
        UserProfileVO userProfile = userService.getUserProfile(userId);
        
        return Result.success("获取用户信息成功", userProfile);
    }

    /**
     * 更新用户信息
     */
    @PutMapping("/profile")
    @ApiOperation(value = "更新用户信息", notes = "更新当前登录用户的基本信息")
    @ApiImplicitParam(name = "Authorization", value = "Bearer Token", required = true, dataType = "string", paramType = "header")
    @ApiResponses({
        @ApiResponse(code = 200, message = "更新成功"),
        @ApiResponse(code = 400, message = "请求参数错误"),
        @ApiResponse(code = 401, message = "未授权访问")
    })
    public Result<Void> updateUserProfile(
            @ApiParam(value = "用户信息更新参数", required = true)
            @Valid @RequestBody UpdateUserDTO updateDTO,
            HttpServletRequest request) {
        
        Long userId = (Long) request.getAttribute("userId");
        log.info("更新用户信息，userId: {}", userId);
        
        userService.updateUserProfile(userId, updateDTO);
        
        return Result.success("更新用户信息成功");
    }

    /**
     * 获取用户地址列表
     */
    @GetMapping("/addresses")
    @ApiOperation(value = "获取用户地址", notes = "获取当前用户的所有地址信息")
    @ApiImplicitParam(name = "Authorization", value = "Bearer Token", required = true, dataType = "string", paramType = "header")
    @ApiResponses({
        @ApiResponse(code = 200, message = "获取成功"),
        @ApiResponse(code = 401, message = "未授权访问")
    })
    public Result<java.util.List<UserAddress>> getUserAddresses(HttpServletRequest request) {
        Long userId = (Long) request.getAttribute("userId");
        log.info("获取用户地址列表，userId: {}", userId);
        
        java.util.List<UserAddress> addresses = userService.getUserAddresses(userId);
        
        return Result.success("获取地址列表成功", addresses);
    }

    /**
     * 添加用户地址
     */
    @PostMapping("/addresses")
    @ApiOperation(value = "添加用户地址", notes = "为当前用户添加新的地址信息")
    @ApiImplicitParam(name = "Authorization", value = "Bearer Token", required = true, dataType = "string", paramType = "header")
    @ApiResponses({
        @ApiResponse(code = 200, message = "添加成功"),
        @ApiResponse(code = 400, message = "请求参数错误"),
        @ApiResponse(code = 401, message = "未授权访问")
    })
    public Result<Void> addUserAddress(
            @ApiParam(value = "地址信息", required = true)
            @Valid @RequestBody AddressDTO addressDTO,
            HttpServletRequest request) {
        
        Long userId = (Long) request.getAttribute("userId");
        log.info("添加用户地址，userId: {}", userId);
        
        userService.addUserAddress(userId, addressDTO);
        
        return Result.success("添加地址成功");
    }

    /**
     * 更新用户地址
     */
    @PutMapping("/addresses/{id}")
    @ApiOperation(value = "更新用户地址", notes = "更新指定的用户地址信息")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "Authorization", value = "Bearer Token", required = true, dataType = "string", paramType = "header"),
        @ApiImplicitParam(name = "id", value = "地址ID", required = true, dataType = "long", paramType = "path")
    })
    @ApiResponses({
        @ApiResponse(code = 200, message = "更新成功"),
        @ApiResponse(code = 400, message = "请求参数错误"),
        @ApiResponse(code = 401, message = "未授权访问"),
        @ApiResponse(code = 404, message = "地址不存在")
    })
    public Result<Void> updateUserAddress(
            @PathVariable Long id,
            @ApiParam(value = "地址信息", required = true)
            @Valid @RequestBody AddressDTO addressDTO,
            HttpServletRequest request) {
        
        Long userId = (Long) request.getAttribute("userId");
        log.info("更新用户地址，userId: {}, addressId: {}", userId, id);
        
        userService.updateUserAddress(userId, id, addressDTO);
        
        return Result.success("更新地址成功");
    }

    /**
     * 删除用户地址
     */
    @DeleteMapping("/addresses/{id}")
    @ApiOperation(value = "删除用户地址", notes = "删除指定的用户地址")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "Authorization", value = "Bearer Token", required = true, dataType = "string", paramType = "header"),
        @ApiImplicitParam(name = "id", value = "地址ID", required = true, dataType = "long", paramType = "path")
    })
    @ApiResponses({
        @ApiResponse(code = 200, message = "删除成功"),
        @ApiResponse(code = 401, message = "未授权访问"),
        @ApiResponse(code = 404, message = "地址不存在")
    })
    public Result<Void> deleteUserAddress(
            @PathVariable Long id,
            HttpServletRequest request) {
        
        Long userId = (Long) request.getAttribute("userId");
        log.info("删除用户地址，userId: {}, addressId: {}", userId, id);
        
        userService.deleteUserAddress(userId, id);
        
        return Result.success("删除地址成功");
    }

    /**
     * 获取用户收藏
     */
    @GetMapping("/favorites")
    @ApiOperation(value = "获取用户收藏", notes = "获取当前用户的收藏列表")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "Authorization", value = "Bearer Token", required = true, dataType = "string", paramType = "header"),
        @ApiImplicitParam(name = "type", value = "收藏类型 1-站点 2-产品", dataType = "int", paramType = "query"),
        @ApiImplicitParam(name = "page", value = "页码", dataType = "int", paramType = "query", defaultValue = "1"),
        @ApiImplicitParam(name = "size", value = "每页数量", dataType = "int", paramType = "query", defaultValue = "20")
    })
    @ApiResponses({
        @ApiResponse(code = 200, message = "获取成功"),
        @ApiResponse(code = 401, message = "未授权访问")
    })
    public Result<IPage<FavoriteVO>> getUserFavorites(
            @RequestParam(required = false) Integer type,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            HttpServletRequest request) {
        
        Long userId = (Long) request.getAttribute("userId");
        log.info("获取用户收藏，userId: {}, type: {}", userId, type);
        
        IPage<FavoriteVO> favorites = userService.getUserFavorites(userId, type, page, size);
        
        return Result.success("获取收藏列表成功", favorites);
    }

    /**
     * 添加收藏
     */
    @PostMapping("/favorites")
    @ApiOperation(value = "添加收藏", notes = "收藏站点或产品")
    @ApiImplicitParam(name = "Authorization", value = "Bearer Token", required = true, dataType = "string", paramType = "header")
    @ApiResponses({
        @ApiResponse(code = 200, message = "收藏成功"),
        @ApiResponse(code = 400, message = "请求参数错误"),
        @ApiResponse(code = 401, message = "未授权访问"),
        @ApiResponse(code = 409, message = "已收藏该项目")
    })
    public Result<Void> addFavorite(
            @ApiParam(value = "收藏参数", required = true)
            @Valid @RequestBody FavoriteDTO favoriteDTO,
            HttpServletRequest request) {
        
        Long userId = (Long) request.getAttribute("userId");
        log.info("添加收藏，userId: {}, type: {}, targetId: {}", 
                userId, favoriteDTO.getType(), favoriteDTO.getTargetId());
        
        userService.addFavorite(userId, favoriteDTO);
        
        return Result.success("收藏成功");
    }

    /**
     * 取消收藏
     */
    @DeleteMapping("/favorites/{id}")
    @ApiOperation(value = "取消收藏", notes = "取消收藏的站点或产品")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "Authorization", value = "Bearer Token", required = true, dataType = "string", paramType = "header"),
        @ApiImplicitParam(name = "id", value = "收藏ID", required = true, dataType = "long", paramType = "path")
    })
    @ApiResponses({
        @ApiResponse(code = 200, message = "取消收藏成功"),
        @ApiResponse(code = 401, message = "未授权访问"),
        @ApiResponse(code = 404, message = "收藏不存在")
    })
    public Result<Void> removeFavorite(
            @PathVariable Long id,
            HttpServletRequest request) {
        
        Long userId = (Long) request.getAttribute("userId");
        log.info("取消收藏，userId: {}, favoriteId: {}", userId, id);
        
        userService.removeFavorite(userId, id);
        
        return Result.success("取消收藏成功");
    }
}
