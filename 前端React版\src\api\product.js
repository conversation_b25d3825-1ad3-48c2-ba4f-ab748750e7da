import request from '../utils/request';

/**
 * 产品相关API
 */

// 获取产品分类
export const getCategories = () => {
  return request.get('/products/categories');
};

// 搜索产品
export const searchProducts = (params) => {
  return request.get('/products/search', { params });
};

// 获取产品详情
export const getProductDetail = (id) => {
  return request.get(`/products/${id}`);
};

// 获取热门产品
export const getHotProducts = (params) => {
  return request.get('/products/hot', { params });
};

// 获取产品价格信息
export const getProductPrices = (id, params) => {
  return request.get(`/products/${id}/prices`, { params });
};
