@startuml 慢慢回收数据库简化ER关系图

!theme plain
skinparam linetype ortho
skinparam backgroundColor #FAFAFA
skinparam roundcorner 10

' 定义实体样式
skinparam entity {
  BackgroundColor #E3F2FD
  BorderColor #1976D2
  FontSize 11
  FontName Arial
}

' 定义包样式
skinparam package {
  BackgroundColor #F5F5F5
  BorderColor #757575
  FontSize 12
  FontStyle bold
}

' 用户领域
package "👥 用户领域" as UserDomain #E8F5E8 {
  entity "users\n用户表" as users {
    * id : int <<PK>>
    --
    * openid : varchar(32) <<UK>>
    nickname : varchar(20)
    phone : varchar(11)
    city : varchar(20)
    status : tinyint(1)
  }

  entity "user_addresses\n用户地址表" as user_addresses {
    * id : int <<PK>>
    --
    * user_id : int <<FK>>
    name : varchar(20)
    address : varchar(100)
    lng : decimal(9,6)
    lat : decimal(9,6)
    is_default : tinyint(1)
  }
}

' 产品领域
package "💰 产品领域" as ProductDomain #FFF3E0 {
  entity "categories\n产品分类表" as categories {
    * id : int <<PK>>
    --
    parent_id : int
    name : varchar(20)
    icon : varchar(10)
    sort : int
    status : tinyint(1)
  }

  entity "products\n产品表" as products {
    * id : int <<PK>>
    --
    * category_id : int <<FK>>
    name : varchar(50)
    brand : varchar(20)
    model : varchar(30)
    keywords : varchar(100)
    status : tinyint(1)
  }

  entity "prices\n产品价格表" as prices {
    * id : int <<PK>>
    --
    * product_id : int <<FK>>
    station_id : int <<FK>>
    min_price : decimal(8,2)
    max_price : decimal(8,2)
    unit : varchar(10)
    date : date
  }
}

' 站点领域
package "🏪 站点领域" as StationDomain #F3E5F5 {
  entity "stations\n回收站点表" as stations {
    * id : int <<PK>>
    --
    name : varchar(30)
    phone : varchar(11)
    address : varchar(100)
    lng : decimal(9,6)
    lat : decimal(9,6)
    city : varchar(20)
    rating : decimal(3,2)
    status : tinyint(1)
  }

  entity "station_services\n站点服务表" as station_services {
    * id : int <<PK>>
    --
    * station_id : int <<FK>>
    * category_id : int <<FK>>
    min_price : decimal(8,2)
    max_price : decimal(8,2)
    unit : varchar(10)
    status : tinyint(1)
  }

  entity "reviews\n站点评价表" as reviews {
    * id : int <<PK>>
    --
    * user_id : int <<FK>>
    * station_id : int <<FK>>
    rating : tinyint(1)
    content : varchar(200)
    images : json
    status : tinyint(1)
  }
}

' 行为领域
package "📊 行为领域" as BehaviorDomain #E0F2F1 {
  entity "search_logs\n搜索记录表" as search_logs {
    * id : int <<PK>>
    --
    * user_id : int <<FK>>
    keyword : varchar(50)
    type : tinyint(1)
    result_count : int
    created_at : timestamp
  }

  entity "favorites\n收藏表" as favorites {
    * id : int <<PK>>
    --
    * user_id : int <<FK>>
    type : tinyint(1)
    target_id : int
    created_at : timestamp
  }

  entity "browse_logs\n浏览记录表" as browse_logs {
    * id : int <<PK>>
    --
    * user_id : int <<FK>>
    type : tinyint(1)
    target_id : int
    duration : int
    created_at : timestamp
  }
}

' 系统领域
package "⚙️ 系统领域" as SystemDomain #FFF8E1 {
  entity "configs\n系统配置表" as configs {
    * id : int <<PK>>
    --
    key : varchar(50) <<UK>>
    value : text
    desc : varchar(100)
    type : varchar(20)
    status : tinyint(1)
  }
}

' 定义关系
' 用户领域内部关系
users ||--o{ user_addresses : "拥有地址"

' 产品领域内部关系
categories ||--o{ categories : "父子分类"
categories ||--o{ products : "分类产品"
products ||--o{ prices : "产品价格"

' 站点领域内部关系
stations ||--o{ station_services : "提供服务"
stations ||--o{ reviews : "站点评价"

' 跨领域关系
users ||--o{ search_logs : "搜索行为"
users ||--o{ favorites : "收藏行为"
users ||--o{ browse_logs : "浏览行为"
users ||--o{ reviews : "评价行为"

categories ||--o{ station_services : "服务分类"
stations ||--o{ prices : "站点价格"

' 添加关系说明
note top of users : 核心实体\n所有用户行为的起点
note top of categories : 树形结构\n支持多级分类
note top of prices : 价格中心\n连接产品和站点
note bottom of favorites : 统一收藏\n支持多种类型
note bottom of configs : 系统配置\n动态参数管理

' 添加领域说明
note top of UserDomain : 管理用户信息\n和用户相关数据
note top of ProductDomain : 管理产品分类\n产品信息和价格
note top of StationDomain : 管理回收站点\n服务和评价
note top of BehaviorDomain : 记录用户行为\n支持数据分析
note top of SystemDomain : 系统配置管理\n全局参数设置

title 慢慢回收小程序数据库ER关系图 (简化版)
center footer 🌿 领域驱动设计 | 字段简化优化 | 关系清晰明确

@enduml
