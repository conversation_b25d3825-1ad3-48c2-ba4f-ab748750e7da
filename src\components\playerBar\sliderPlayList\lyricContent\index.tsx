import React, { memo, useEffect, useRef } from 'react'
import { shallowEqual, useSelector } from 'react-redux'
import { scrollTo } from '../../../../utils/playerUtils'
import './style.css'

export default memo(function LyricContent() {
  const { lyricList, currentLyricIndex } = useSelector(
    (state: any) => ({
      lyricList: state.getIn(['player', 'lyricList']),
      currentLyricIndex: state.getIn(['player', 'currentLyricIndex']),
    }),
    shallowEqual
  )

  // other hooks
  const panelRef: any = useRef()
  useEffect(() => {
    if (currentLyricIndex > 0 && currentLyricIndex < 3) return
    scrollTo(panelRef.current, (currentLyricIndex - 3) * 32, 300)
  }, [currentLyricIndex])

  return (
      <div className="lyricContentBox" ref={panelRef}>
        <div className="lyricContent">
          {lyricList && lyricList.map((item: any, index: any) => {
          return (
            <div key={index} className={'lyricItem ' + (currentLyricIndex === index ? 'active' : '')}>
              {item.content}
            </div>
          )
        })}
        </div>
      </div>

   
  )
})
