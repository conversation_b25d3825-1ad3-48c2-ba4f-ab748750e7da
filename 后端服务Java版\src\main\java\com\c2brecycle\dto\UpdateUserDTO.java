package com.c2brecycle.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Size;

/**
 * 更新用户信息DTO
 * 
 * <AUTHOR> Recycle Team
 * @since 2024-01-15
 */
@Data
@ApiModel("更新用户信息参数")
public class UpdateUserDTO {

    @ApiModelProperty(value = "昵称", example = "张三")
    @Size(min = 1, max = 50, message = "昵称长度为1-50个字符")
    private String nickname;

    @ApiModelProperty(value = "头像URL", example = "https://example.com/avatar.jpg")
    private String avatar;

    @ApiModelProperty(value = "手机号", example = "13800138000")
    @Size(min = 11, max = 11, message = "手机号必须为11位")
    private String phone;

    @ApiModelProperty(value = "城市", example = "北京市")
    @Size(max = 20, message = "城市名称最多20个字符")
    private String city;
}
