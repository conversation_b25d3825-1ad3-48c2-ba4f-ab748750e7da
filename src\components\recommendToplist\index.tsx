import React, { memo } from 'react'
import { shallowEqual, useDispatch, useSelector } from 'react-redux'
import { getSizeImage } from '../../utils/formatUtils'
import { message } from 'antd'
import './style.css'
import {
  getSongDetailAction,
  changeFirstLoad,
} from '../playerBar/store/actionCreators'
import { useAddPlaylist } from '../../hooks/changeMusic'
import { changeCurrentIndexAction } from '../../pages/discover/toplist/store/actionCreators'

export default memo(function RecommendToplist(props: any) {
  const { info, index } = props
  const { tracks = [] } = info
  // let localPlayList = [] // 本地存储(暂时不做)

  // redux hook
  const dispatch = useDispatch()
  const { playList } = useSelector(
    (state: any) => ({
      playList: state.getIn(['player', 'playList']),
    }),
    shallowEqual
  )

  // other handle
  // 播放音乐
  const playMusic = (e: any, item: any) => {
    // 阻止超链接跳转
    e.preventDefault()
    // 派发action 歌曲详情
    dispatch(getSongDetailAction(item.id))
    // 不是首次加载,播放音乐
    dispatch(changeFirstLoad(false))
  }

  const addPlaylist = useAddPlaylist(playList, message)



  return (
    <div className="recommendToplistBox">
      <div className="recommendToplistHeader">
        <div className="recommendToplistImage">
          <img src={getSizeImage(info.coverImgUrl, 80)} alt="" />
          <div className="recommendToplistImageCover"></div>
        </div>
        <div className="recommendToplistTitle">
          <h3>{info.name}</h3>
          <div className="recommendToplistButton">
            <span className="recommendToplistButtonPlay"></span>
            <span className="recommendToplistButtonFavourite"></span>
          </div>
        </div>
      </div>
      <div className="recommendToplist">
        {tracks &&
          tracks.length > 0 &&
          tracks.slice(0, 10).map((item: any, index: any) => {
            return (
              <div key={item.id} className="recommendToplistItem">
                <div className="recommendToplistItemNumber">{index + 1}</div>
                <a href={`#/song?id=${item.id}`} className="recommendToplistItemSongName textNowrap" onClick={()=>dispatch(getSongDetailAction(item.id))}>
                  {item.name}
                </a>
                <div className="recommendToplistItemOper">
                  <a
                    href="/#"
                    className="recommendToplistItemOperButton recommendToplistItemOperPlay"
                    onClick={e => playMusic(e, item)}
                  >
                  </a>
                  <a
                    href="/#"
                    className="recommendToplistItemOperButton recommendToplistItemOperAddto"
                    onClick={e => addPlaylist(e, item.id)}
                  >
                  </a>
                  <span className="recommendToplistItemOperButton recommendToplistItemOperFavourite"></span>
                </div>
              </div>
            )
          })}
      </div>
      <div className="recommendToplistFooter">
        <a href={`#/discover/toplist?id=${info.id}`} className="recommendToplistShowAll" onClick={() => dispatch(changeCurrentIndexAction(index))}>
          查看全部&gt;
        </a>
      </div>
    </div>
  )
})
