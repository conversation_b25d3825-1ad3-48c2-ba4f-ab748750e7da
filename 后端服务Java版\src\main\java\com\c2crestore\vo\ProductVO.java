package com.c2crestore.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 产品信息VO
 * 
 * <AUTHOR> Restore Team
 * @since 2024-01-15
 */
@Data
@ApiModel("产品信息")
public class ProductVO {

    @ApiModelProperty("产品ID")
    private Long id;

    @ApiModelProperty("分类ID")
    private Long categoryId;

    @ApiModelProperty("分类名称")
    private String categoryName;

    @ApiModelProperty("分类图标")
    private String categoryIcon;

    @ApiModelProperty("产品名称")
    private String name;

    @ApiModelProperty("品牌")
    private String brand;

    @ApiModelProperty("型号")
    private String model;

    @ApiModelProperty("图片")
    private String image;

    @ApiModelProperty("关键词")
    private String keywords;

    @ApiModelProperty("状态")
    private Integer status;

    @ApiModelProperty("当前最低价")
    private BigDecimal currentMinPrice;

    @ApiModelProperty("当前最高价")
    private BigDecimal currentMaxPrice;

    @ApiModelProperty("价格单位")
    private String priceUnit;

    @ApiModelProperty("收藏数量")
    private Integer favoriteCount;

    @ApiModelProperty("创建时间")
    private LocalDateTime createdAt;
}
