package com.c2crestore.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.*;
import java.math.BigDecimal;

/**
 * 附近站点查询DTO
 * 
 * <AUTHOR> Restore Team
 * @since 2024-01-15
 */
@Data
@ApiModel("附近站点查询参数")
public class NearbyStationDTO {

    @ApiModelProperty(value = "经度", required = true, example = "116.407400")
    @NotNull(message = "经度不能为空")
    @DecimalMin(value = "-180.0", message = "经度范围为-180到180")
    @DecimalMax(value = "180.0", message = "经度范围为-180到180")
    private BigDecimal lng;

    @ApiModelProperty(value = "纬度", required = true, example = "39.904200")
    @NotNull(message = "纬度不能为空")
    @DecimalMin(value = "-90.0", message = "纬度范围为-90到90")
    @DecimalMax(value = "90.0", message = "纬度范围为-90到90")
    private BigDecimal lat;

    @ApiModelProperty(value = "搜索半径(km)", example = "5.0")
    @DecimalMin(value = "0.1", message = "搜索半径至少为0.1km")
    @DecimalMax(value = "50.0", message = "搜索半径最大为50km")
    private BigDecimal radius = new BigDecimal("5.0");

    @ApiModelProperty(value = "站点状态", example = "1", notes = "0-停业 1-营业")
    @Min(value = 0, message = "状态值只能是0或1")
    @Max(value = 1, message = "状态值只能是0或1")
    private Integer status;

    @ApiModelProperty(value = "页码", example = "1")
    @Min(value = 1, message = "页码必须大于0")
    private Integer page = 1;

    @ApiModelProperty(value = "每页数量", example = "20")
    @Min(value = 1, message = "每页数量至少为1")
    @Max(value = 100, message = "每页数量最多为100")
    private Integer size = 20;
}
