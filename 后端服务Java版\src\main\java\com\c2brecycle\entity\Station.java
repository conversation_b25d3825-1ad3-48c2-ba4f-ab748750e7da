package com.c2brecycle.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 回收站点实体类
 * 
 * <AUTHOR> Recycle Team
 * @since 2024-01-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("stations")
public class Station implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 站点ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 站点名称
     */
    @TableField("name")
    private String name;

    /**
     * 电话
     */
    @TableField("phone")
    private String phone;

    /**
     * 地址
     */
    @TableField("address")
    private String address;

    /**
     * 经度
     */
    @TableField("lng")
    private BigDecimal lng;

    /**
     * 纬度
     */
    @TableField("lat")
    private BigDecimal lat;

    /**
     * 城市
     */
    @TableField("city")
    private String city;

    /**
     * 营业时间
     */
    @TableField("hours")
    private String hours;

    /**
     * 图片
     */
    @TableField("image")
    private String image;

    /**
     * 评分
     */
    @TableField("rating")
    private BigDecimal rating;

    /**
     * 评价数
     */
    @TableField("review_count")
    private Integer reviewCount;

    /**
     * 状态 0-停业 1-营业
     */
    @TableField("status")
    private Integer status;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 逻辑删除标记
     */
    @TableLogic
    @TableField("deleted")
    private Integer deleted;
}
