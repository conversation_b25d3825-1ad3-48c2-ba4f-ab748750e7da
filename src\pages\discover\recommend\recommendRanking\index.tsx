import React, { memo, useEffect } from 'react'

import ThemeHeaderRcm from '../../../../components/themeHeaderRCM'
import RecommendToplist from '../../../../components/recommendToplist'
import { shallowEqual, useDispatch, useSelector } from 'react-redux'
import { getToplistAction } from '../store/actionCreators'
import './style.css'
import { useNavigate } from 'react-router-dom'

export default memo(function RecommendRanking() {
  const { upRanking = [], originRanking = [], newRanking = [] } = useSelector((state: any) => ({
    upRanking: state.getIn(['recommend', 'upRanking']),
    originRanking: state.getIn(['recommend', 'originRanking']),
    newRanking: state.getIn(['recommend', 'newRanking'])
  }), shallowEqual)
  const dispatch = useDispatch()

  // other hook
  useEffect(() => {
    dispatch(getToplistAction(19723756))
    dispatch(getToplistAction(3779629))
    dispatch(getToplistAction(2884035))
  }, [dispatch])
  const navigate = useNavigate();
  const handleTitleClick = () => {
    navigate('/discover/toplist')
  }
  return (
    <div className="recommendRankingBox">
      <ThemeHeaderRcm title="榜单"  titleClick={() => handleTitleClick()} />
      <div className="recommendRankingInfo">
        <RecommendToplist info={upRanking} index={0} />
        <RecommendToplist info={newRanking} index={1} />
        <RecommendToplist info={originRanking} index={2} />
      </div>
    </div>
  )
})
