import React, { memo } from 'react'
import { shallowEqual, useDispatch, useSelector } from 'react-redux'
import propTypes from 'prop-types'
import { NavLink } from 'react-router-dom'
import { useAddPlaylist } from '../../../hooks/changeMusic'
import { getSizeImage } from '../../../utils/formatUtils'
import { getSongDetailAction } from '../../../components/playerBar/store'
import { PlayCircleOutlined } from '@ant-design/icons'
import { message } from 'antd'
import './style.css'

function SimiSongItem(props: any) {
  // props/state
  const {
    coverPic,
    singer,
    songId,
    songName,
    className = '',
  } = props

  // redux hook
  const dispatch = useDispatch()
  const { playlist } = useSelector(
    (state: any) => ({
      playlist: state.getIn(['player', 'playList']),
    }),
    shallowEqual
  )
  // other function
  const playMusic = (e: any, isTo = false) => {
    if (!isTo) e.preventDefault()
    dispatch(getSongDetailAction(songId));
    // 播放音乐
    (document.getElementById('audio') as any).autoplay = true
  }

  const addPlaylist = useAddPlaylist(playlist, message)

  return (
    <div className={className}>
      {coverPic && (
        <NavLink to={`/song?id=${songId}`} className="simiSongItem" onClick={(e) => playMusic(e, true)}>
          <img src={getSizeImage(coverPic, 50)} alt="" />
        </NavLink>
      )}
      <div className="simiSongItem simiSongInfo">
        <div className="simiSongInfoLeft">
          <PlayCircleOutlined className="active" onClick={(e) => playMusic(e)} />
          <div className="simiSongSingerSong">
            <a href={`#/song?id=${songId}`} onClick={(e) => playMusic(e, true)} className="text-nowrap">
              {songName}
            </a>
            <div className="simiSongItem singer">
              {singer}
            </div>
          </div>
        </div>
        <div className="right-operator">
          <button className="simiSongAddto" onClick={(e) => addPlaylist(e, songId)}></button>
        </div>
      </div></div>
  )
}

SimiSongItem.propTypes = {
  coverPic: propTypes.string,
  duration: propTypes.string.isRequired,
  singer: propTypes.string.isRequired,
  songId: propTypes.number.isRequired,
  className: propTypes.string,
  songName: propTypes.string.isRequired,
}

export default memo(SimiSongItem)
