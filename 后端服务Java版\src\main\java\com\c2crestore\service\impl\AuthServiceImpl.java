package com.c2crestore.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.c2crestore.common.exception.BusinessException;
import com.c2crestore.common.result.ResultCode;
import com.c2crestore.dto.WechatLoginDTO;
import com.c2crestore.entity.User;
import com.c2crestore.mapper.UserMapper;
import com.c2crestore.service.AuthService;
import com.c2crestore.util.JwtUtil;
import com.c2crestore.vo.LoginVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.concurrent.TimeUnit;

/**
 * 认证服务实现类
 * 
 * <AUTHOR> Restore Team
 * @since 2024-01-15
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AuthServiceImpl implements AuthService {

    private final UserMapper userMapper;
    private final JwtUtil jwtUtil;
    private final RedisTemplate<String, Object> redisTemplate;

    @Value("${app.wechat.app-id}")
    private String wechatAppId;

    @Value("${app.wechat.app-secret}")
    private String wechatAppSecret;

    @Value("${app.wechat.login-url}")
    private String wechatLoginUrl;

    private static final String REDIS_TOKEN_PREFIX = "token:";
    private static final String REDIS_REFRESH_TOKEN_PREFIX = "refresh_token:";

    @Override
    @Transactional(rollbackFor = Exception.class)
    public LoginVO wechatLogin(WechatLoginDTO loginDTO) {
        log.info("微信登录开始，code: {}", loginDTO.getCode());

        // 1. 调用微信API获取openid
        String openid = getOpenidFromWechat(loginDTO.getCode());
        if (StrUtil.isBlank(openid)) {
            throw new BusinessException(ResultCode.WECHAT_CODE_INVALID);
        }

        // 2. 查询用户是否存在
        User user = userMapper.selectByOpenid(openid);
        boolean isNewUser = false;

        if (user == null) {
            // 3. 新用户注册
            user = createNewUser(openid, loginDTO);
            isNewUser = true;
            log.info("新用户注册成功，userId: {}, openid: {}", user.getId(), openid);
        } else {
            // 4. 老用户更新信息
            updateUserInfo(user, loginDTO);
            log.info("用户登录成功，userId: {}, openid: {}", user.getId(), openid);
        }

        // 5. 生成Token
        String token = jwtUtil.generateToken(user.getId());
        String refreshToken = jwtUtil.generateRefreshToken(user.getId());

        // 6. 缓存Token
        cacheToken(user.getId(), token, refreshToken);

        // 7. 构建返回结果
        return LoginVO.builder()
                .token(token)
                .refreshToken(refreshToken)
                .user(user)
                .isNewUser(isNewUser)
                .expiresIn(jwtUtil.getTokenRemainingTime(token))
                .build();
    }

    @Override
    public LoginVO refreshToken(String refreshToken) {
        log.info("刷新Token开始");

        // 1. 验证刷新Token
        Long userId = jwtUtil.verifyRefreshToken(refreshToken);
        if (userId == null) {
            throw new BusinessException(ResultCode.TOKEN_INVALID);
        }

        // 2. 检查用户状态
        User user = userMapper.selectById(userId);
        if (user == null || user.getStatus() != 1) {
            throw new BusinessException(ResultCode.USER_NOT_FOUND);
        }

        // 3. 生成新Token
        String newToken = jwtUtil.generateToken(userId);
        String newRefreshToken = jwtUtil.generateRefreshToken(userId);

        // 4. 更新缓存
        cacheToken(userId, newToken, newRefreshToken);

        // 5. 删除旧的刷新Token
        redisTemplate.delete(REDIS_REFRESH_TOKEN_PREFIX + refreshToken);

        log.info("Token刷新成功，userId: {}", userId);

        return LoginVO.builder()
                .token(newToken)
                .refreshToken(newRefreshToken)
                .user(user)
                .isNewUser(false)
                .expiresIn(jwtUtil.getTokenRemainingTime(newToken))
                .build();
    }

    @Override
    public void logout(Long userId) {
        log.info("用户登出，userId: {}", userId);

        // 删除Redis中的Token缓存
        String tokenKey = REDIS_TOKEN_PREFIX + userId;
        String refreshTokenKey = REDIS_REFRESH_TOKEN_PREFIX + userId;
        
        redisTemplate.delete(tokenKey);
        redisTemplate.delete(refreshTokenKey);
    }

    @Override
    public Long verifyToken(String token) {
        try {
            // 1. 验证JWT Token
            Long userId = jwtUtil.verifyToken(token);
            if (userId == null) {
                return null;
            }

            // 2. 检查Redis缓存
            String tokenKey = REDIS_TOKEN_PREFIX + userId;
            String cachedToken = (String) redisTemplate.opsForValue().get(tokenKey);
            
            if (!token.equals(cachedToken)) {
                log.warn("Token不匹配，userId: {}", userId);
                return null;
            }

            // 3. 更新用户活动时间
            userMapper.updateLastActiveTime(userId);

            return userId;
        } catch (Exception e) {
            log.error("Token验证失败", e);
            return null;
        }
    }

    /**
     * 从微信获取openid
     */
    private String getOpenidFromWechat(String code) {
        try {
            String url = String.format("%s?appid=%s&secret=%s&js_code=%s&grant_type=authorization_code",
                    wechatLoginUrl, wechatAppId, wechatAppSecret, code);

            String response = HttpUtil.get(url, 10000);
            JSONObject jsonObject = JSONUtil.parseObj(response);

            if (jsonObject.containsKey("errcode")) {
                Integer errcode = jsonObject.getInt("errcode");
                String errmsg = jsonObject.getStr("errmsg");
                log.error("微信登录失败，errcode: {}, errmsg: {}", errcode, errmsg);
                throw new BusinessException(ResultCode.WECHAT_API_ERROR, errmsg);
            }

            return jsonObject.getStr("openid");
        } catch (Exception e) {
            log.error("调用微信API失败", e);
            throw new BusinessException(ResultCode.WECHAT_API_ERROR);
        }
    }

    /**
     * 创建新用户
     */
    private User createNewUser(String openid, WechatLoginDTO loginDTO) {
        User user = new User();
        user.setOpenid(openid);
        user.setNickname(StrUtil.isNotBlank(loginDTO.getNickname()) ? 
                loginDTO.getNickname() : "用户" + System.currentTimeMillis());
        user.setAvatar(loginDTO.getAvatar());
        user.setCity(loginDTO.getCity());
        user.setStatus(1);
        user.setCreatedAt(LocalDateTime.now());
        user.setUpdatedAt(LocalDateTime.now());

        userMapper.insert(user);
        return user;
    }

    /**
     * 更新用户信息
     */
    private void updateUserInfo(User user, WechatLoginDTO loginDTO) {
        boolean needUpdate = false;

        if (StrUtil.isNotBlank(loginDTO.getNickname()) && 
            !loginDTO.getNickname().equals(user.getNickname())) {
            user.setNickname(loginDTO.getNickname());
            needUpdate = true;
        }

        if (StrUtil.isNotBlank(loginDTO.getAvatar()) && 
            !loginDTO.getAvatar().equals(user.getAvatar())) {
            user.setAvatar(loginDTO.getAvatar());
            needUpdate = true;
        }

        if (StrUtil.isNotBlank(loginDTO.getCity()) && 
            !loginDTO.getCity().equals(user.getCity())) {
            user.setCity(loginDTO.getCity());
            needUpdate = true;
        }

        if (needUpdate) {
            user.setUpdatedAt(LocalDateTime.now());
            userMapper.updateById(user);
        }
    }

    /**
     * 缓存Token
     */
    private void cacheToken(Long userId, String token, String refreshToken) {
        // 缓存访问Token（7天）
        String tokenKey = REDIS_TOKEN_PREFIX + userId;
        redisTemplate.opsForValue().set(tokenKey, token, 7, TimeUnit.DAYS);

        // 缓存刷新Token（30天）
        String refreshTokenKey = REDIS_REFRESH_TOKEN_PREFIX + userId;
        redisTemplate.opsForValue().set(refreshTokenKey, refreshToken, 30, TimeUnit.DAYS);
    }
}
