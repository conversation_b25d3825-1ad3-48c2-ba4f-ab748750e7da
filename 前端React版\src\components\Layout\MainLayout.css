.main-layout {
  min-height: 100vh;
}

.main-sider {
  position: fixed;
  height: 100vh;
  left: 0;
  top: 0;
  bottom: 0;
  z-index: 100;
}

.main-sider .ant-layout-sider-children {
  display: flex;
  flex-direction: column;
}

.logo {
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  margin: 16px;
  border-radius: 8px;
}

.logo h2 {
  color: #fff;
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.site-layout {
  margin-left: 200px;
  transition: margin-left 0.2s;
}

.site-layout.collapsed {
  margin-left: 80px;
}

.site-layout-header {
  background: #fff;
  padding: 0 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 99;
}

.header-left {
  display: flex;
  align-items: center;
}

.trigger {
  font-size: 18px;
  line-height: 64px;
  cursor: pointer;
  transition: color 0.3s;
}

.trigger:hover {
  color: #1890ff;
}

.header-right {
  display: flex;
  align-items: center;
}

.welcome-text {
  color: #666;
  margin-right: 16px;
}

.user-avatar {
  cursor: pointer;
  transition: all 0.3s;
}

.user-avatar:hover {
  transform: scale(1.1);
}

.site-layout-content {
  padding: 24px;
  min-height: calc(100vh - 64px);
  background: #f5f5f5;
}

/* 响应式设计 */
@media (max-width: 992px) {
  .main-sider {
    position: fixed;
    z-index: 1000;
  }
  
  .site-layout {
    margin-left: 0;
  }
  
  .site-layout.collapsed {
    margin-left: 0;
  }
  
  .site-layout-header {
    padding: 0 16px;
  }
  
  .site-layout-content {
    padding: 16px;
  }
  
  .welcome-text {
    display: none;
  }
}

@media (max-width: 576px) {
  .logo h2 {
    font-size: 16px;
  }
  
  .site-layout-header {
    padding: 0 12px;
  }
  
  .site-layout-content {
    padding: 12px;
  }
}
