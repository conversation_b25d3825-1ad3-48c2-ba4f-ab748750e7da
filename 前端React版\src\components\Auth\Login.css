.login-container {
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.login-card {
  width: 100%;
  max-width: 400px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  overflow: hidden;
}

.login-card .ant-card-head {
  background: #fff;
  border-bottom: 1px solid #f0f0f0;
  text-align: center;
}

.login-card .ant-card-head-title {
  font-size: 24px;
  font-weight: 600;
  color: #1890ff;
}

.login-tabs {
  display: flex;
  justify-content: center;
  margin-bottom: 0;
}

.wechat-login {
  text-align: center;
  padding: 20px 0;
}

.wechat-login-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.wechat-icon {
  font-size: 64px;
  color: #07c160;
  margin-bottom: 8px;
}

.wechat-login p {
  color: #666;
  margin: 0;
  font-size: 14px;
}

.login-footer {
  text-align: center;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.login-footer p {
  color: #999;
  font-size: 12px;
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .login-container {
    padding: 16px;
  }
  
  .login-card {
    max-width: 100%;
  }
  
  .login-card .ant-card-head-title {
    font-size: 20px;
  }
  
  .wechat-icon {
    font-size: 48px;
  }
}
