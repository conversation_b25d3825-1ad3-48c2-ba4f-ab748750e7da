import { message } from 'antd';

/**
 * 加密信息,本地存储
 * @param {String} key 本地存储key
 * @param {Object} info 用户信息
 */
export async function setLoginInfo(key: any, info: any) {
    console.log(key, info);
    if (key.length && JSON.stringify(info) !== '{}') {
        localStorage.setItem(key, JSON.stringify(info).toString()); //本地存储
        return true;
    } else {
        message.error('网络异常, 请稍后重试');
        return false;
    }
}

/**
 * @param {String} key 本地存储key
 */
export function getLoginInfo(key: any) {
    if (key.length) {
        let tk: any = localStorage.getItem(key); //把存储的值取出
        return JSON.parse(tk);
    }
}

/**
 * 清除登录状态
 */
export function clearLoginState() {
    localStorage.clear()
    window.location.reload()
}
