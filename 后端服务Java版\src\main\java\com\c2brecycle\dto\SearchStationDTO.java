package com.c2brecycle.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.*;

/**
 * 站点搜索DTO
 * 
 * <AUTHOR> Recycle Team
 * @since 2024-01-15
 */
@Data
@ApiModel("站点搜索参数")
public class SearchStationDTO {

    @ApiModelProperty(value = "搜索关键词", required = true, example = "回收站")
    @NotBlank(message = "搜索关键词不能为空")
    @Size(min = 1, max = 50, message = "关键词长度为1-50个字符")
    private String keyword;

    @ApiModelProperty(value = "城市", example = "北京市")
    @Size(max = 20, message = "城市名称最多20个字符")
    private String city;

    @ApiModelProperty(value = "页码", example = "1")
    @Min(value = 1, message = "页码必须大于0")
    private Integer page = 1;

    @ApiModelProperty(value = "每页数量", example = "20")
    @Min(value = 1, message = "每页数量至少为1")
    @Max(value = 100, message = "每页数量最多为100")
    private Integer size = 20;
}
