package com.c2brecycle.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.c2brecycle.common.exception.BusinessException;
import com.c2brecycle.common.result.ResultCode;
import com.c2brecycle.dto.SearchProductDTO;
import com.c2brecycle.entity.Product;
import com.c2brecycle.mapper.ProductMapper;
import com.c2brecycle.service.ProductService;
import com.c2brecycle.vo.ProductDetailVO;
import com.c2brecycle.vo.ProductVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 产品服务实现类
 * 
 * <AUTHOR> Recycle Team
 * @since 2024-01-15
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProductServiceImpl implements ProductService {

    private final ProductMapper productMapper;

    @Override
    public IPage<ProductVO> searchProducts(SearchProductDTO searchDTO) {
        log.info("搜索产品，关键词: {}, 分类: {}", searchDTO.getKeyword(), searchDTO.getCategoryId());
        
        Page<ProductVO> page = new Page<>(searchDTO.getPage(), searchDTO.getSize());
        
        return productMapper.searchProducts(page, searchDTO.getKeyword(), 
                searchDTO.getCategoryId(), searchDTO.getCity());
    }

    @Override
    public ProductDetailVO getProductDetail(Long productId, Long userId) {
        log.info("获取产品详情，productId: {}, userId: {}", productId, userId);
        
        Product product = productMapper.selectById(productId);
        if (product == null) {
            throw new BusinessException(ResultCode.PRODUCT_NOT_FOUND);
        }
        
        // 获取产品详情信息
        ProductDetailVO productDetail = productMapper.selectProductDetail(productId, userId);
        if (productDetail == null) {
            throw new BusinessException(ResultCode.PRODUCT_NOT_FOUND);
        }
        
        return productDetail;
    }

    @Override
    public List<ProductVO> getHotProducts(Long categoryId, Integer limit) {
        log.info("获取热门产品，分类: {}, 数量: {}", categoryId, limit);
        
        return productMapper.selectHotProducts(categoryId, limit);
    }

    @Override
    public Object getProductPrices(Long productId, Long stationId, String city) {
        log.info("获取产品价格，productId: {}, stationId: {}, city: {}", productId, stationId, city);
        
        // 检查产品是否存在
        Product product = productMapper.selectById(productId);
        if (product == null) {
            throw new BusinessException(ResultCode.PRODUCT_NOT_FOUND);
        }
        
        // 获取价格信息
        return productMapper.selectProductPrices(productId, stationId, city);
    }
}
