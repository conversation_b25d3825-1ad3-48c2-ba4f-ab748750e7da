-- C2B回收平台数据库表结构
-- 创建数据库
CREATE DATABASE IF NOT EXISTS c2b_recycle DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE c2b_recycle;

-- 用户表
CREATE TABLE users (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '用户ID',
    openid VARCHAR(100) NOT NULL UNIQUE COMMENT '微信OpenID',
    nickname VARCHAR(50) NOT NULL COMMENT '昵称',
    avatar VARCHAR(500) COMMENT '头像URL',
    phone VARCHAR(20) COMMENT '手机号',
    city VARCHAR(50) COMMENT '城市',
    status TINYINT DEFAULT 1 COMMENT '状态 0-禁用 1-正常',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT DEFAULT 0 COMMENT '逻辑删除 0-未删除 1-已删除',
    INDEX idx_openid (openid),
    INDEX idx_phone (phone),
    INDEX idx_city (city)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- 产品分类表
CREATE TABLE categories (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '分类ID',
    name VARCHAR(50) NOT NULL COMMENT '分类名称',
    icon VARCHAR(200) COMMENT '分类图标',
    sort INT DEFAULT 0 COMMENT '排序',
    status TINYINT DEFAULT 1 COMMENT '状态 0-禁用 1-启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    deleted TINYINT DEFAULT 0 COMMENT '逻辑删除 0-未删除 1-已删除',
    INDEX idx_sort (sort),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='产品分类表';

-- 产品表
CREATE TABLE products (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '产品ID',
    name VARCHAR(100) NOT NULL COMMENT '产品名称',
    image VARCHAR(500) COMMENT '产品图片',
    category_id BIGINT NOT NULL COMMENT '分类ID',
    description TEXT COMMENT '产品描述',
    content TEXT COMMENT '详细说明',
    unit VARCHAR(20) DEFAULT '公斤' COMMENT '单位',
    min_price DECIMAL(10,2) DEFAULT 0.00 COMMENT '最低价格',
    max_price DECIMAL(10,2) DEFAULT 0.00 COMMENT '最高价格',
    avg_price DECIMAL(10,2) DEFAULT 0.00 COMMENT '平均价格',
    search_count INT DEFAULT 0 COMMENT '搜索次数',
    status TINYINT DEFAULT 1 COMMENT '状态 0-下架 1-上架',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT DEFAULT 0 COMMENT '逻辑删除 0-未删除 1-已删除',
    INDEX idx_category_id (category_id),
    INDEX idx_name (name),
    INDEX idx_status (status),
    INDEX idx_search_count (search_count),
    FOREIGN KEY (category_id) REFERENCES categories(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='产品表';

-- 回收站点表
CREATE TABLE stations (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '站点ID',
    name VARCHAR(100) NOT NULL COMMENT '站点名称',
    phone VARCHAR(20) COMMENT '电话',
    address VARCHAR(200) NOT NULL COMMENT '地址',
    lng DECIMAL(10,6) NOT NULL COMMENT '经度',
    lat DECIMAL(10,6) NOT NULL COMMENT '纬度',
    city VARCHAR(50) NOT NULL COMMENT '城市',
    hours VARCHAR(100) COMMENT '营业时间',
    image VARCHAR(500) COMMENT '图片',
    rating DECIMAL(3,2) DEFAULT 5.00 COMMENT '评分',
    review_count INT DEFAULT 0 COMMENT '评价数',
    status TINYINT DEFAULT 1 COMMENT '状态 0-停业 1-营业',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT DEFAULT 0 COMMENT '逻辑删除 0-未删除 1-已删除',
    INDEX idx_city (city),
    INDEX idx_location (lng, lat),
    INDEX idx_status (status),
    INDEX idx_rating (rating)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='回收站点表';

-- 价格表
CREATE TABLE prices (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '价格ID',
    product_id BIGINT NOT NULL COMMENT '产品ID',
    station_id BIGINT NOT NULL COMMENT '站点ID',
    price_type TINYINT DEFAULT 1 COMMENT '价格类型 1-收购价 2-零售价 3-批发价',
    price DECIMAL(10,2) NOT NULL COMMENT '价格',
    quality_grade VARCHAR(20) COMMENT '质量等级',
    min_quantity DECIMAL(10,2) COMMENT '最小数量',
    max_quantity DECIMAL(10,2) COMMENT '最大数量',
    unit VARCHAR(20) DEFAULT '公斤' COMMENT '单位',
    status TINYINT DEFAULT 1 COMMENT '状态 0-无效 1-有效',
    effective_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '生效时间',
    expire_time TIMESTAMP NULL COMMENT '失效时间',
    remark VARCHAR(200) COMMENT '备注',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT DEFAULT 0 COMMENT '逻辑删除 0-未删除 1-已删除',
    INDEX idx_product_id (product_id),
    INDEX idx_station_id (station_id),
    INDEX idx_price_type (price_type),
    INDEX idx_status (status),
    INDEX idx_effective_time (effective_time),
    FOREIGN KEY (product_id) REFERENCES products(id),
    FOREIGN KEY (station_id) REFERENCES stations(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='价格表';

-- 用户地址表
CREATE TABLE user_addresses (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '地址ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    contact VARCHAR(50) NOT NULL COMMENT '联系人',
    phone VARCHAR(20) NOT NULL COMMENT '电话',
    province VARCHAR(50) NOT NULL COMMENT '省份',
    city VARCHAR(50) NOT NULL COMMENT '城市',
    district VARCHAR(50) NOT NULL COMMENT '区县',
    address VARCHAR(200) NOT NULL COMMENT '详细地址',
    lng DECIMAL(10,6) COMMENT '经度',
    lat DECIMAL(10,6) COMMENT '纬度',
    is_default TINYINT DEFAULT 0 COMMENT '是否默认地址 0-否 1-是',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT DEFAULT 0 COMMENT '逻辑删除 0-未删除 1-已删除',
    INDEX idx_user_id (user_id),
    INDEX idx_is_default (is_default),
    FOREIGN KEY (user_id) REFERENCES users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户地址表';

-- 收藏表
CREATE TABLE favorites (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '收藏ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    type TINYINT NOT NULL COMMENT '收藏类型 1-站点 2-产品',
    target_id BIGINT NOT NULL COMMENT '目标ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    deleted TINYINT DEFAULT 0 COMMENT '逻辑删除 0-未删除 1-已删除',
    INDEX idx_user_id (user_id),
    INDEX idx_type (type),
    INDEX idx_target_id (target_id),
    UNIQUE KEY uk_user_type_target (user_id, type, target_id),
    FOREIGN KEY (user_id) REFERENCES users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='收藏表';
