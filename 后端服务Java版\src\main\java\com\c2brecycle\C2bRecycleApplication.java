package com.c2brecycle;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * C2B回收平台主启动类
 * 
 * <AUTHOR> Recycle Team
 * @since 2024-01-15
 */
@SpringBootApplication
@EnableTransactionManagement
@EnableCaching
@MapperScan("com.c2brecycle.mapper")
public class C2bRecycleApplication {

    public static void main(String[] args) {
        SpringApplication.run(C2bRecycleApplication.class, args);
        System.out.println("🚀 C2B回收平台启动成功！");
        System.out.println("📚 API文档地址: http://localhost:8080/api/swagger-ui/");
        System.out.println("💾 数据库监控: http://localhost:8080/api/druid/");
        System.out.println("❤️ 健康检查: http://localhost:8080/api/actuator/health");
    }
}
