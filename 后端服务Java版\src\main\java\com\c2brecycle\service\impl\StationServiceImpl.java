package com.c2brecycle.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.c2brecycle.common.exception.BusinessException;
import com.c2brecycle.common.result.ResultCode;
import com.c2brecycle.dto.NearbyStationDTO;
import com.c2brecycle.dto.SearchStationDTO;
import com.c2brecycle.entity.Station;
import com.c2brecycle.mapper.StationMapper;
import com.c2brecycle.service.StationService;
import com.c2brecycle.vo.StationVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 站点服务实现类
 * 
 * <AUTHOR> Recycle Team
 * @since 2024-01-15
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class StationServiceImpl implements StationService {

    private final StationMapper stationMapper;

    @Override
    public IPage<StationVO> getNearbyStations(NearbyStationDTO queryDTO) {
        log.info("查询附近站点，经度: {}, 纬度: {}, 半径: {}km", 
                queryDTO.getLng(), queryDTO.getLat(), queryDTO.getRadius());
        
        Page<StationVO> page = new Page<>(queryDTO.getPage(), queryDTO.getSize());
        
        return stationMapper.selectNearbyStations(page, 
                queryDTO.getLng(), queryDTO.getLat(), queryDTO.getRadius());
    }

    @Override
    public IPage<StationVO> searchStations(SearchStationDTO searchDTO) {
        log.info("搜索站点，关键词: {}, 城市: {}", searchDTO.getKeyword(), searchDTO.getCity());
        
        Page<StationVO> page = new Page<>(searchDTO.getPage(), searchDTO.getSize());
        
        return stationMapper.searchStations(page, searchDTO.getKeyword(), searchDTO.getCity());
    }

    @Override
    public StationVO getStationDetail(Long stationId, Long userId) {
        log.info("获取站点详情，stationId: {}, userId: {}", stationId, userId);
        
        Station station = stationMapper.selectById(stationId);
        if (station == null) {
            throw new BusinessException(ResultCode.STATION_NOT_FOUND);
        }
        
        // TODO: 转换为StationDetailVO，包含收藏状态等信息
        StationVO stationVO = new StationVO();
        // 简单转换，实际应该使用BeanUtils或MapStruct
        stationVO.setId(station.getId());
        stationVO.setName(station.getName());
        stationVO.setPhone(station.getPhone());
        stationVO.setAddress(station.getAddress());
        stationVO.setLng(station.getLng());
        stationVO.setLat(station.getLat());
        stationVO.setCity(station.getCity());
        stationVO.setHours(station.getHours());
        stationVO.setImage(station.getImage());
        stationVO.setRating(station.getRating());
        stationVO.setReviewCount(station.getReviewCount());
        stationVO.setStatus(station.getStatus());
        
        return stationVO;
    }

    @Override
    public List<StationVO> getHotStations(String city, Integer limit) {
        log.info("获取热门站点，城市: {}, 数量: {}", city, limit);
        
        return stationMapper.selectHotStations(city, limit);
    }
}
