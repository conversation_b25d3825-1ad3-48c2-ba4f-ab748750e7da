package com.manmanrecycle.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 刷新Token请求DTO
 * 
 * <AUTHOR> Team
 * @since 2024-01-15
 */
@Data
@ApiModel("刷新Token请求")
public class RefreshTokenDTO {

    @ApiModelProperty(value = "刷新令牌", required = true)
    @NotBlank(message = "刷新令牌不能为空")
    private String refreshToken;
}
