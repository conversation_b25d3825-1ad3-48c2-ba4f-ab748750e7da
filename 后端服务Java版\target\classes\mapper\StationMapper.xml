<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.c2brecycle.mapper.StationMapper">

    <!-- 站点VO结果映射 -->
    <resultMap id="StationVOMap" type="com.c2brecycle.vo.StationVO">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="phone" property="phone"/>
        <result column="address" property="address"/>
        <result column="lng" property="lng"/>
        <result column="lat" property="lat"/>
        <result column="city" property="city"/>
        <result column="hours" property="hours"/>
        <result column="image" property="image"/>
        <result column="rating" property="rating"/>
        <result column="review_count" property="reviewCount"/>
        <result column="status" property="status"/>
        <result column="distance" property="distance"/>
        <result column="created_at" property="createdAt"/>
    </resultMap>

    <!-- 查询附近站点 -->
    <select id="selectNearbyStations" resultMap="StationVOMap">
        SELECT 
            s.id,
            s.name,
            s.phone,
            s.address,
            s.lng,
            s.lat,
            s.city,
            s.hours,
            s.image,
            s.rating,
            s.review_count,
            s.status,
            s.created_at,
            ROUND(
                6371 * ACOS(
                    COS(RADIANS(#{lat})) * COS(RADIANS(s.lat)) * 
                    COS(RADIANS(s.lng) - RADIANS(#{lng})) + 
                    SIN(RADIANS(#{lat})) * SIN(RADIANS(s.lat))
                ), 2
            ) AS distance
        FROM stations s
        WHERE s.deleted = 0 AND s.status = 1
        HAVING distance &lt;= #{radius}
        ORDER BY distance ASC, s.rating DESC
    </select>

    <!-- 搜索站点 -->
    <select id="searchStations" resultMap="StationVOMap">
        SELECT 
            s.id,
            s.name,
            s.phone,
            s.address,
            s.lng,
            s.lat,
            s.city,
            s.hours,
            s.image,
            s.rating,
            s.review_count,
            s.status,
            s.created_at
        FROM stations s
        WHERE s.deleted = 0 AND s.status = 1
        <if test="keyword != null and keyword != ''">
            AND (s.name LIKE CONCAT('%', #{keyword}, '%') 
                 OR s.address LIKE CONCAT('%', #{keyword}, '%'))
        </if>
        <if test="city != null and city != ''">
            AND s.city = #{city}
        </if>
        ORDER BY s.rating DESC, s.review_count DESC, s.created_at DESC
    </select>

</mapper>
