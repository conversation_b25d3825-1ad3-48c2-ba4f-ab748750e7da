import React, { memo, useEffect } from 'react'
import { shallowEqual, useDispatch, useSelector } from 'react-redux'
import { formatMinuteSecond } from '../../../../utils/handleData'
import ThemeHeaderRcm from '../../../../components/themeHeaderRCM'
import SongItem from '../songItem'
import { getToplistItemAction } from '../store/actionCreators'
import './style.css'
export default memo(function ToplistMain() {
  // props/states

  // redux hooks
  const dispatch = useDispatch()
  const { playCount, currentToplistId, currentToplist } = useSelector(
    (state: any) => ({
      playCount: state.getIn([
        'toplist',
        'currentToplistTitleInfo',
        'playCount',
      ]),
      currentToplistId: state.getIn(['toplist', 'currentToplistId']),
      currentToplist: state.getIn(['toplist', 'currentToplist'])
    }),
    shallowEqual
  )

  useEffect(() => {
    dispatch(getToplistItemAction(currentToplistId))
  }, [dispatch, currentToplistId])

  const rightSlot = (
    <span>
      播放：<em style={{ color: '#c20c0c', fontWeight: 700 }}>{playCount}</em>次
    </span>
  )

  return (
    <div className="toplistMainBox">
      <ThemeHeaderRcm title="歌曲列表" showIcon={false} right={rightSlot} />
      <div className="toplistMain">
        <div className="toplistMainHeader">
          <div className="toplistMainHeaderItem"></div>
          <div className="toplistMainHeaderItem toplistMainHeaderTitle">标题</div>
          <div className="toplistMainHeaderItem toplistMainHeaderTime">时长</div>
          <div className="toplistMainHeaderItem toplistMainHeaderSinger">歌手</div>
        </div>
        <div>
          {
            currentToplist && currentToplist.slice(0, 100).map((item: any, index: any) => {
              return <SongItem
                key={item.id}
                currentRanking={index + 1}
                coverPic={index < 3 ? item.al.picUrl : ''}
                duration={formatMinuteSecond(item.dt)}
                songName={item.name}
                singer={item.ar[0].name}
                singerId={item.ar[0].id}
                songId={item.id}
              />
            })
          }
        </div>
      </div>
    </div>

  )
})
