import React, { memo, useEffect } from 'react'
import { shallowEqual, useDispatch, useSelector } from 'react-redux'
import './style.css'
import TopListItem from './toplistItem'
import ToplistTitle from './toplistTitle'
import ToplistMain from './toplistMain'
import {
    getToplistTitleInfoAction,
    getToplistInfoAction,
} from './store/actionCreators'
import { useSearchParams } from 'react-router-dom'
import NavBar from '../../../components/navBar'
export default memo(function Toplist(props: any) {

    let [searchParams, setSearchParams] = useSearchParams();
    const dispatch = useDispatch()
    const { toplistInfo, currentToplistId } = useSelector(
        (state: any) => ({
            toplistInfo: state.getIn(['toplist', 'toplistInfo']),
            currentToplistId: state.getIn(['toplist', 'currentToplistId']),
        }),
        shallowEqual
    )

    useEffect(() => {
        dispatch(getToplistInfoAction())
    }, [dispatch])

    useEffect(() => {
        let id = searchParams.get('id')
        id = id ? id : currentToplistId
        dispatch(getToplistTitleInfoAction(id))
    }, [currentToplistId, dispatch, props])

    return (<div>
        <NavBar />
        <div className="toplistBox">
            <div className="toplistContent width980">
                <div className="toplistLeft">
                    <div className="toplistContainer">
                        <TopListItem toplistInfo={toplistInfo} />
                    </div>
                </div>

                <div className="toplistRight">
                    <ToplistTitle />
                    <ToplistMain />
                </div>
            </div>
        </div>
    </div>
    )
})
