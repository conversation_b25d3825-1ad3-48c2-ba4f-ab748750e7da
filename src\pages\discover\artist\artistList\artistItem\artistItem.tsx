import { getSizeImage } from '../../../../../utils/formatUtils';
import './style.css'

export default function ArtistItem(props: any) {
    const { info } = props;

    return (
        <div className='artistItemBox'>
            {
                <div className="image">
                    <a href={`#/artist?id=${info.id}`}>
                        <img src={getSizeImage(info.img1v1Url, 130)} alt="" />
                    </a>
                </div>
            }
            <div className="info">
                <a className="name" href={`#/artist?id=${info.id}`}>{info.name}</a>
                <i className="icon"></i>
            </div>
        </div>
    )
}
