import React, { memo } from 'react'
import propTypes from 'prop-types'
import './style.css'
const ThemeHeaderRCM = function ThemeHeaderRCM(props: any) {
    const { title, keywords, showIcon, right, titleClick, keywordsClick } = props
    return (
        <div className={showIcon ? "themeHeaderBox" : "themeHeaderBox themeHeaderBoxNotShow"} >
            <div className="themeHeaderLeft">
                <h2 className="themeHeaderTitle">
                    <em onClick={() => { titleClick() }}>{title}</em>
                </h2>
                <ul className="themeHeaderKeywords">
                    {keywords.map((item: any) => {
                        return (
                            <li className="themeHeaderKeywordsItem" key={item}>
                                <em onClick={() => { keywordsClick(item) }}>{item}</em>
                                <span className="themeHeaderKeywordsLine">|</span>
                            </li>
                        )
                    })}
                </ul>
            </div>
            <div className="themeHeaderRight">
                <span onClick={() => { titleClick() }}>{right}</span>
                {showIcon && <i className="themeHeaderIcon"></i>}
            </div>
        </div>
    )
}

ThemeHeaderRCM.propTypes = {
    // title属性必填
    title: propTypes.string.isRequired,
    keywords: propTypes.array,
    showIcon: propTypes.bool,
    right: propTypes.any,
    titleClick: propTypes.func,
    keywordsClick: propTypes.func,
}

ThemeHeaderRCM.defaultProps = {
    keywords: [],
    showIcon: true,
    right: '更多'
}

export default memo(ThemeHeaderRCM)
