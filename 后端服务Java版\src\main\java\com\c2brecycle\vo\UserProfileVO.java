package com.c2brecycle.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户信息VO
 * 
 * <AUTHOR> Recycle Team
 * @since 2024-01-15
 */
@Data
@ApiModel("用户信息")
public class UserProfileVO {

    @ApiModelProperty("用户ID")
    private Long id;

    @ApiModelProperty("微信OpenID")
    private String openid;

    @ApiModelProperty("昵称")
    private String nickname;

    @ApiModelProperty("头像")
    private String avatar;

    @ApiModelProperty("手机号")
    private String phone;

    @ApiModelProperty("城市")
    private String city;

    @ApiModelProperty("状态 0-禁用 1-正常")
    private Integer status;

    @ApiModelProperty("注册时间")
    private LocalDateTime createdAt;

    @ApiModelProperty("最后更新时间")
    private LocalDateTime updatedAt;
}
