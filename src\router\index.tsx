import React from 'react'
import { Navigate, Route, Routes } from 'react-router-dom'
import Album from "../pages/discover/album";
import DjRadio from "../pages/discover/djRadio";
import SongList from "../pages/discover/songList";
import TopList from '../pages/discover/toplist'
import Artist from '../pages/discover/artist'
import Recommend from "../pages/discover/recommend";
import NotFound from "../pages/notFound";
import SongDetail from "../pages/songDetail";
import User from '../pages/user'
import MyMusic from '../pages/myMusic';
import Focus from '../pages/focus';
import Search from '../pages/search';
import Single from '../pages/search/single'
import Singer from '../pages/search/singer'


export default function RouterConfig() {
  return <Routes>
    <Route path="/" element={<Navigate to="/discover" />}></Route>
    <Route path="/discover" element={<Navigate to="/discover/recommend" />}></Route>
    <Route path="discover">
      <Route path="recommend" element={<Recommend />} />
      <Route path="album" element={<Album />} />
      <Route path="toplist" element={<TopList />} />
      <Route path="djRadio" element={<DjRadio />} />
      <Route path="artist" element={<Artist />} />
      <Route path="songList" element={<SongList />} />
    </Route>
    <Route path="song" element={<SongDetail />} />
    <Route path="search/*" element={<Search />} />
    <Route path="single" element={<Single />} />
    <Route path="singer" element={<Singer />} />
    <Route path="user" element={<User />} />
    <Route path="mymusic" element={<MyMusic />} />
    <Route path="focus" element={<Focus />} />
    <Route path='*' element={<NotFound />}></Route>
  </Routes>

}
