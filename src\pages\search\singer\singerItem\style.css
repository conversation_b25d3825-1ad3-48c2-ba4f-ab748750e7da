.singerItemBox {
    width: 130px;
    height: 154px;
    overflow: hidden;
    padding: 0 0 30px 0;
    line-height: 1.4;
    margin-right: 62px;
    margin-bottom: 30px;
}
.singerItemBox:nth-child(5n) {
    margin-right: 0;
}

.singerItemBox .singerItemImage {
    position: relative;
    width: 130px;
    height: 130px;
}
.singerItemBox .singerItemImage img {
    width: 100%;
    height: 100%;
}
.singerItemBox .singerItemImage .singerItemImageCover {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    background: url(../../../../static/images/sprite_cover.png) no-repeat 0 -680px;
}

.singerItemBox .singerItemInfo {
    display: flex;
    justify-content: space-between;
    margin-top: 8px;
}

.singerItemBox .singerItemInfo .singerItemIcon {
    width: 17px;
    height: 18px;
    background: url(../../../../static/images/sprite_icon2.png) 0 -740px;
}