-- C2B回收平台初始化数据

USE c2b_recycle;

-- 插入产品分类数据
INSERT INTO categories (id, name, icon, sort, status) VALUES
(1, '废纸类', '📄', 1, 1),
(2, '废塑料', '🥤', 2, 1),
(3, '废金属', '🔧', 3, 1),
(4, '废玻璃', '🍾', 4, 1),
(5, '电子废料', '📱', 5, 1),
(6, '废纺织品', '👕', 6, 1),
(7, '废橡胶', '🛞', 7, 1),
(8, '有害废物', '☢️', 8, 1);

-- 插入产品数据
INSERT INTO products (id, name, image, category_id, description, unit, min_price, max_price, avg_price, search_count, status) VALUES
-- 废纸类
(1, '废报纸', 'https://via.placeholder.com/200x200?text=废报纸', 1, '旧报纸、杂志等印刷品', '公斤', 0.8, 1.2, 1.0, 150, 1),
(2, '废纸箱', 'https://via.placeholder.com/200x200?text=废纸箱', 1, '各种纸箱、包装盒', '公斤', 1.0, 1.5, 1.25, 200, 1),
(3, '废书本', 'https://via.placeholder.com/200x200?text=废书本', 1, '旧书籍、教材等', '公斤', 0.6, 1.0, 0.8, 80, 1),
(4, '办公用纸', 'https://via.placeholder.com/200x200?text=办公用纸', 1, 'A4纸、复印纸等', '公斤', 1.2, 1.8, 1.5, 120, 1),

-- 废塑料
(5, 'PET塑料瓶', 'https://via.placeholder.com/200x200?text=PET瓶', 2, '饮料瓶、矿泉水瓶', '公斤', 2.0, 3.0, 2.5, 180, 1),
(6, 'PE塑料袋', 'https://via.placeholder.com/200x200?text=PE袋', 2, '购物袋、垃圾袋等', '公斤', 1.5, 2.2, 1.85, 90, 1),
(7, 'PP塑料制品', 'https://via.placeholder.com/200x200?text=PP制品', 2, '塑料盒、塑料椅等', '公斤', 1.8, 2.8, 2.3, 110, 1),
(8, 'PVC管材', 'https://via.placeholder.com/200x200?text=PVC管', 2, 'PVC水管、线管等', '公斤', 1.2, 2.0, 1.6, 70, 1),

-- 废金属
(9, '废铁', 'https://via.placeholder.com/200x200?text=废铁', 3, '废钢铁、铁制品', '公斤', 1.5, 2.5, 2.0, 160, 1),
(10, '废铜', 'https://via.placeholder.com/200x200?text=废铜', 3, '废铜线、铜制品', '公斤', 35.0, 45.0, 40.0, 140, 1),
(11, '废铝', 'https://via.placeholder.com/200x200?text=废铝', 3, '铝合金、铝制品', '公斤', 8.0, 12.0, 10.0, 130, 1),
(12, '废不锈钢', 'https://via.placeholder.com/200x200?text=不锈钢', 3, '不锈钢制品', '公斤', 6.0, 10.0, 8.0, 100, 1),

-- 电子废料
(13, '废手机', 'https://via.placeholder.com/200x200?text=废手机', 5, '旧手机、智能手机', '台', 20.0, 100.0, 60.0, 220, 1),
(14, '废电脑', 'https://via.placeholder.com/200x200?text=废电脑', 5, '台式机、笔记本电脑', '台', 50.0, 300.0, 175.0, 180, 1),
(15, '废电视', 'https://via.placeholder.com/200x200?text=废电视', 5, 'CRT、液晶电视', '台', 30.0, 150.0, 90.0, 120, 1),
(16, '废电池', 'https://via.placeholder.com/200x200?text=废电池', 5, '各种废旧电池', '公斤', 5.0, 15.0, 10.0, 90, 1);

-- 插入回收站点数据
INSERT INTO stations (id, name, phone, address, lng, lat, city, hours, rating, review_count, status) VALUES
(1, '朝阳区环保回收站', '010-12345678', '北京市朝阳区三里屯街道1号', 116.447303, 39.937967, '北京市', '08:00-18:00', 4.5, 128, 1),
(2, '海淀区绿色回收点', '010-87654321', '北京市海淀区中关村大街2号', 116.310316, 39.992957, '北京市', '09:00-17:00', 4.3, 95, 1),
(3, '丰台区废品收购站', '010-11223344', '北京市丰台区南三环西路3号', 116.286968, 39.858364, '北京市', '08:30-17:30', 4.2, 76, 1),
(4, '东城区回收服务中心', '010-55667788', '北京市东城区王府井大街4号', 116.417592, 39.917723, '北京市', '09:00-18:00', 4.6, 142, 1),
(5, '西城区环保站', '010-99887766', '北京市西城区西单北大街5号', 116.374142, 39.913611, '北京市', '08:00-17:00', 4.4, 89, 1),

(6, '浦东新区回收中心', '021-12345678', '上海市浦东新区陆家嘴环路6号', 121.499763, 31.245944, '上海市', '08:00-18:00', 4.7, 156, 1),
(7, '徐汇区绿色回收', '021-87654321', '上海市徐汇区淮海中路7号', 121.444443, 31.213849, '上海市', '09:00-17:30', 4.3, 103, 1),
(8, '黄浦区废品站', '021-11223344', '上海市黄浦区南京东路8号', 121.484443, 31.235849, '上海市', '08:30-18:00', 4.5, 118, 1),

(9, '天河区回收服务点', '020-12345678', '广州市天河区天河路9号', 113.331954, 23.145474, '广州市', '08:00-17:30', 4.4, 92, 1),
(10, '越秀区环保回收', '020-87654321', '广州市越秀区中山五路10号', 113.266887, 23.129163, '广州市', '09:00-18:00', 4.2, 67, 1);

-- 插入价格数据
INSERT INTO prices (product_id, station_id, price_type, price, quality_grade, unit, status) VALUES
-- 废报纸价格
(1, 1, 1, 1.0, '普通', '公斤', 1),
(1, 2, 1, 1.1, '普通', '公斤', 1),
(1, 3, 1, 0.9, '普通', '公斤', 1),
(1, 4, 1, 1.2, '优质', '公斤', 1),

-- 废纸箱价格
(2, 1, 1, 1.3, '普通', '公斤', 1),
(2, 2, 1, 1.4, '普通', '公斤', 1),
(2, 3, 1, 1.2, '普通', '公斤', 1),
(2, 4, 1, 1.5, '优质', '公斤', 1),

-- PET塑料瓶价格
(5, 1, 1, 2.5, '普通', '公斤', 1),
(5, 2, 1, 2.8, '普通', '公斤', 1),
(5, 3, 1, 2.3, '普通', '公斤', 1),
(5, 6, 1, 2.9, '优质', '公斤', 1),

-- 废铁价格
(9, 1, 1, 2.0, '普通', '公斤', 1),
(9, 2, 1, 2.2, '普通', '公斤', 1),
(9, 3, 1, 1.8, '普通', '公斤', 1),
(9, 9, 1, 2.3, '优质', '公斤', 1),

-- 废铜价格
(10, 1, 1, 40.0, '普通', '公斤', 1),
(10, 2, 1, 42.0, '普通', '公斤', 1),
(10, 4, 1, 43.0, '优质', '公斤', 1),
(10, 6, 1, 41.0, '普通', '公斤', 1),

-- 废手机价格
(13, 1, 1, 60.0, '普通', '台', 1),
(13, 2, 1, 80.0, '良好', '台', 1),
(13, 4, 1, 100.0, '优质', '台', 1),
(13, 6, 1, 70.0, '普通', '台', 1);

-- 插入测试用户数据
INSERT INTO users (id, openid, nickname, avatar, phone, city, status) VALUES
(1, 'test_openid_001', '测试用户1', 'https://via.placeholder.com/100x100?text=U1', '13800138001', '北京市', 1),
(2, 'test_openid_002', '测试用户2', 'https://via.placeholder.com/100x100?text=U2', '13800138002', '上海市', 1),
(3, 'test_openid_003', '测试用户3', 'https://via.placeholder.com/100x100?text=U3', '13800138003', '广州市', 1);

-- 插入测试地址数据
INSERT INTO user_addresses (user_id, contact, phone, province, city, district, address, lng, lat, is_default) VALUES
(1, '张三', '13800138001', '北京市', '北京市', '朝阳区', '三里屯街道1号院', 116.447303, 39.937967, 1),
(1, '张三', '13800138001', '北京市', '北京市', '海淀区', '中关村大街2号', 116.310316, 39.992957, 0),
(2, '李四', '13800138002', '上海市', '上海市', '浦东新区', '陆家嘴环路6号', 121.499763, 31.245944, 1),
(3, '王五', '13800138003', '广东省', '广州市', '天河区', '天河路9号', 113.331954, 23.145474, 1);

-- 插入测试收藏数据
INSERT INTO favorites (user_id, type, target_id) VALUES
(1, 1, 1), -- 用户1收藏站点1
(1, 1, 2), -- 用户1收藏站点2
(1, 2, 1), -- 用户1收藏产品1
(1, 2, 5), -- 用户1收藏产品5
(2, 1, 6), -- 用户2收藏站点6
(2, 2, 13), -- 用户2收藏产品13
(3, 1, 9), -- 用户3收藏站点9
(3, 2, 10); -- 用户3收藏产品10
