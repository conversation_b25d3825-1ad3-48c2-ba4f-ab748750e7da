package com.manmanrecycle.common.result;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 响应状态码枚举
 * 
 * <AUTHOR> Team
 * @since 2024-01-15
 */
@Getter
@AllArgsConstructor
public enum ResultCode {

    // 成功
    SUCCESS(200, "操作成功"),
    CREATED(201, "创建成功"),
    ACCEPTED(202, "请求已接受"),
    NO_CONTENT(204, "无内容"),

    // 客户端错误 4xx
    BAD_REQUEST(400, "请求参数错误"),
    UNAUTHORIZED(401, "未授权访问"),
    FORBIDDEN(403, "禁止访问"),
    NOT_FOUND(404, "资源不存在"),
    METHOD_NOT_ALLOWED(405, "请求方法不允许"),
    NOT_ACCEPTABLE(406, "不可接受的请求"),
    CONFLICT(409, "请求冲突"),
    GONE(410, "资源已删除"),
    PRECONDITION_FAILED(412, "前置条件失败"),
    PAYLOAD_TOO_LARGE(413, "请求体过大"),
    UNSUPPORTED_MEDIA_TYPE(415, "不支持的媒体类型"),
    UNPROCESSABLE_ENTITY(422, "无法处理的实体"),
    TOO_MANY_REQUESTS(429, "请求过于频繁"),

    // 服务器错误 5xx
    INTERNAL_SERVER_ERROR(500, "服务器内部错误"),
    NOT_IMPLEMENTED(501, "功能未实现"),
    BAD_GATEWAY(502, "网关错误"),
    SERVICE_UNAVAILABLE(503, "服务不可用"),
    GATEWAY_TIMEOUT(504, "网关超时"),

    // 业务错误 1xxx
    BUSINESS_ERROR(1000, "业务处理失败"),
    VALIDATION_ERROR(1001, "数据验证失败"),
    DUPLICATE_ERROR(1002, "数据重复"),
    DEPENDENCY_ERROR(1003, "依赖关系错误"),
    STATE_ERROR(1004, "状态错误"),

    // 认证授权错误 2xxx
    TOKEN_INVALID(2001, "Token无效"),
    TOKEN_EXPIRED(2002, "Token已过期"),
    TOKEN_MISSING(2003, "Token缺失"),
    LOGIN_FAILED(2004, "登录失败"),
    PERMISSION_DENIED(2005, "权限不足"),
    ACCOUNT_DISABLED(2006, "账户已禁用"),
    ACCOUNT_LOCKED(2007, "账户已锁定"),

    // 微信相关错误 3xxx
    WECHAT_CODE_INVALID(3001, "微信授权码无效"),
    WECHAT_API_ERROR(3002, "微信API调用失败"),
    WECHAT_USER_INFO_ERROR(3003, "获取微信用户信息失败"),

    // 价格相关错误 4xxx
    PRICE_NOT_FOUND(4001, "价格信息不存在"),
    PRICE_OUTDATED(4002, "价格信息已过期"),
    PRICE_ANOMALY(4003, "价格异常"),
    PRICE_ALERT_EXISTS(4004, "价格预警已存在"),
    PRICE_ALERT_NOT_FOUND(4005, "价格预警不存在"),

    // 站点相关错误 5xxx
    STATION_NOT_FOUND(5001, "回收站点不存在"),
    STATION_INACTIVE(5002, "回收站点未营业"),
    STATION_FULL(5003, "回收站点已满"),
    STATION_SERVICE_UNAVAILABLE(5004, "站点服务不可用"),

    // 产品相关错误 6xxx
    PRODUCT_NOT_FOUND(6001, "产品不存在"),
    PRODUCT_INACTIVE(6002, "产品已下架"),
    CATEGORY_NOT_FOUND(6003, "产品分类不存在"),

    // 用户相关错误 7xxx
    USER_NOT_FOUND(7001, "用户不存在"),
    USER_INACTIVE(7002, "用户已禁用"),
    ADDRESS_NOT_FOUND(7003, "地址不存在"),
    ADDRESS_LIMIT_EXCEEDED(7004, "地址数量超限"),
    FAVORITE_EXISTS(7005, "已收藏该项目"),
    FAVORITE_NOT_FOUND(7006, "收藏不存在"),

    // 文件上传错误 8xxx
    FILE_UPLOAD_ERROR(8001, "文件上传失败"),
    FILE_TYPE_NOT_SUPPORTED(8002, "文件类型不支持"),
    FILE_SIZE_EXCEEDED(8003, "文件大小超限"),
    FILE_NOT_FOUND(8004, "文件不存在"),

    // 外部服务错误 9xxx
    EXTERNAL_SERVICE_ERROR(9001, "外部服务调用失败"),
    GEOCODING_ERROR(9002, "地址解析失败"),
    SMS_SEND_ERROR(9003, "短信发送失败"),
    EMAIL_SEND_ERROR(9004, "邮件发送失败"),
    PUSH_NOTIFICATION_ERROR(9005, "推送通知失败");

    /**
     * 状态码
     */
    private final Integer code;

    /**
     * 状态消息
     */
    private final String message;

    /**
     * 根据状态码获取枚举
     */
    public static ResultCode getByCode(Integer code) {
        for (ResultCode resultCode : values()) {
            if (resultCode.getCode().equals(code)) {
                return resultCode;
            }
        }
        return INTERNAL_SERVER_ERROR;
    }

    /**
     * 判断是否为成功状态码
     */
    public boolean isSuccess() {
        return this.code >= 200 && this.code < 300;
    }

    /**
     * 判断是否为客户端错误
     */
    public boolean isClientError() {
        return this.code >= 400 && this.code < 500;
    }

    /**
     * 判断是否为服务器错误
     */
    public boolean isServerError() {
        return this.code >= 500 && this.code < 600;
    }

    /**
     * 判断是否为业务错误
     */
    public boolean isBusinessError() {
        return this.code >= 1000 && this.code < 10000;
    }
}
