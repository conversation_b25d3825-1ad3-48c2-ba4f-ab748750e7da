package com.c2crestore.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.manmanrecycle.common.result.Result;
import com.manmanrecycle.dto.NearbyStationDTO;
import com.manmanrecycle.dto.SearchStationDTO;
import com.manmanrecycle.service.StationService;
import com.manmanrecycle.vo.StationDetailVO;
import com.manmanrecycle.vo.StationVO;
import io.swagger.annotations.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

/**
 * 回收站点控制器
 * 
 * <AUTHOR> Team
 * @since 2024-01-15
 */
@Slf4j
@RestController
@RequestMapping("/stations")
@RequiredArgsConstructor
@Api(tags = "🏪 站点模块", description = "回收站点相关接口")
public class StationController {

    private final StationService stationService;

    /**
     * 获取附近站点
     */
    @GetMapping("/nearby")
    @ApiOperation(value = "获取附近站点", notes = "根据经纬度查询附近的回收站点")
    @ApiResponses({
        @ApiResponse(code = 200, message = "查询成功"),
        @ApiResponse(code = 400, message = "请求参数错误")
    })
    public Result<IPage<StationVO>> getNearbyStations(
            @ApiParam("附近站点查询参数") @Valid NearbyStationDTO queryDTO,
            HttpServletRequest request) {
        
        log.info("查询附近站点，经度: {}, 纬度: {}, 半径: {}km", 
                queryDTO.getLng(), queryDTO.getLat(), queryDTO.getRadius());
        
        // 记录用户查询行为（如果已登录）
        Long userId = (Long) request.getAttribute("userId");
        if (userId != null) {
            log.info("用户{}查询附近站点", userId);
        }
        
        IPage<StationVO> stations = stationService.getNearbyStations(queryDTO);
        
        return Result.success("获取附近站点成功", stations);
    }

    /**
     * 搜索站点
     */
    @GetMapping("/search")
    @ApiOperation(value = "搜索站点", notes = "根据关键词搜索回收站点")
    @ApiResponses({
        @ApiResponse(code = 200, message = "搜索成功"),
        @ApiResponse(code = 400, message = "请求参数错误")
    })
    public Result<IPage<StationVO>> searchStations(
            @ApiParam("站点搜索参数") @Valid SearchStationDTO searchDTO,
            HttpServletRequest request) {
        
        log.info("搜索站点，关键词: {}, 城市: {}", searchDTO.getKeyword(), searchDTO.getCity());
        
        // 记录搜索行为（如果已登录）
        Long userId = (Long) request.getAttribute("userId");
        if (userId != null) {
            log.info("用户{}搜索站点: {}", userId, searchDTO.getKeyword());
        }
        
        IPage<StationVO> stations = stationService.searchStations(searchDTO);
        
        return Result.success("搜索站点成功", stations);
    }

    /**
     * 获取站点详情
     */
    @GetMapping("/{id}")
    @ApiOperation(value = "获取站点详情", notes = "根据站点ID获取详细信息")
    @ApiImplicitParam(name = "id", value = "站点ID", required = true, dataType = "long", paramType = "path")
    @ApiResponses({
        @ApiResponse(code = 200, message = "获取成功"),
        @ApiResponse(code = 404, message = "站点不存在")
    })
    public Result<StationDetailVO> getStationDetail(
            @PathVariable Long id,
            HttpServletRequest request) {
        
        log.info("获取站点详情，stationId: {}", id);
        
        // 记录浏览行为（如果已登录）
        Long userId = (Long) request.getAttribute("userId");
        if (userId != null) {
            log.info("用户{}查看站点详情: {}", userId, id);
        }
        
        StationDetailVO stationDetail = stationService.getStationDetail(id, userId);
        
        return Result.success("获取站点详情成功", stationDetail);
    }

    /**
     * 获取热门站点
     */
    @GetMapping("/hot")
    @ApiOperation(value = "获取热门站点", notes = "获取评分较高的热门回收站点")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "city", value = "城市", dataType = "string", paramType = "query"),
        @ApiImplicitParam(name = "limit", value = "数量限制", dataType = "int", paramType = "query", defaultValue = "10")
    })
    @ApiResponses({
        @ApiResponse(code = 200, message = "获取成功")
    })
    public Result<java.util.List<StationVO>> getHotStations(
            @RequestParam(required = false) String city,
            @RequestParam(defaultValue = "10") Integer limit) {
        
        log.info("获取热门站点，城市: {}, 数量: {}", city, limit);
        
        java.util.List<StationVO> hotStations = stationService.getHotStations(city, limit);
        
        return Result.success("获取热门站点成功", hotStations);
    }
}
