.playListItemBox {
    display: flex;
    align-items: center;
    padding-left: 15px;
    height: 28px;
    cursor: pointer;
    justify-content: space-around;
    margin-top: 2px;
}
.playListItemBox:hover {
  background-color: rgba(0, 0, 0, 0.4);
  color: #fff;
}

.playListItemBox .active {
  background-color: rgba(0, 0, 0, 0.4);
}

.playListItemBox .playListItemSongName {
    width: 250px;
    height: 28px;
    text-align: left;
    line-height: 28px;
    text-indent: -18px;
}

.playListItemBox .playListItemControlSinger {
  display: flex;
}
.playListItemBox .playListItemControlSinger .anticon-delete,
.playListItemBox .playListItemControlSinger .anticon-download,
.playListItemBox .playListItemControlSinger .anticon-like,
.playListItemBox .playListItemControlSinger .anticon-github {
    opacity: 0;
    color: #ccc;
    font-size: 14px;
    margin: 2px 6px 0;
}

.playListItemBox .playListItemControlSinger .anticon-delete:hover,
.playListItemBox .playListItemControlSinger .anticon-download:hover,
.playListItemBox .playListItemControlSinger span:hover,
.playListItemBox .playListItemControlSinger .anticon-like:hover,
.playListItemBox .playListItemControlSinger .anticon-github:hover {
    color: #fff;
}

.playListItemBox .playListItemControlSinger span {
    margin-left: 4px;
}

.playListItemBox .playListItemControlSinger:hover .anticon-delete,
.playListItemBox .playListItemControlSinger:hover .anticon-download,
.playListItemBox .playListItemControlSinger:hover .anticon-like,
.playListItemBox .playListItemControlSinger:hover .anticon-github {
  opacity: 1;
}

