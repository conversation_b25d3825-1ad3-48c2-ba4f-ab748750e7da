.toplistMainBox {
    padding: 10px 40px;
}
.toplistMainBox .toplistMain {
    border: 1px solid #d9d9d9;
}
.toplistMainBox .toplistMain .toplistMainHeader {
    display: flex;
}
.toplistMainBox .toplistMain .toplistMainHeader .toplistMainHeaderItem {
    width: 74px;
    height: 34px;
    line-height: 18px;
    padding: 8px 10px;
    background-position: 0 0;
    background-repeat: repeat-x;
    background-color: #f2f2f2;
    color: #666;
}
.toplistMainBox .toplistMain .toplistMainHeader .toplistMainHeaderItem:first-child {
    border-right: 1px solid #dadada;
    border-bottom: 1px solid #dadada;
}
.toplistMainBox .toplistMain .toplistMainHeader .toplistMainHeaderTitle {
    width: 327px;
    border-right: 1px solid #dadada;
    border-bottom: 1px solid #dadada;
}
.toplistMainBox .toplistMain .toplistMainHeader .toplistMainHeaderSinger {
    width: 173px;
    border-bottom: 1px solid #dadada;
}
.toplistMainBox .toplistMain .toplistMainHeader .toplistMainHeaderTime {
    width: 91px;
    border-right: 1px solid #dadada;
    border-bottom: 1px solid #dadada;
}
