import request from './request'

export function getSongDetail(ids: number) {
  return request({
    url: '/song/detail',
    params: {
      ids,
    },
  })
}

export function getLyric(id: any) {
  return request({
    url: '/lyric',
    params: {
      id,
    },
  })
}

// /comment/hot?type=0&id=167876
export function getHotComment(id: any, type = 0) {
  return request({
    url: '/comment/hot',
    params: {
      id,
      type,
    },
  })
}

// 歌曲评论
export function getSongComment(id: any, limit = 20, offset = 0) {
  return request({
    url: '/comment/music',
    params: {
      id,
      limit,
      offset,
      timestamp: new Date().getTime()
    },
  })
}

// 评论歌曲
export function sendSongComment(id: any, content: any, cookie: any) {
  return request({
    url: '/comment',
    method: 'get',
    params: {
      t: 1, // 发送
      type: 0, // 歌曲类型
      id,
      content: content,
      cookie: cookie,
      timestamp: new Date().getTime()
    },
  })
}

// 歌曲评论
export function getSimilaritySong(songId: any) {
  return request({
    url: '/simi/song',
    params: {
      id: songId,
    },
  })
}