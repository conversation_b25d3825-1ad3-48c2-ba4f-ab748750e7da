import {
  FETCH_CATEGORIES_REQUEST,
  FETCH_CATEGORIES_SUCCESS,
  FETCH_CATEGORIES_FAILURE,
  SEARCH_PRODUCTS_REQUEST,
  SEARCH_PRODUCTS_SUCCESS,
  SEARCH_PRODUCTS_FAILURE,
  FETCH_PRODUCT_DETAIL_REQUEST,
  FETCH_PRODUCT_DETAIL_SUCCESS,
  FETCH_PRODUCT_DETAIL_FAILURE,
  FETCH_HOT_PRODUCTS_REQUEST,
  FETCH_HOT_PRODUCTS_SUCCESS,
  FETCH_HOT_PRODUCTS_FAILURE,
  FETCH_PRODUCT_PRICES_REQUEST,
  FETCH_PRODUCT_PRICES_SUCCESS,
  FETCH_PRODUCT_PRICES_FAILURE,
  CLEAR_PRODUCT_DETAIL,
  C<PERSON><PERSON>_SEARCH_RESULTS
} from '../actions/productActions';

const initialState = {
  // 分类数据
  categories: [],
  categoriesLoading: false,
  categoriesError: null,
  
  // 搜索结果
  searchResults: [],
  searchTotal: 0,
  searchCurrent: 1,
  searchSize: 10,
  searchParams: {},
  searchLoading: false,
  searchError: null,
  
  // 产品详情
  productDetail: null,
  productDetailLoading: false,
  productDetailError: null,
  
  // 热门产品
  hotProducts: [],
  hotProductsLoading: false,
  hotProductsError: null,
  
  // 产品价格
  productPrices: null,
  productPricesLoading: false,
  productPricesError: null
};

const productReducer = (state = initialState, action) => {
  switch (action.type) {
    // 分类相关
    case FETCH_CATEGORIES_REQUEST:
      return {
        ...state,
        categoriesLoading: true,
        categoriesError: null
      };

    case FETCH_CATEGORIES_SUCCESS:
      return {
        ...state,
        categoriesLoading: false,
        categories: action.payload,
        categoriesError: null
      };

    case FETCH_CATEGORIES_FAILURE:
      return {
        ...state,
        categoriesLoading: false,
        categoriesError: action.payload
      };

    // 搜索相关
    case SEARCH_PRODUCTS_REQUEST:
      return {
        ...state,
        searchLoading: true,
        searchError: null
      };

    case SEARCH_PRODUCTS_SUCCESS:
      return {
        ...state,
        searchLoading: false,
        searchResults: action.payload.products,
        searchTotal: action.payload.total,
        searchCurrent: action.payload.current,
        searchSize: action.payload.size,
        searchParams: action.payload.searchParams,
        searchError: null
      };

    case SEARCH_PRODUCTS_FAILURE:
      return {
        ...state,
        searchLoading: false,
        searchError: action.payload
      };

    case CLEAR_SEARCH_RESULTS:
      return {
        ...state,
        searchResults: [],
        searchTotal: 0,
        searchCurrent: 1,
        searchParams: {},
        searchError: null
      };

    // 产品详情相关
    case FETCH_PRODUCT_DETAIL_REQUEST:
      return {
        ...state,
        productDetailLoading: true,
        productDetailError: null
      };

    case FETCH_PRODUCT_DETAIL_SUCCESS:
      return {
        ...state,
        productDetailLoading: false,
        productDetail: action.payload,
        productDetailError: null
      };

    case FETCH_PRODUCT_DETAIL_FAILURE:
      return {
        ...state,
        productDetailLoading: false,
        productDetailError: action.payload
      };

    case CLEAR_PRODUCT_DETAIL:
      return {
        ...state,
        productDetail: null,
        productDetailError: null
      };

    // 热门产品相关
    case FETCH_HOT_PRODUCTS_REQUEST:
      return {
        ...state,
        hotProductsLoading: true,
        hotProductsError: null
      };

    case FETCH_HOT_PRODUCTS_SUCCESS:
      return {
        ...state,
        hotProductsLoading: false,
        hotProducts: action.payload,
        hotProductsError: null
      };

    case FETCH_HOT_PRODUCTS_FAILURE:
      return {
        ...state,
        hotProductsLoading: false,
        hotProductsError: action.payload
      };

    // 产品价格相关
    case FETCH_PRODUCT_PRICES_REQUEST:
      return {
        ...state,
        productPricesLoading: true,
        productPricesError: null
      };

    case FETCH_PRODUCT_PRICES_SUCCESS:
      return {
        ...state,
        productPricesLoading: false,
        productPrices: action.payload,
        productPricesError: null
      };

    case FETCH_PRODUCT_PRICES_FAILURE:
      return {
        ...state,
        productPricesLoading: false,
        productPricesError: action.payload
      };

    default:
      return state;
  }
};

export default productReducer;
