import request from '../utils/request';

/**
 * 认证相关API
 */

// 微信登录
export const wechatLogin = (data) => {
  return request.post('/auth/wechat-login', data);
};

// 刷新Token
export const refreshToken = (refreshToken) => {
  return request.post('/auth/refresh-token', { refreshToken });
};

// 用户登出
export const logout = () => {
  return request.post('/auth/logout');
};

// 验证Token
export const verifyToken = () => {
  return request.get('/auth/verify');
};

// 模拟登录（开发环境使用）
export const mockLogin = (data) => {
  return request.post('/auth/mock-login', data);
};
