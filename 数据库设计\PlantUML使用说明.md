# 📊 PlantUML ER关系图使用说明

## 📋 文件说明

我已经为您创建了3个PlantUML格式的数据库关系图：

### 1. **ER关系图.puml** - 详细版ER图
- 📊 **完整的表结构**：包含所有字段和约束
- 🎨 **领域分色**：不同领域用不同颜色区分
- 🔗 **详细关系**：显示所有外键关系和索引
- 📝 **字段说明**：主键、外键、索引标注清晰

### 2. **简化ER关系图.puml** - 简化版ER图
- 🎯 **核心字段**：只显示主要字段
- 🔗 **关系清晰**：重点展示表间关系
- 📱 **移动友好**：适合小屏幕查看
- 💡 **易于理解**：适合业务人员查看

### 3. **领域架构图.puml** - 领域架构图
- 🏗️ **领域划分**：清晰展示5大业务领域
- 📊 **职责说明**：每个领域的职责和核心表
- 🔄 **数据流向**：展示领域间的数据流
- 📋 **设计原则**：说明设计理念

---

## 🛠️ 查看方法

### 方法一：在线查看器 (推荐)
1. **访问PlantUML在线编辑器**：
   ```
   http://www.plantuml.com/plantuml/uml/
   ```

2. **复制粘贴代码**：
   - 打开任意一个 `.puml` 文件
   - 复制全部内容
   - 粘贴到在线编辑器中
   - 点击"Submit"查看图形

3. **导出图片**：
   - 支持PNG、SVG、PDF等格式
   - 可以下载保存到本地

### 方法二：VS Code插件
1. **安装插件**：
   ```
   PlantUML (by jebbs)
   ```

2. **查看图形**：
   - 打开 `.puml` 文件
   - 按 `Alt + D` 预览图形
   - 或右键选择 "Preview Current PlantUML"

3. **导出图片**：
   - 按 `Ctrl + Shift + P`
   - 输入 "PlantUML: Export"
   - 选择导出格式

### 方法三：本地安装PlantUML
1. **安装Java**：
   ```bash
   # 确保Java 8+已安装
   java -version
   ```

2. **下载PlantUML**：
   ```bash
   # 下载plantuml.jar
   wget http://sourceforge.net/projects/plantuml/files/plantuml.jar/download
   ```

3. **生成图片**：
   ```bash
   # 生成PNG图片
   java -jar plantuml.jar ER关系图.puml
   
   # 生成SVG图片
   java -jar plantuml.jar -tsvg ER关系图.puml
   ```

---

## 📊 图形说明

### ER关系图符号说明

| 符号 | 含义 | 示例 |
|------|------|------|
| 🔑 **PK** | 主键 (Primary Key) | `* id : int <<PK>>` |
| 🔗 **FK** | 外键 (Foreign Key) | `* user_id : int <<FK>>` |
| 🔒 **UK** | 唯一键 (Unique Key) | `openid : varchar(32) <<UK>>` |
| 📊 **IDX** | 索引 (Index) | `city : varchar(20) <<IDX>>` |
| `\|\|--o{` | 一对多关系 | 一个用户有多个地址 |
| `\|\|--\|\|` | 一对一关系 | 用户与详情一对一 |
| `}o--o{` | 多对多关系 | 站点与分类多对多 |

### 领域颜色说明

| 颜色 | 领域 | 说明 |
|------|------|------|
| 🟢 **绿色** | 用户领域 | 用户相关的所有表 |
| 🟡 **黄色** | 产品领域 | 产品、分类、价格表 |
| 🟣 **紫色** | 站点领域 | 站点、服务、评价表 |
| 🔵 **蓝色** | 行为领域 | 用户行为记录表 |
| 🟠 **橙色** | 系统领域 | 系统配置表 |

---

## 🎯 使用建议

### 开发团队使用
1. **需求分析阶段**：
   - 使用 `领域架构图.puml` 理解业务领域
   - 确认领域划分是否合理

2. **数据库设计阶段**：
   - 使用 `ER关系图.puml` 查看完整表结构
   - 确认字段类型和约束

3. **开发实现阶段**：
   - 使用 `简化ER关系图.puml` 快速查看关系
   - 编写SQL时参考表关系

### 业务团队使用
1. **业务理解**：
   - 查看 `领域架构图.puml` 了解系统架构
   - 理解各领域的职责

2. **需求确认**：
   - 使用 `简化ER关系图.puml` 确认数据关系
   - 验证业务流程是否完整

### 运维团队使用
1. **数据库维护**：
   - 参考 `ER关系图.puml` 了解表结构
   - 制定备份和优化策略

2. **性能调优**：
   - 根据关系图分析查询路径
   - 优化索引和查询语句

---

## 🔧 自定义修改

### 修改颜色主题
```plantuml
' 修改领域颜色
package "用户领域" #你的颜色代码 {
  ' 表定义
}
```

### 添加新表
```plantuml
entity "新表名" as new_table {
  * id : int <<PK>>
  --
  字段名 : 类型
}
```

### 修改关系
```plantuml
' 一对多关系
table1 ||--o{ table2 : "关系说明"

' 一对一关系
table1 ||--|| table2 : "关系说明"

' 多对多关系
table1 }o--o{ table2 : "关系说明"
```

---

## 📱 移动端查看

### 手机查看建议
1. **使用简化版**：`简化ER关系图.puml` 更适合小屏幕
2. **横屏查看**：获得更好的显示效果
3. **缩放功能**：支持手势缩放查看细节

### 平板查看建议
1. **完整版图形**：可以查看 `ER关系图.puml` 完整版
2. **分屏显示**：一边看图一边看代码
3. **导出PDF**：便于离线查看和分享

---

## 🚀 进阶使用

### 生成文档
```bash
# 生成包含图形的Markdown文档
java -jar plantuml.jar -tpng *.puml
# 然后在Markdown中引用生成的PNG图片
```

### 集成到项目
```yaml
# 在CI/CD中自动生成图形
- name: Generate PlantUML diagrams
  run: |
    java -jar plantuml.jar -tsvg database/*.puml
    mv database/*.svg docs/images/
```

### 版本控制
- 将 `.puml` 文件加入Git版本控制
- 图片文件可以忽略，通过CI自动生成
- 在README中引用生成的图片

---

**📝 这些ER关系图将帮助您更好地理解和维护数据库结构，支持团队协作和项目文档化！**
