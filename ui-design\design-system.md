# 慢慢回收小程序 UI 设计系统

## 🎨 设计理念

### 核心设计原则
- **环保绿色**：体现环保回收的主题
- **简洁实用**：功能导向，减少视觉干扰
- **专业可信**：建立用户对回收服务的信任
- **易用友好**：符合用户操作习惯

### 设计风格
- **现代简约**：扁平化设计语言
- **卡片化布局**：信息模块化展示
- **图标化表达**：直观的视觉符号
- **渐变点缀**：增加视觉层次感

## 🎯 色彩系统

### 主色调
```css
/* 主绿色 - 环保主题 */
--primary-color: #52C41A;
--primary-light: #73D13D;
--primary-dark: #389E0D;
--primary-gradient: linear-gradient(135deg, #52C41A 0%, #73D13D 100%);

/* 辅助绿色 */
--success-color: #52C41A;
--success-light: #F6FFED;
--success-border: #B7EB8F;
```

### 辅助色彩
```css
/* 功能色彩 */
--info-color: #1890FF;
--info-light: #E6F7FF;
--warning-color: #FAAD14;
--warning-light: #FFF7E6;
--error-color: #FF4D4F;
--error-light: #FFF2F0;

/* 价格相关色彩 */
--price-up: #F5222D;      /* 价格上涨 */
--price-down: #52C41A;    /* 价格下跌 */
--price-stable: #8C8C8C;  /* 价格稳定 */
```

### 中性色彩
```css
/* 文字色彩 */
--text-primary: #262626;     /* 主要文字 */
--text-secondary: #595959;   /* 次要文字 */
--text-tertiary: #8C8C8C;    /* 辅助文字 */
--text-placeholder: #BFBFBF; /* 占位文字 */
--text-disabled: #D9D9D9;    /* 禁用文字 */

/* 背景色彩 */
--bg-primary: #FFFFFF;       /* 主背景 */
--bg-secondary: #FAFAFA;     /* 次背景 */
--bg-tertiary: #F5F5F5;      /* 三级背景 */
--bg-disabled: #F5F5F5;      /* 禁用背景 */

/* 边框色彩 */
--border-primary: #D9D9D9;   /* 主边框 */
--border-secondary: #E8E8E8; /* 次边框 */
--border-light: #F0F0F0;     /* 浅边框 */
```

## 📐 尺寸规范

### 字体大小
```css
--font-size-xs: 20rpx;    /* 辅助信息 */
--font-size-sm: 24rpx;    /* 次要文字 */
--font-size-md: 28rpx;    /* 正文 */
--font-size-lg: 32rpx;    /* 小标题 */
--font-size-xl: 36rpx;    /* 大标题 */
--font-size-xxl: 40rpx;   /* 特大标题 */
--font-size-huge: 48rpx;  /* 超大标题 */
```

### 间距系统
```css
--spacing-xs: 8rpx;
--spacing-sm: 12rpx;
--spacing-md: 16rpx;
--spacing-lg: 24rpx;
--spacing-xl: 32rpx;
--spacing-xxl: 48rpx;
--spacing-huge: 64rpx;
```

### 圆角规范
```css
--border-radius-xs: 4rpx;
--border-radius-sm: 6rpx;
--border-radius-md: 8rpx;
--border-radius-lg: 12rpx;
--border-radius-xl: 16rpx;
--border-radius-round: 50%;
```

### 阴影系统
```css
--shadow-sm: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
--shadow-md: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
--shadow-lg: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
--shadow-xl: 0 12rpx 32rpx rgba(0, 0, 0, 0.16);
```

## 🧩 组件规范

### 按钮组件
```css
/* 主要按钮 */
.btn-primary {
  background: var(--primary-gradient);
  color: #FFFFFF;
  border: none;
  border-radius: var(--border-radius-md);
  padding: 24rpx 48rpx;
  font-size: var(--font-size-md);
  font-weight: 500;
  box-shadow: var(--shadow-sm);
}

/* 次要按钮 */
.btn-secondary {
  background: #FFFFFF;
  color: var(--primary-color);
  border: 2rpx solid var(--primary-color);
  border-radius: var(--border-radius-md);
  padding: 22rpx 46rpx;
}

/* 文字按钮 */
.btn-text {
  background: transparent;
  color: var(--primary-color);
  border: none;
  padding: 12rpx 24rpx;
}
```

### 卡片组件
```css
.card {
  background: var(--bg-primary);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-md);
  box-shadow: var(--shadow-sm);
  border: 1rpx solid var(--border-light);
}

.card-hover {
  transition: all 0.3s ease;
}

.card-hover:active {
  transform: scale(0.98);
  box-shadow: var(--shadow-md);
}
```

### 标签组件
```css
.tag {
  display: inline-flex;
  align-items: center;
  padding: 8rpx 16rpx;
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-xs);
  font-weight: 500;
}

.tag-success {
  background: var(--success-light);
  color: var(--success-color);
  border: 1rpx solid var(--success-border);
}

.tag-info {
  background: var(--info-light);
  color: var(--info-color);
}

.tag-warning {
  background: var(--warning-light);
  color: var(--warning-color);
}
```

## 📱 页面布局规范

### 页面结构
```css
.page {
  min-height: 100vh;
  background: var(--bg-secondary);
}

.page-header {
  background: var(--bg-primary);
  padding: var(--spacing-md);
  border-bottom: 1rpx solid var(--border-light);
}

.page-content {
  padding: var(--spacing-md);
  padding-bottom: 120rpx; /* 为底部导航留空间 */
}

.page-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: var(--bg-primary);
  border-top: 1rpx solid var(--border-light);
}
```

### 网格系统
```css
.grid {
  display: grid;
  gap: var(--spacing-md);
}

.grid-2 {
  grid-template-columns: 1fr 1fr;
}

.grid-3 {
  grid-template-columns: repeat(3, 1fr);
}

.grid-4 {
  grid-template-columns: repeat(4, 1fr);
}
```

## 🎭 图标系统

### 图标规范
- **尺寸**：32rpx, 48rpx, 64rpx, 96rpx
- **风格**：线性图标，2rpx描边
- **色彩**：跟随文字色彩或使用主题色

### 常用图标
- 🏠 首页：home
- 📂 分类：category  
- 📈 行情：trending
- 👤 我的：profile
- 🗺️ 地图：map
- 💰 价格：price
- 📞 电话：phone
- 📍 位置：location
- ⭐ 收藏：favorite
- 🔍 搜索：search
