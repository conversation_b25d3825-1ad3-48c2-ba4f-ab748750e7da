import React, { memo, useEffect } from 'react'
import { shallowEqual, useDispatch, useSelector } from 'react-redux'


import { getSettleSingerAction } from '../store/actionCreators'
import { getSizeImage } from "../../../../utils/formatUtils";
import "./style.css";

export default memo(function SettleSinger() {
    // redux
    const dispatch = useDispatch();
    const state = useSelector((state: any) => ({
        settleSinger: state.getIn(["recommend", "settleSinger"])
    }), shallowEqual);

    // hooks
    useEffect(() => {
        dispatch(getSettleSingerAction());
    }, [dispatch])

    return (
        <div className="settleSinger">
            <div className="settleSingerHeader">
                <div className="settleSingerTitle">热门歌手</div>
                <a href="#/discover/artist" className="settleSingerMore">查看全部&gt;</a>
            </div>
            <div className="settleSingerList">
                {
                    state.settleSinger.map((item: any) => {
                        return (
                            <a href={`#/artist?id=${item.id}`} key={item.id} className="settleSingerItem">
                                <img src={getSizeImage(item.img1v1Url, 62)} alt="" />
                                <div className="settleSingerInfo">
                                    <div className="settleSingerInfoEnglishName textNowrap">{item.name} &nbsp;</div>
                                    <div className="settleSingerInfoDetails textNowrap"> 流行歌手 </div>
                                </div>
                            </a>
                        )
                    })
                }
            </div>
            <div className="applyfor">
                <a href="https://music.163.com/st/musician" target="_blank">申请成为网易音乐人</a>
            </div>
        </div>
    )
})