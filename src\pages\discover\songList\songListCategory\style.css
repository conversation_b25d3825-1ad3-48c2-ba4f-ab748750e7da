
.songListCategoryBox {
  position: absolute;
  z-index: 99;
  top: 55px;
  left: -25px;
  width: 700px;
  border: 1px solid #ccc;
  background-color: #fefefe;
  box-shadow: 0 0 10px 2px #d3d3d3;
  border-radius: 5px;
  padding-top: 10px;
}  

.songListCategoryBox .songListCategoryArrow {
    position: absolute;
    top: -11px;
    left: 110px;
    width: 24px;
    height: 11px;
    background: url(../../../../static/images/sprite_icon.png);
    background-position: -48px 0;
}

.songListCategoryBox .songListCategoryAll {
    padding: 10px 25px;
    border-bottom: 1px solid #e2e2e2;
}

.songListCategoryBox .songListCategoryAll a {
    display: inline-block;
    text-align: center;
    width: 75px;
    height: 26px;
    line-height: 26px;
    border: 1px solid #d3d3d3;
    border-radius: 3px;
    background-color: #fafafa;
}

.songListCategoryBox .songListCategory {
    padding-left: 25px;
}
.songListCategoryBox .songListCategory dl {
    display: flex;
    align-items: flex-start;
}

.songListCategoryBox .songListCategory dt {
    display: inline-flex;
    align-items: center;
    padding: 15px 0 0;
    width: 70px;
    text-align: center;
}
.songListCategoryBox .songListCategory dl.songListCategoryItem0 dt{
    padding: 10px 0 0;
}

.songListCategoryBox .songListCategory dt i {
    display: inline-block;
    width: 24px;
    height: 24px;
    background: url(../../../../static/images/sprite_icon2.png);
    background-position: -20px -735px;
    margin-right: 8px;
}
.songListCategoryBox .songListCategory dt span{
    font-weight: 700;
}
.songListCategoryBox .songListCategory dl.songListCategoryItem1 i{  
    background-position: 0 -60px;
}

.songListCategoryBox .songListCategory dl.songListCategoryItem2 i {
    background-position: 0 -88px;
}
    

.songListCategoryBox .songListCategory dl.songListCategoryItem3 i {
    background-position: 0 -117px;
}
  

.songListCategoryBox .songListCategory dl.songListCategoryItem4 i {
    background-position: 0 -141px;
}

.songListCategoryBox .songListCategory dl.songListCategoryItem4 dd {
    padding-bottom: 25px;
}

.songListCategoryBox .songListCategory dd {
    padding-top: 15px;
    padding-left: 15px;
    flex: 1;
    display: flex;
    flex-wrap: wrap;
    border-left: 1px solid #e2e2e2;
}
.songListCategoryBox .songListCategory dd .item {
    margin-bottom: 8px;
    cursor: pointer;
}

.songListCategoryBox .songListCategory dd a {
    color: #333;
}

.songListCategoryBox .songListCategory dd .songListCategoryDivider {
    margin: 0 12px;
    color: #e2e2e2;
}


  
