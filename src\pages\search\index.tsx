import React, { memo, useState, useEffect } from 'react'
import { searchCategories } from '../../common/localData'
import RouterConfig from '../../router'
import { Input } from 'antd'
import { NavLink, useSearchParams, Outlet } from 'react-router-dom'
import './style.css'
import NavBar from '../../components/navBar'

export default memo(function Search() {
  // props/state
  const [searchSongName, setSearchSongName] = useState('')// 搜索歌曲名字
  const [activeIndex, setActiveIndex] = useState(null)// 控制导航item的active
  const [searchParams] = useSearchParams();
  // other handle
  const { Search } = Input
  const song: any = searchParams.get('song')
  // other hooks
  // (组件渲染更新歌曲名字)
  useEffect(() => {
    setSearchSongName(song)
  }, [song])
  // (本次存储索引: NavLink选中状态的索引)
  useEffect(() => {
    // 判断本地存储是否包含key: activeIndex
    !localStorage.hasOwnProperty('activeIndex') &&
      localStorage.setItem('activeIndex', '0')
    const activeIndex = JSON.parse(localStorage.getItem('activeIndex')!)
    setActiveIndex(activeIndex)
  }, [])

  // 更新activeIndex索引时保存本地存储
  useEffect(() => {
    localStorage.setItem('activeIndex', JSON.stringify(activeIndex))
  }, [activeIndex])

  return (
    <div>
      <NavBar />
    <div className="width980 searchBox">
      <div className="search">
        <Search
          value={searchSongName}
          style={{ width: 490 }}
          onChange={(e: any) => { setSearchSongName(e.target.value);  }}
        />
      </div>
      <div className="searchContent">
        <div className="searchInfo">
          搜索"{song}", 找到
          <span className="searchAmount"> 500 </span>单曲
        </div>
        <div className="searchCategory">
          {searchCategories.map((item, index: any) => {
            return (
              <NavLink
                key={item.link}
                to={{ pathname: item.link + `&song=${song}` }}
                className={`searchRouteItem  ${activeIndex === index ? 'active' : ''}`}
                onClick={() => setActiveIndex(index)}
              >
                <em>{item.title}</em>
              </NavLink>
            )
          })}
        </div>
        <RouterConfig />
      </div>
      <Outlet />
    </div>
    </div>
  )
})
