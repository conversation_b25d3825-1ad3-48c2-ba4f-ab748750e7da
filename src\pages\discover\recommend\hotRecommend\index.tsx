import React, { memo, useEffect } from 'react'
import { shallowEqual, useDispatch, useSelector } from 'react-redux'
import { HOT_RECOMMEND_LIMIT } from '../../../../common/constants'
import ThemeHeaderRCM from '../../../../components/themeHeaderRCM'
import { getHotBannersAction } from '../store/actionCreators'
import SongCover from '../../../../components/songCover'
import { useNavigate } from 'react-router-dom'
import './style.css'

export default memo(function HotRecommend() {
    const navigate = useNavigate();
    const dispatch = useDispatch();
    const { hotRecommends } = useSelector(
        (state: any) => ({
            hotRecommends: state.getIn(['recommend', 'hotRecommends']),
        }),
        shallowEqual
    )
    useEffect(() => {
        dispatch(getHotBannersAction(HOT_RECOMMEND_LIMIT))
    }, [dispatch])

    const handleTitleClick = () => {
        navigate('/discover/songlist')
    }

    const handleKeyWordClick = (item: any) => {
        navigate(`/discover/songlist?cat=${item}`)
    }
    
    return (
        <div className="hotRecommend">
            <ThemeHeaderRCM
                title="热门推荐"
                titleClick={() => handleTitleClick()}
                keywords={['华语', '流行', '摇滚', '民谣', '电子']}
                keywordsClick={(item: any) => handleKeyWordClick(item)}
            />
            <div className="hotRecommendList">
                {hotRecommends &&
                    hotRecommends.map((item: any) => {
                        return (
                            <SongCover key={item.id} info={item} className="hotRecommendListItem">
                                {item.name}
                            </SongCover>
                        )
                    })}
            </div>
        </div>
    )
})
