import React, { memo, useEffect, useRef } from 'react';
import { shallowEqual, useDispatch, useSelector } from 'react-redux';
import propTypes from 'prop-types';
import Sortable from 'sortablejs';
import PlaylistItem from './playlistItem';
import { ClearOutlined, CloseOutlined, HeartOutlined } from '@ant-design/icons';
import {
  changePlaylistAndCount,
  getSongDetailAction,
  changePlayListAction,
  // changeSongIndexAction
} from '../store/actionCreators';
import LyricContent from './lyricContent';
import { removeAllSong, resetPlaylistId } from '../../../utils/localstorage';
import './style.css'

function SliderPlayList(props: any) {
  const {
    isShowSlider,
    playlistCount,
    closeWindow,
    playMusic,
    changeSong,
  } = props;

  const dispatch = useDispatch();
  const { currentSong, playList, currentSongIndex } = useSelector(
    (state: any) => ({
      currentSong: state.getIn(['player', 'currentSong']),
      playList: state.getIn(['player', 'playList']),
      currentSongIndex: state.getIn(['player', 'currentSongIndex']),
    }),
    shallowEqual
  );

  // other hook
  const playlistRef: any = useRef();
  console.log()
  // 歌曲列表拖拽初始化
  useEffect(() => {

    const el = playlistRef.current.querySelector('.sliderPlayListMain');
    new Sortable(el, {
      sort: true,
      animation: 200,
      onEnd: function (evt: any) {
        let tempPlayList = playList;
        const musicsId = []
        tempPlayList.splice(
          evt.newIndex,
          0,
          playList.splice(evt.oldIndex, 1)[0]
        );
        dispatch(changePlayListAction(tempPlayList));
        musicsId.push(...tempPlayList.map((item: any) => item.id))
        // 重置歌曲列数组
        resetPlaylistId(musicsId)
      },
    });
    

  }, [currentSongIndex, dispatch, playList, currentSong]);

  // 清除全部歌曲
  const clearAllPlaylist = () => {
    removeAllSong()
    playList.splice(0, playList.length);
    dispatch(changePlaylistAndCount(playList));
  };


  // 点击item播放音乐
  const clickItem = (index: any, item: any) => {
    dispatch(getSongDetailAction(item.id));
    playMusic();
  };

  return (
    <div
      className="sliderPlayListBox"
      style={{ visibility: isShowSlider ? 'visible' : 'hidden' }}
      onClick={(e) => e.stopPropagation()}>
      <div className="sliderPlayListHeader">
        <div className="sliderPlayListHeaderLeft">
          <h3 className="sliderPlayListHeaderTitle">播放列表({playlistCount})</h3>
          <div>
            <span className="sliderPlayListHeaderIcon">
              <HeartOutlined />
              <span>收藏</span>
            </span>
            <span onClick={() => clearAllPlaylist()} className="sliderPlayListHeaderIcon">
              <ClearOutlined />
              <span>清除播放列表</span>
            </span>
          </div>
        </div>
        <div className="sliderPlayListHeaderRight">
          <div className="sliderPlayListHeaderSongName">{currentSong.name}</div>
          <div className="sliderPlayListHeaderClose" onClick={closeWindow}>
            <CloseOutlined />
          </div>
        </div>
      </div>
      <div className="sliderPlayListMainBox" ref={playlistRef}>
        <div className="sliderPlayListMain">
          {playList &&
            playList.map((item: any, index: any) => {
              return (
                <PlaylistItem
                  key={item.id}
                  isActive={
                    (currentSongIndex ? currentSongIndex : 0) === index
                      ? 'active'
                      : ''
                  }
                  songName={item.name}
                  singer={item.ar[0].name}
                  duration={item.dt}
                  clickItem={() => clickItem(index, item)}
                  songId={item.id}
                  nextMusic={() => changeSong(1)}
                />
              );
            })}
        </div>
        <div className="sliderPlayListMainLine"></div>
        <LyricContent />
      </div>
    </div>
  );
}

SliderPlayList.propTypes = {
  isShowSlider: propTypes.bool.isRequired,
  playlistCount: propTypes.number.isRequired,
  closeWindow: propTypes.any,
  playMusic: propTypes.any,
  changeSong: propTypes.any,
  isPlaying: propTypes.any,
};

export default memo(SliderPlayList);
