package com.c2brecycle.service.impl;

import com.c2brecycle.entity.Category;
import com.c2brecycle.mapper.CategoryMapper;
import com.c2brecycle.service.CategoryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 分类服务实现类
 * 
 * <AUTHOR> Recycle Team
 * @since 2024-01-15
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CategoryServiceImpl implements CategoryService {

    private final CategoryMapper categoryMapper;

    @Override
    public List<Category> getAllCategories() {
        log.info("获取所有分类");
        return categoryMapper.selectAllEnabled();
    }

    @Override
    public Category getCategoryById(Long categoryId) {
        log.info("根据ID获取分类，categoryId: {}", categoryId);
        return categoryMapper.selectById(categoryId);
    }
}
