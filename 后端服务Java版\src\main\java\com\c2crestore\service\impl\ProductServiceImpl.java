package com.c2crestore.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.c2crestore.common.exception.BusinessException;
import com.c2crestore.common.result.ResultCode;
import com.c2crestore.dto.SearchProductDTO;
import com.c2crestore.entity.Category;
import com.c2crestore.mapper.FavoriteMapper;
import com.c2crestore.mapper.ProductMapper;
import com.c2crestore.service.CategoryService;
import com.c2crestore.service.ProductService;
import com.c2crestore.vo.ProductDetailVO;
import com.c2crestore.vo.ProductVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 产品服务实现类
 * 
 * <AUTHOR> Restore Team
 * @since 2024-01-15
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProductServiceImpl implements ProductService {

    private final ProductMapper productMapper;
    private final FavoriteMapper favoriteMapper;
    private final CategoryService categoryService;

    @Override
    public IPage<ProductVO> searchProducts(SearchProductDTO searchDTO) {
        log.info("搜索产品，关键词: {}, 分类: {}, 页码: {}, 大小: {}", 
                searchDTO.getKeyword(), searchDTO.getCategoryId(), searchDTO.getPage(), searchDTO.getSize());

        // 验证分类是否存在
        if (searchDTO.getCategoryId() != null) {
            Category category = categoryService.getCategoryById(searchDTO.getCategoryId());
            if (category == null) {
                throw new BusinessException(ResultCode.CATEGORY_NOT_FOUND);
            }
        }

        // 构建分页参数
        Page<ProductVO> page = new Page<>(searchDTO.getPage(), searchDTO.getSize());
        
        // 执行搜索
        IPage<ProductVO> result = productMapper.searchProducts(page, searchDTO.getKeyword(), searchDTO.getCategoryId());
        
        log.info("搜索产品完成，总数: {}, 当前页数量: {}", result.getTotal(), result.getRecords().size());
        return result;
    }

    @Override
    public ProductDetailVO getProductDetail(Long productId, Long userId) {
        log.info("获取产品详情，productId: {}, userId: {}", productId, userId);

        if (productId == null || productId <= 0) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "产品ID不能为空");
        }

        // 获取产品基本信息
        ProductVO productVO = productMapper.selectProductDetail(productId);
        if (productVO == null) {
            throw new BusinessException(ResultCode.PRODUCT_NOT_FOUND);
        }

        if (productVO.getStatus() != 1) {
            throw new BusinessException(ResultCode.PRODUCT_INACTIVE);
        }

        // 构建详情VO
        ProductDetailVO detailVO = new ProductDetailVO();
        detailVO.setId(productVO.getId());
        detailVO.setCategoryId(productVO.getCategoryId());
        detailVO.setCategoryName(productVO.getCategoryName());
        detailVO.setCategoryIcon(productVO.getCategoryIcon());
        detailVO.setName(productVO.getName());
        detailVO.setBrand(productVO.getBrand());
        detailVO.setModel(productVO.getModel());
        detailVO.setImage(productVO.getImage());
        detailVO.setKeywords(productVO.getKeywords());
        detailVO.setStatus(productVO.getStatus());
        detailVO.setCreatedAt(productVO.getCreatedAt());

        // 检查是否已收藏
        if (userId != null) {
            var favorite = favoriteMapper.selectByUserAndTarget(userId, 2, productId);
            detailVO.setIsFavorited(favorite != null);
        } else {
            detailVO.setIsFavorited(false);
        }

        // 获取价格信息
        List<Object> priceList = productMapper.selectProductPrices(productId, null, null);
        
        // 构建价格信息（这里简化处理，实际项目中需要更复杂的价格计算逻辑）
        ProductDetailVO.PriceInfoVO priceInfo = new ProductDetailVO.PriceInfoVO();
        priceInfo.setUnit("个");
        priceInfo.setUpdatedAt(java.time.LocalDateTime.now());
        detailVO.setPriceInfo(priceInfo);

        // 获取站点价格列表（简化处理）
        detailVO.setStationPrices(java.util.Collections.emptyList());

        log.info("获取产品详情成功，productName: {}", detailVO.getName());
        return detailVO;
    }

    @Override
    public List<ProductVO> getHotProducts(Long categoryId, Integer limit) {
        log.info("获取热门产品，分类: {}, 数量: {}", categoryId, limit);

        if (limit == null || limit <= 0) {
            limit = 10;
        }
        if (limit > 50) {
            limit = 50;
        }

        // 验证分类是否存在
        if (categoryId != null) {
            Category category = categoryService.getCategoryById(categoryId);
            if (category == null) {
                throw new BusinessException(ResultCode.CATEGORY_NOT_FOUND);
            }
        }

        List<ProductVO> hotProducts = productMapper.selectHotProducts(categoryId, limit);
        
        log.info("获取热门产品完成，数量: {}", hotProducts.size());
        return hotProducts;
    }

    @Override
    public Object getProductPrices(Long productId, Long stationId, String city) {
        log.info("获取产品价格，productId: {}, stationId: {}, city: {}", productId, stationId, city);

        if (productId == null || productId <= 0) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "产品ID不能为空");
        }

        // 验证产品是否存在
        ProductVO product = productMapper.selectProductDetail(productId);
        if (product == null) {
            throw new BusinessException(ResultCode.PRODUCT_NOT_FOUND);
        }

        // 获取价格信息
        List<Object> prices = productMapper.selectProductPrices(productId, stationId, city);
        
        log.info("获取产品价格完成，价格记录数: {}", prices.size());
        return prices;
    }
}
