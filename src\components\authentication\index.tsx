import React, { memo, useEffect } from 'react'
import { message, Skeleton } from 'antd'

type AuthType = {
  flag: boolean;
  to: Function;
  showModal: Function;
}

type AuthType2 = {
  flag: boolean;
  to: Function;
  showModal: Function;
}

function Auth(props: AuthType) {
  // props/state
  const { flag } = props

  // other hook
  useEffect(() => {
    // 没登录
    if (!flag) {
      message.loading('请先登录', 2).then(() => {
        props.to()
        props.showModal()
      })
    }
  }, [flag, props])

  return (
    <div style={{ display: !flag ? 'block' : 'none' }}>
      <Skeleton active  />
    </div>
  )
}


export default memo(Auth)
