<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>慢慢回收 - 专业的废品回收信息服务平台</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>🌿</text></svg>">
    <style>
        /* 基础样式重置 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            color: #333;
            line-height: 1.6;
        }

        /* 主容器 */
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        /* 项目头部 */
        .project-header {
            background: linear-gradient(135deg, #52C41A, #73D13D);
            color: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(82, 196, 26, 0.3);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 30px;
        }

        .logo-section {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .logo {
            font-size: 4em;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            width: 80px;
            height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .brand-info h1 {
            font-size: 2.5em;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .brand-info p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .project-stats {
            display: flex;
            gap: 30px;
        }

        .stat-item {
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }

        .stat-number {
            display: block;
            font-size: 2.5em;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 0.9em;
            opacity: 0.9;
        }

        /* 导航菜单 */
        .demo-nav {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-bottom: 30px;
            flex-wrap: wrap;
            background: white;
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .nav-btn {
            padding: 15px 25px;
            border: 2px solid #52C41A;
            background: white;
            color: #52C41A;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .nav-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #52C41A, #73D13D);
            transition: left 0.3s ease;
            z-index: -1;
        }

        .nav-btn:hover::before,
        .nav-btn.active::before {
            left: 0;
        }

        .nav-btn:hover,
        .nav-btn.active {
            color: white;
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(82, 196, 26, 0.3);
        }

        /* 页面容器 */
        .pages-container {
            display: flex;
            justify-content: center;
            min-height: 600px;
        }

        .page {
            display: none;
            width: 100%;
            justify-content: center;
            align-items: flex-start;
        }

        .page.active {
            display: flex;
        }

        /* 手机模拟器 */
        .phone-mockup {
            width: 375px;
            height: 812px;
            background: white;
            border-radius: 30px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
            overflow: hidden;
            position: relative;
            border: 8px solid #333;
            transform: scale(0.9);
            transition: all 0.3s ease;
        }

        .phone-mockup:hover {
            transform: scale(0.92);
            box-shadow: 0 25px 80px rgba(0, 0, 0, 0.25);
        }

        /* 动画效果 */
        @keyframes mapMove {
            0% { background-position: 0 0; }
            100% { background-position: 20px 20px; }
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-15px); }
            60% { transform: translateY(-8px); }
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .demo-container {
                padding: 10px;
            }

            .phone-mockup {
                width: 100%;
                max-width: 375px;
                height: 600px;
                transform: scale(1);
            }

            .phone-mockup:hover {
                transform: scale(1);
            }

            .header-content {
                flex-direction: column;
                text-align: center;
            }

            .project-stats {
                flex-direction: column;
                gap: 15px;
            }

            .demo-nav {
                gap: 8px;
                padding: 15px;
            }

            .nav-btn {
                padding: 10px 15px;
                font-size: 14px;
            }
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <!-- 项目介绍头部 -->
        <header class="project-header">
            <div class="header-content">
                <div class="logo-section">
                    <div class="logo">🌿</div>
                    <div class="brand-info">
                        <h1>慢慢回收</h1>
                        <p>专业的废品回收信息服务平台</p>
                    </div>
                </div>
                <div class="project-stats">
                    <div class="stat-item">
                        <span class="stat-number">12</span>
                        <span class="stat-label">功能模块</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">5</span>
                        <span class="stat-label">核心页面</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">1</span>
                        <span class="stat-label">AI核心功能</span>
                    </div>
                </div>
            </div>
        </header>

        <!-- 导航菜单 -->
        <nav class="demo-nav">
            <button class="nav-btn active" onclick="showPage('home')" data-page="home">
                🏠 首页地图
            </button>
            <button class="nav-btn" onclick="showPage('category')" data-page="category">
                📂 分类查询
            </button>
            <button class="nav-btn" onclick="showPage('ai-route')" data-page="ai-route">
                🤖 AI路线规划
            </button>
            <button class="nav-btn" onclick="showPage('market')" data-page="market">
                📈 价格行情
            </button>
            <button class="nav-btn" onclick="showPage('profile')" data-page="profile">
                👤 个人中心
            </button>
        </nav>

        <!-- 页面容器 -->
        <div class="pages-container">
            <!-- 首页 -->
            <div id="home" class="page active">
                <div class="phone-mockup">
                    <!-- 手机头部 -->
                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 15px 20px; background: white; border-bottom: 1px solid #f0f0f0; font-size: 14px;">
                        <span style="font-weight: 600; color: #333;">📍 北京市朝阳区</span>
                        <div style="display: flex; gap: 15px;">
                            <span style="color: #52C41A; cursor: pointer; padding: 5px 10px; border-radius: 15px; transition: all 0.3s ease;">🔍 搜索</span>
                            <span style="color: #52C41A; cursor: pointer; padding: 5px 10px; border-radius: 15px; transition: all 0.3s ease;">☰ 筛选</span>
                        </div>
                    </div>

                    <!-- 地图区域 -->
                    <div style="height: 300px; position: relative; background: linear-gradient(45deg, #e8f5e8 25%, #f0f9f0 25%, #f0f9f0 50%, #e8f5e8 50%, #e8f5e8 75%, #f0f9f0 75%); background-size: 20px 20px; animation: mapMove 20s linear infinite;">
                        <div style="width: 100%; height: 100%; position: relative; display: flex; align-items: center; justify-content: center;">
                            <div style="font-size: 20px; color: #52C41A; font-weight: 600; text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);">🗺️ 地图显示区域</div>
                            <div style="position: absolute; top: 30%; left: 20%; font-size: 24px; animation: bounce 2s infinite; cursor: pointer;" onclick="showToast('绿色回收站')">📍</div>
                            <div style="position: absolute; top: 50%; left: 60%; font-size: 24px; animation: bounce 2s infinite; cursor: pointer;" onclick="showToast('环保回收中心')">📍</div>
                            <div style="position: absolute; top: 70%; left: 40%; font-size: 24px; animation: bounce 2s infinite; cursor: pointer;" onclick="showToast('蓝天回收点')">📍</div>
                            <div style="position: absolute; top: 35%; left: 25%; background: white; padding: 6px 12px; border-radius: 15px; font-size: 12px; font-weight: 500; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); border: 2px solid #52C41A; color: #52C41A;">绿色回收站</div>
                            <div style="position: absolute; top: 55%; left: 65%; background: white; padding: 6px 12px; border-radius: 15px; font-size: 12px; font-weight: 500; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); border: 2px solid #52C41A; color: #52C41A;">环保回收中心</div>
                        </div>
                    </div>

                    <!-- 快捷功能区 -->
                    <div style="display: flex; padding: 20px; gap: 15px; background: #fafafa;">
                        <div style="flex: 1; background: white; border-radius: 15px; padding: 25px 15px; text-align: center; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08); cursor: pointer; transition: all 0.3s ease;" onclick="showPage('category')" onmouseover="this.style.transform='translateY(-5px)'" onmouseout="this.style.transform='translateY(0)'">
                            <div style="font-size: 28px; margin-bottom: 10px;">📱</div>
                            <div style="font-size: 14px; font-weight: 600;">快速查价</div>
                        </div>
                        <div style="flex: 1; background: linear-gradient(135deg, #52C41A, #73D13D); color: white; border-radius: 15px; padding: 25px 15px; text-align: center; box-shadow: 0 8px 25px rgba(82, 196, 26, 0.4); cursor: pointer; transition: all 0.3s ease;" onclick="showPage('ai-route')" onmouseover="this.style.transform='translateY(-5px) scale(1.05)'" onmouseout="this.style.transform='translateY(0) scale(1)'">
                            <div style="font-size: 28px; margin-bottom: 10px;">🤖</div>
                            <div style="font-size: 14px; font-weight: 600;">AI路线</div>
                        </div>
                        <div style="flex: 1; background: white; border-radius: 15px; padding: 25px 15px; text-align: center; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08); cursor: pointer; transition: all 0.3s ease;" onclick="showToast('回收指南功能')" onmouseover="this.style.transform='translateY(-5px)'" onmouseout="this.style.transform='translateY(0)'">
                            <div style="font-size: 28px; margin-bottom: 10px;">📚</div>
                            <div style="font-size: 14px; font-weight: 600;">回收指南</div>
                        </div>
                    </div>

                    <!-- 站点列表 -->
                    <div style="padding: 20px; padding-bottom: 100px; background: white;">
                        <h3 style="margin-bottom: 20px; font-size: 18px; color: #333; font-weight: 600;">📋 附近回收站点</h3>
                        <div style="background: white; border-radius: 15px; padding: 20px; margin-bottom: 15px; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08); border: 2px solid #f0f0f0; cursor: pointer; transition: all 0.3s ease;" onclick="showToast('查看绿色回收站详情')" onmouseover="this.style.transform='translateY(-3px)'; this.style.borderColor='#52C41A'" onmouseout="this.style.transform='translateY(0)'; this.style.borderColor='#f0f0f0'">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                                <span style="font-weight: 700; font-size: 16px; color: #333;">🏪 绿色回收站</span>
                                <div style="display: flex; gap: 12px; font-size: 14px;">
                                    <span style="color: #faad14; font-weight: 600;">⭐ 4.8</span>
                                    <span style="color: #52C41A; font-weight: 600;">📍 500m</span>
                                </div>
                            </div>
                            <div style="display: flex; justify-content: space-between; margin-bottom: 12px; font-size: 14px; color: #666;">
                                <div>📞 138****8888</div>
                                <div style="color: #52C41A; font-weight: 600;">🕐 营业中</div>
                            </div>
                            <div style="display: flex; gap: 8px; flex-wrap: wrap;">
                                <span style="background: linear-gradient(135deg, #f6ffed, #e6f7ff); color: #52C41A; padding: 6px 12px; border-radius: 15px; font-size: 12px; font-weight: 500; border: 1px solid #b7eb8f;">💰 电脑配件</span>
                                <span style="background: linear-gradient(135deg, #f6ffed, #e6f7ff); color: #52C41A; padding: 6px 12px; border-radius: 15px; font-size: 12px; font-weight: 500; border: 1px solid #b7eb8f;">🔩 金属</span>
                                <span style="background: linear-gradient(135deg, #f6ffed, #e6f7ff); color: #52C41A; padding: 6px 12px; border-radius: 15px; font-size: 12px; font-weight: 500; border: 1px solid #b7eb8f;">📄 纸类</span>
                            </div>
                        </div>

                        <div style="background: white; border-radius: 15px; padding: 20px; margin-bottom: 15px; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08); border: 2px solid #f0f0f0; cursor: pointer; transition: all 0.3s ease;" onclick="showToast('查看环保回收中心详情')" onmouseover="this.style.transform='translateY(-3px)'; this.style.borderColor='#52C41A'" onmouseout="this.style.transform='translateY(0)'; this.style.borderColor='#f0f0f0'">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                                <span style="font-weight: 700; font-size: 16px; color: #333;">🏪 环保回收中心</span>
                                <div style="display: flex; gap: 12px; font-size: 14px;">
                                    <span style="color: #faad14; font-weight: 600;">⭐ 4.5</span>
                                    <span style="color: #52C41A; font-weight: 600;">📍 800m</span>
                                </div>
                            </div>
                            <div style="display: flex; justify-content: space-between; margin-bottom: 12px; font-size: 14px; color: #666;">
                                <div>📞 139****9999</div>
                                <div style="color: #52C41A; font-weight: 600;">🕐 营业中</div>
                            </div>
                            <div style="display: flex; gap: 8px; flex-wrap: wrap;">
                                <span style="background: linear-gradient(135deg, #f6ffed, #e6f7ff); color: #52C41A; padding: 6px 12px; border-radius: 15px; font-size: 12px; font-weight: 500; border: 1px solid #b7eb8f;">📱 手机数码</span>
                                <span style="background: linear-gradient(135deg, #f6ffed, #e6f7ff); color: #52C41A; padding: 6px 12px; border-radius: 15px; font-size: 12px; font-weight: 500; border: 1px solid #b7eb8f;">🔩 金属类</span>
                                <span style="background: linear-gradient(135deg, #f6ffed, #e6f7ff); color: #52C41A; padding: 6px 12px; border-radius: 15px; font-size: 12px; font-weight: 500; border: 1px solid #b7eb8f;">🥤 塑料</span>
                            </div>
                        </div>
                    </div>

                    <!-- 底部导航 -->
                    <div style="position: absolute; bottom: 0; left: 0; right: 0; background: white; border-top: 1px solid #f0f0f0; display: flex; padding: 10px 0; backdrop-filter: blur(10px);">
                        <div style="flex: 1; text-align: center; padding: 10px; cursor: pointer; color: #52C41A; background: #f6ffed; border-radius: 15px; margin: 0 5px;" onclick="showPage('home')">
                            <div style="font-size: 22px; margin-bottom: 4px;">🏠</div>
                            <div style="font-size: 12px; font-weight: 500;">首页</div>
                        </div>
                        <div style="flex: 1; text-align: center; padding: 10px; cursor: pointer; border-radius: 15px; margin: 0 5px; transition: all 0.3s ease;" onclick="showPage('category')" onmouseover="this.style.background='#f6ffed'" onmouseout="this.style.background='transparent'">
                            <div style="font-size: 22px; margin-bottom: 4px;">📂</div>
                            <div style="font-size: 12px; font-weight: 500;">分类</div>
                        </div>
                        <div style="flex: 1; text-align: center; padding: 10px; cursor: pointer; border-radius: 15px; margin: 0 5px; transition: all 0.3s ease;" onclick="showPage('market')" onmouseover="this.style.background='#f6ffed'" onmouseout="this.style.background='transparent'">
                            <div style="font-size: 22px; margin-bottom: 4px;">📈</div>
                            <div style="font-size: 12px; font-weight: 500;">行情</div>
                        </div>
                        <div style="flex: 1; text-align: center; padding: 10px; cursor: pointer; border-radius: 15px; margin: 0 5px; transition: all 0.3s ease;" onclick="showPage('profile')" onmouseover="this.style.background='#f6ffed'" onmouseout="this.style.background='transparent'">
                            <div style="font-size: 22px; margin-bottom: 4px;">👤</div>
                            <div style="font-size: 12px; font-weight: 500;">我的</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentPage = 'home';
        let isAnimating = false;

        // 页面切换功能
        function showPage(pageId) {
            if (isAnimating) return;

            isAnimating = true;

            // 隐藏所有页面
            const pages = document.querySelectorAll('.page');
            const currentPageElement = document.querySelector('.page.active');

            if (currentPageElement) {
                currentPageElement.style.opacity = '0';
                currentPageElement.style.transform = 'translateX(-50px)';

                setTimeout(() => {
                    currentPageElement.classList.remove('active');

                    // 显示目标页面
                    const targetPage = document.getElementById(pageId);
                    if (targetPage) {
                        targetPage.classList.add('active');
                        targetPage.style.opacity = '0';
                        targetPage.style.transform = 'translateX(50px)';

                        setTimeout(() => {
                            targetPage.style.opacity = '1';
                            targetPage.style.transform = 'translateX(0)';
                            isAnimating = false;
                        }, 50);
                    }
                }, 300);
            } else {
                // 首次加载
                const targetPage = document.getElementById(pageId);
                if (targetPage) {
                    targetPage.classList.add('active');
                    isAnimating = false;
                }
            }

            // 更新导航按钮状态
            updateNavButtons(pageId);

            // 更新当前页面
            currentPage = pageId;

            // 显示页面切换提示
            showToast(`切换到${getPageName(pageId)}`);
        }

        // 更新顶部导航按钮状态
        function updateNavButtons(activePageId) {
            const navBtns = document.querySelectorAll('.nav-btn');
            navBtns.forEach(btn => {
                btn.classList.remove('active');
                if (btn.getAttribute('data-page') === activePageId) {
                    btn.classList.add('active');
                }
            });
        }

        // 获取页面名称
        function getPageName(pageId) {
            const pageNames = {
                'home': '首页地图',
                'category': '分类查询',
                'ai-route': 'AI路线规划',
                'market': '价格行情',
                'profile': '个人中心'
            };
            return pageNames[pageId] || '未知页面';
        }

        // 显示提示消息
        function showToast(message, type = 'info') {
            // 移除已存在的toast
            const existingToast = document.querySelector('.toast');
            if (existingToast) {
                existingToast.remove();
            }

            // 创建新的toast
            const toast = document.createElement('div');
            toast.className = `toast toast-${type}`;
            toast.textContent = message;

            // 设置样式
            Object.assign(toast.style, {
                position: 'fixed',
                top: '20px',
                left: '50%',
                transform: 'translateX(-50%)',
                background: type === 'success' ? '#52C41A' :
                           type === 'error' ? '#ff4d4f' :
                           type === 'warning' ? '#faad14' : '#1890ff',
                color: 'white',
                padding: '12px 24px',
                borderRadius: '25px',
                fontSize: '14px',
                fontWeight: '500',
                zIndex: '10000',
                boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
                animation: 'toastSlideIn 0.3s ease-out'
            });

            // 添加动画样式
            if (!document.querySelector('#toast-styles')) {
                const style = document.createElement('style');
                style.id = 'toast-styles';
                style.textContent = `
                    @keyframes toastSlideIn {
                        from {
                            opacity: 0;
                            transform: translateX(-50%) translateY(-20px);
                        }
                        to {
                            opacity: 1;
                            transform: translateX(-50%) translateY(0);
                        }
                    }
                    @keyframes toastSlideOut {
                        from {
                            opacity: 1;
                            transform: translateX(-50%) translateY(0);
                        }
                        to {
                            opacity: 0;
                            transform: translateX(-50%) translateY(-20px);
                        }
                    }
                `;
                document.head.appendChild(style);
            }

            // 添加到页面
            document.body.appendChild(toast);

            // 3秒后移除
            setTimeout(() => {
                toast.style.animation = 'toastSlideOut 0.3s ease-in';
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.parentNode.removeChild(toast);
                    }
                }, 300);
            }, 3000);
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 显示欢迎消息
            setTimeout(() => {
                showToast('欢迎使用慢慢回收！🌿', 'success');
            }, 1000);

            // 统计数字动画
            setTimeout(() => {
                animateNumbers();
            }, 1500);
        });

        // 统计数字动画
        function animateNumbers() {
            const statNumbers = document.querySelectorAll('.stat-number');

            statNumbers.forEach(stat => {
                const finalValue = stat.textContent;
                const numericValue = parseInt(finalValue);

                if (!isNaN(numericValue)) {
                    let currentValue = 0;
                    const increment = numericValue / 50;
                    const timer = setInterval(() => {
                        currentValue += increment;
                        if (currentValue >= numericValue) {
                            stat.textContent = finalValue;
                            clearInterval(timer);
                        } else {
                            stat.textContent = Math.floor(currentValue);
                        }
                    }, 30);
                }
            });
        }

        // 键盘导航
        document.addEventListener('keydown', function(event) {
            const pages = ['home', 'category', 'ai-route', 'market', 'profile'];
            const currentIndex = pages.indexOf(currentPage);

            if (event.key === 'ArrowLeft' && currentIndex > 0) {
                showPage(pages[currentIndex - 1]);
            } else if (event.key === 'ArrowRight' && currentIndex < pages.length - 1) {
                showPage(pages[currentIndex + 1]);
            } else if (event.key === 'Escape') {
                if (currentPage !== 'home') {
                    showPage('home');
                }
            }
        });

        // 触摸手势支持
        let touchStartX = 0;
        let touchEndX = 0;

        document.addEventListener('touchstart', function(event) {
            touchStartX = event.changedTouches[0].screenX;
        });

        document.addEventListener('touchend', function(event) {
            touchEndX = event.changedTouches[0].screenX;
            handleSwipe();
        });

        function handleSwipe() {
            const swipeThreshold = 50;
            const diff = touchStartX - touchEndX;

            if (Math.abs(diff) > swipeThreshold) {
                const pages = ['home', 'category', 'ai-route', 'market', 'profile'];
                const currentIndex = pages.indexOf(currentPage);

                if (diff > 0 && currentIndex < pages.length - 1) {
                    // 向左滑动，显示下一页
                    showPage(pages[currentIndex + 1]);
                } else if (diff < 0 && currentIndex > 0) {
                    // 向右滑动，显示上一页
                    showPage(pages[currentIndex - 1]);
                }
            }
        }
    </script>
</body>
</html>