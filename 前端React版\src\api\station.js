import request from '../utils/request';

/**
 * 站点相关API
 */

// 获取附近站点
export const getNearbyStations = (params) => {
  return request.get('/stations/nearby', { params });
};

// 搜索站点
export const searchStations = (params) => {
  return request.get('/stations/search', { params });
};

// 获取站点详情
export const getStationDetail = (id) => {
  return request.get(`/stations/${id}`);
};

// 获取热门站点
export const getHotStations = (params) => {
  return request.get('/stations/hot', { params });
};
