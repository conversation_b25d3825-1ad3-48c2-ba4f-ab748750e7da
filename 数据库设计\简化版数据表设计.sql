-- =============================================
-- 慢慢回收小程序 简化版数据库设计
-- 版本: V2.0
-- 创建日期: 2024-01-15
-- 说明: 按领域划分的简化数据表结构
-- =============================================

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- =============================================
-- 用户领域 (User Domain)
-- =============================================

-- 用户表
CREATE TABLE `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `openid` varchar(32) NOT NULL COMMENT '微信openid',
  `nickname` varchar(20) DEFAULT NULL COMMENT '昵称',
  `avatar` varchar(200) DEFAULT NULL COMMENT '头像',
  `phone` varchar(11) DEFAULT NULL COMMENT '手机号',
  `city` varchar(20) DEFAULT NULL COMMENT '城市',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态 0-禁用 1-正常',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_openid` (`openid`),
  KEY `idx_city` (`city`)
) ENGINE=InnoDB COMMENT='用户表';

-- 用户地址表
CREATE TABLE `user_addresses` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '地址ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `name` varchar(20) NOT NULL COMMENT '联系人',
  `phone` varchar(11) NOT NULL COMMENT '电话',
  `address` varchar(100) NOT NULL COMMENT '地址',
  `lng` decimal(9,6) DEFAULT NULL COMMENT '经度',
  `lat` decimal(9,6) DEFAULT NULL COMMENT '纬度',
  `is_default` tinyint(1) DEFAULT 0 COMMENT '默认地址',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB COMMENT='用户地址表';

-- =============================================
-- 产品领域 (Product Domain)
-- =============================================

-- 产品分类表
CREATE TABLE `categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `parent_id` int(11) DEFAULT 0 COMMENT '父分类ID',
  `name` varchar(20) NOT NULL COMMENT '分类名称',
  `icon` varchar(10) DEFAULT NULL COMMENT '图标',
  `sort` int(11) DEFAULT 0 COMMENT '排序',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态',
  PRIMARY KEY (`id`),
  KEY `idx_parent_id` (`parent_id`)
) ENGINE=InnoDB COMMENT='产品分类表';

-- 产品表
CREATE TABLE `products` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '产品ID',
  `category_id` int(11) NOT NULL COMMENT '分类ID',
  `name` varchar(50) NOT NULL COMMENT '产品名称',
  `brand` varchar(20) DEFAULT NULL COMMENT '品牌',
  `model` varchar(30) DEFAULT NULL COMMENT '型号',
  `image` varchar(200) DEFAULT NULL COMMENT '图片',
  `keywords` varchar(100) DEFAULT NULL COMMENT '关键词',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_name` (`name`),
  FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`)
) ENGINE=InnoDB COMMENT='产品表';

-- 产品价格表
CREATE TABLE `prices` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '价格ID',
  `product_id` int(11) NOT NULL COMMENT '产品ID',
  `station_id` int(11) DEFAULT NULL COMMENT '站点ID',
  `min_price` decimal(8,2) NOT NULL COMMENT '最低价',
  `max_price` decimal(8,2) NOT NULL COMMENT '最高价',
  `unit` varchar(10) NOT NULL COMMENT '单位',
  `date` date NOT NULL COMMENT '日期',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_station_id` (`station_id`),
  KEY `idx_date` (`date`),
  FOREIGN KEY (`product_id`) REFERENCES `products` (`id`)
) ENGINE=InnoDB COMMENT='产品价格表';

-- =============================================
-- 站点领域 (Station Domain)
-- =============================================

-- 回收站点表
CREATE TABLE `stations` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '站点ID',
  `name` varchar(30) NOT NULL COMMENT '站点名称',
  `phone` varchar(11) NOT NULL COMMENT '电话',
  `address` varchar(100) NOT NULL COMMENT '地址',
  `lng` decimal(9,6) NOT NULL COMMENT '经度',
  `lat` decimal(9,6) NOT NULL COMMENT '纬度',
  `city` varchar(20) NOT NULL COMMENT '城市',
  `hours` varchar(50) DEFAULT NULL COMMENT '营业时间',
  `image` varchar(200) DEFAULT NULL COMMENT '图片',
  `rating` decimal(3,2) DEFAULT 0.00 COMMENT '评分',
  `review_count` int(11) DEFAULT 0 COMMENT '评价数',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态 0-停业 1-营业',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_location` (`lng`,`lat`),
  KEY `idx_city` (`city`),
  KEY `idx_rating` (`rating`)
) ENGINE=InnoDB COMMENT='回收站点表';

-- 站点服务表
CREATE TABLE `station_services` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '服务ID',
  `station_id` int(11) NOT NULL COMMENT '站点ID',
  `category_id` int(11) NOT NULL COMMENT '分类ID',
  `min_price` decimal(8,2) DEFAULT NULL COMMENT '最低价',
  `max_price` decimal(8,2) DEFAULT NULL COMMENT '最高价',
  `unit` varchar(10) DEFAULT NULL COMMENT '单位',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态',
  PRIMARY KEY (`id`),
  KEY `idx_station_id` (`station_id`),
  KEY `idx_category_id` (`category_id`),
  FOREIGN KEY (`station_id`) REFERENCES `stations` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`)
) ENGINE=InnoDB COMMENT='站点服务表';

-- 站点评价表
CREATE TABLE `reviews` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '评价ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `station_id` int(11) NOT NULL COMMENT '站点ID',
  `rating` tinyint(1) NOT NULL COMMENT '评分 1-5',
  `content` varchar(200) DEFAULT NULL COMMENT '内容',
  `images` json DEFAULT NULL COMMENT '图片',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_station_id` (`station_id`),
  FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`station_id`) REFERENCES `stations` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB COMMENT='站点评价表';

-- =============================================
-- 行为领域 (Behavior Domain)
-- =============================================

-- 搜索记录表
CREATE TABLE `search_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `keyword` varchar(50) NOT NULL COMMENT '关键词',
  `type` tinyint(1) NOT NULL COMMENT '类型 1-产品 2-站点',
  `result_count` int(11) DEFAULT 0 COMMENT '结果数',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_keyword` (`keyword`),
  FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB COMMENT='搜索记录表';

-- 收藏表
CREATE TABLE `favorites` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '收藏ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `type` tinyint(1) NOT NULL COMMENT '类型 1-站点 2-产品',
  `target_id` int(11) NOT NULL COMMENT '目标ID',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_type_target` (`user_id`,`type`,`target_id`),
  FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB COMMENT='收藏表';

-- 浏览记录表
CREATE TABLE `browse_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `type` tinyint(1) NOT NULL COMMENT '类型 1-站点 2-产品',
  `target_id` int(11) NOT NULL COMMENT '目标ID',
  `duration` int(11) DEFAULT 0 COMMENT '时长(秒)',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB COMMENT='浏览记录表';

-- =============================================
-- 系统领域 (System Domain)
-- =============================================

-- 系统配置表
CREATE TABLE `configs` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `key` varchar(50) NOT NULL COMMENT '配置键',
  `value` text COMMENT '配置值',
  `desc` varchar(100) DEFAULT NULL COMMENT '描述',
  `type` varchar(20) DEFAULT 'string' COMMENT '类型',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态',
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_key` (`key`)
) ENGINE=InnoDB COMMENT='系统配置表';

-- =============================================
-- 初始化数据
-- =============================================

-- 插入分类数据
INSERT INTO `categories` (`id`, `parent_id`, `name`, `icon`, `sort`) VALUES
(1, 0, '电子产品', '💻', 1),
(2, 0, '金属类', '🔩', 2),
(3, 0, '纸类', '📄', 3),
(4, 0, '塑料类', '🥤', 4),
(5, 1, '电脑配件', '💻', 11),
(6, 1, '手机数码', '📱', 12),
(7, 1, '家用电器', '📺', 13),
(8, 2, '废铜', '🔩', 21),
(9, 2, '废铝', '🔩', 22),
(10, 2, '废铁', '🔩', 23);

-- 插入系统配置
INSERT INTO `configs` (`key`, `value`, `desc`) VALUES
('app_name', '慢慢回收', '应用名称'),
('app_version', '1.0.0', '应用版本'),
('default_city', '北京市', '默认城市'),
('search_radius', '10', '搜索半径(km)'),
('price_update_hours', '24', '价格更新间隔(小时)');

SET FOREIGN_KEY_CHECKS = 1;
