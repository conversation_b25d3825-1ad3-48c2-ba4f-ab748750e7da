.topBannerBox {
    width: 100%;
    height: 270px;
}

.topBannerBox .topBanner {
    position: relative;
    display: flex;
    height: 100%;
    justify-content: space-between;
    position: relative;
}

.topBannerLeft {
    width: 730px;
}
.topBannerLeft img {
    width: 100%;
}

.topBannerRight a{
    display: inline-block;
    width: 250px;
    background: url(../../../../static/images/download.png);
    height: 100%;
    text-align: center;
}
.topBannerRight a:hover{
    text-decoration: none;
}

.topBannerControl {
    position: absolute;
    left: 0;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
}
.topBannerControl .topBannerButton {
    position: absolute;
    width: 37px;
    height: 63px;
    background-image: url(../../../../static/images/banner_sprite.png);
    background-color: transparent;
    cursor: pointer;
}
.topBannerControl .topBannerButton:hover {
    background-color: rgba(0, 0, 0, 0.1);
    }
.topBannerControl .topBannerButton:nth-child(1) {
    left: -68px;
    background-position: 0 -360px;
}
.topBannerControl .topBannerButton:nth-child(2) {
    right: -68px;
    background-position: 0 -508px;
}

.left {
    left: -68px;
    background-position: 0 -360px;
}

.right {
    right: -68px;
    background-position: 0 -508px;
}

