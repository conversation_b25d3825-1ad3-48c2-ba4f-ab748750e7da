package com.c2crestore.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.c2crestore.common.exception.BusinessException;
import com.c2crestore.common.result.ResultCode;
import com.c2crestore.dto.AddressDTO;
import com.c2crestore.dto.FavoriteDTO;
import com.c2crestore.dto.UpdateUserDTO;
import com.c2crestore.entity.Favorite;
import com.c2crestore.entity.User;
import com.c2crestore.entity.UserAddress;
import com.c2crestore.mapper.FavoriteMapper;
import com.c2crestore.mapper.UserAddressMapper;
import com.c2crestore.mapper.UserMapper;
import com.c2crestore.service.UserService;
import com.c2crestore.vo.FavoriteVO;
import com.c2crestore.vo.UserProfileVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户服务实现类
 * 
 * <AUTHOR> Restore Team
 * @since 2024-01-15
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserServiceImpl implements UserService {

    private final UserMapper userMapper;
    private final UserAddressMapper userAddressMapper;
    private final FavoriteMapper favoriteMapper;

    @Override
    public UserProfileVO getUserProfile(Long userId) {
        log.info("获取用户信息，userId: {}", userId);

        User user = userMapper.selectById(userId);
        if (user == null || user.getDeleted() == 1) {
            throw new BusinessException(ResultCode.USER_NOT_FOUND);
        }

        if (user.getStatus() != 1) {
            throw new BusinessException(ResultCode.USER_INACTIVE);
        }

        // 构建用户信息VO
        UserProfileVO profileVO = new UserProfileVO();
        profileVO.setId(user.getId());
        profileVO.setNickname(user.getNickname());
        profileVO.setAvatar(user.getAvatar());
        profileVO.setPhone(user.getPhone());
        profileVO.setCity(user.getCity());
        profileVO.setStatus(user.getStatus());
        profileVO.setCreatedAt(user.getCreatedAt());
        profileVO.setUpdatedAt(user.getUpdatedAt());

        // 获取地址数量
        int addressCount = userAddressMapper.countByUserId(userId);
        profileVO.setAddressCount(addressCount);

        // 获取收藏数量（简化处理）
        profileVO.setFavoriteCount(0);

        log.info("获取用户信息成功，nickname: {}", user.getNickname());
        return profileVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateUserProfile(Long userId, UpdateUserDTO updateDTO) {
        log.info("更新用户信息，userId: {}", userId);

        User user = userMapper.selectById(userId);
        if (user == null || user.getDeleted() == 1) {
            throw new BusinessException(ResultCode.USER_NOT_FOUND);
        }

        if (user.getStatus() != 1) {
            throw new BusinessException(ResultCode.USER_INACTIVE);
        }

        boolean needUpdate = false;

        // 更新昵称
        if (StrUtil.isNotBlank(updateDTO.getNickname()) && 
            !updateDTO.getNickname().equals(user.getNickname())) {
            user.setNickname(updateDTO.getNickname());
            needUpdate = true;
        }

        // 更新头像
        if (StrUtil.isNotBlank(updateDTO.getAvatar()) && 
            !updateDTO.getAvatar().equals(user.getAvatar())) {
            user.setAvatar(updateDTO.getAvatar());
            needUpdate = true;
        }

        // 更新手机号
        if (StrUtil.isNotBlank(updateDTO.getPhone()) && 
            !updateDTO.getPhone().equals(user.getPhone())) {
            // 检查手机号是否已被使用
            User existUser = userMapper.selectByPhone(updateDTO.getPhone());
            if (existUser != null && !existUser.getId().equals(userId)) {
                throw new BusinessException(ResultCode.DUPLICATE_ERROR, "手机号已被使用");
            }
            user.setPhone(updateDTO.getPhone());
            needUpdate = true;
        }

        // 更新城市
        if (StrUtil.isNotBlank(updateDTO.getCity()) && 
            !updateDTO.getCity().equals(user.getCity())) {
            user.setCity(updateDTO.getCity());
            needUpdate = true;
        }

        if (needUpdate) {
            user.setUpdatedAt(LocalDateTime.now());
            userMapper.updateById(user);
            log.info("用户信息更新成功，userId: {}", userId);
        } else {
            log.info("用户信息无变化，无需更新，userId: {}", userId);
        }
    }

    @Override
    public List<UserAddress> getUserAddresses(Long userId) {
        log.info("获取用户地址列表，userId: {}", userId);

        // 验证用户是否存在
        User user = userMapper.selectById(userId);
        if (user == null || user.getDeleted() == 1) {
            throw new BusinessException(ResultCode.USER_NOT_FOUND);
        }

        List<UserAddress> addresses = userAddressMapper.selectByUserId(userId);
        
        log.info("获取用户地址列表成功，userId: {}, 数量: {}", userId, addresses.size());
        return addresses;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addUserAddress(Long userId, AddressDTO addressDTO) {
        log.info("添加用户地址，userId: {}", userId);

        // 验证用户是否存在
        User user = userMapper.selectById(userId);
        if (user == null || user.getDeleted() == 1) {
            throw new BusinessException(ResultCode.USER_NOT_FOUND);
        }

        // 检查地址数量限制
        int addressCount = userAddressMapper.countByUserId(userId);
        if (addressCount >= 10) {
            throw new BusinessException(ResultCode.ADDRESS_LIMIT_EXCEEDED);
        }

        // 如果设置为默认地址，先清除其他默认地址
        if (addressDTO.getIsDefault() != null && addressDTO.getIsDefault()) {
            userAddressMapper.clearDefaultByUserId(userId);
        }

        // 创建地址
        UserAddress address = new UserAddress();
        address.setUserId(userId);
        address.setContact(addressDTO.getContact());
        address.setPhone(addressDTO.getPhone());
        address.setProvince(addressDTO.getProvince());
        address.setCity(addressDTO.getCity());
        address.setDistrict(addressDTO.getDistrict());
        address.setAddress(addressDTO.getAddress());
        address.setLng(addressDTO.getLng());
        address.setLat(addressDTO.getLat());
        address.setIsDefault(addressDTO.getIsDefault() != null && addressDTO.getIsDefault() ? 1 : 0);
        address.setCreatedAt(LocalDateTime.now());

        userAddressMapper.insert(address);
        
        log.info("添加用户地址成功，userId: {}, addressId: {}", userId, address.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateUserAddress(Long userId, Long addressId, AddressDTO addressDTO) {
        log.info("更新用户地址，userId: {}, addressId: {}", userId, addressId);

        // 验证地址是否存在且属于该用户
        UserAddress address = userAddressMapper.selectById(addressId);
        if (address == null || address.getDeleted() == 1 || !address.getUserId().equals(userId)) {
            throw new BusinessException(ResultCode.ADDRESS_NOT_FOUND);
        }

        // 如果设置为默认地址，先清除其他默认地址
        if (addressDTO.getIsDefault() != null && addressDTO.getIsDefault()) {
            userAddressMapper.clearDefaultByUserId(userId);
        }

        // 更新地址信息
        address.setContact(addressDTO.getContact());
        address.setPhone(addressDTO.getPhone());
        address.setProvince(addressDTO.getProvince());
        address.setCity(addressDTO.getCity());
        address.setDistrict(addressDTO.getDistrict());
        address.setAddress(addressDTO.getAddress());
        address.setLng(addressDTO.getLng());
        address.setLat(addressDTO.getLat());
        address.setIsDefault(addressDTO.getIsDefault() != null && addressDTO.getIsDefault() ? 1 : 0);

        userAddressMapper.updateById(address);
        
        log.info("更新用户地址成功，userId: {}, addressId: {}", userId, addressId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteUserAddress(Long userId, Long addressId) {
        log.info("删除用户地址，userId: {}, addressId: {}", userId, addressId);

        // 验证地址是否存在且属于该用户
        UserAddress address = userAddressMapper.selectById(addressId);
        if (address == null || address.getDeleted() == 1 || !address.getUserId().equals(userId)) {
            throw new BusinessException(ResultCode.ADDRESS_NOT_FOUND);
        }

        // 逻辑删除地址
        userAddressMapper.deleteById(addressId);
        
        log.info("删除用户地址成功，userId: {}, addressId: {}", userId, addressId);
    }

    @Override
    public IPage<FavoriteVO> getUserFavorites(Long userId, Integer type, Integer page, Integer size) {
        log.info("获取用户收藏，userId: {}, type: {}, page: {}, size: {}", userId, type, page, size);

        // 验证用户是否存在
        User user = userMapper.selectById(userId);
        if (user == null || user.getDeleted() == 1) {
            throw new BusinessException(ResultCode.USER_NOT_FOUND);
        }

        // 构建分页参数
        Page<FavoriteVO> pageParam = new Page<>(page, size);
        
        // 查询收藏列表
        IPage<FavoriteVO> result = favoriteMapper.selectByUserId(pageParam, userId, type);
        
        log.info("获取用户收藏成功，userId: {}, 总数: {}, 当前页数量: {}", 
                userId, result.getTotal(), result.getRecords().size());
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addFavorite(Long userId, FavoriteDTO favoriteDTO) {
        log.info("添加收藏，userId: {}, type: {}, targetId: {}", 
                userId, favoriteDTO.getType(), favoriteDTO.getTargetId());

        // 验证用户是否存在
        User user = userMapper.selectById(userId);
        if (user == null || user.getDeleted() == 1) {
            throw new BusinessException(ResultCode.USER_NOT_FOUND);
        }

        // 检查是否已收藏
        Favorite existFavorite = favoriteMapper.selectByUserAndTarget(
                userId, favoriteDTO.getType(), favoriteDTO.getTargetId());
        if (existFavorite != null) {
            throw new BusinessException(ResultCode.FAVORITE_EXISTS);
        }

        // 创建收藏记录
        Favorite favorite = new Favorite();
        favorite.setUserId(userId);
        favorite.setType(favoriteDTO.getType());
        favorite.setTargetId(favoriteDTO.getTargetId());
        favorite.setCreatedAt(LocalDateTime.now());

        favoriteMapper.insert(favorite);
        
        log.info("添加收藏成功，userId: {}, favoriteId: {}", userId, favorite.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeFavorite(Long userId, Long favoriteId) {
        log.info("取消收藏，userId: {}, favoriteId: {}", userId, favoriteId);

        // 验证收藏是否存在且属于该用户
        Favorite favorite = favoriteMapper.selectById(favoriteId);
        if (favorite == null || favorite.getDeleted() == 1 || !favorite.getUserId().equals(userId)) {
            throw new BusinessException(ResultCode.FAVORITE_NOT_FOUND);
        }

        // 逻辑删除收藏
        favoriteMapper.deleteById(favoriteId);
        
        log.info("取消收藏成功，userId: {}, favoriteId: {}", userId, favoriteId);
    }
}
