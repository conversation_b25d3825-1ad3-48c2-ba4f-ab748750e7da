package com.c2brecycle.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 站点详情VO
 * 
 * <AUTHOR> Recycle Team
 * @since 2024-01-15
 */
@Data
@ApiModel("站点详情信息")
public class StationDetailVO {

    @ApiModelProperty("站点ID")
    private Long id;

    @ApiModelProperty("站点名称")
    private String name;

    @ApiModelProperty("电话")
    private String phone;

    @ApiModelProperty("地址")
    private String address;

    @ApiModelProperty("经度")
    private BigDecimal lng;

    @ApiModelProperty("纬度")
    private BigDecimal lat;

    @ApiModelProperty("城市")
    private String city;

    @ApiModelProperty("营业时间")
    private String hours;

    @ApiModelProperty("图片")
    private String image;

    @ApiModelProperty("评分")
    private BigDecimal rating;

    @ApiModelProperty("评价数")
    private Integer reviewCount;

    @ApiModelProperty("状态 0-停业 1-营业")
    private Integer status;

    @ApiModelProperty("距离(km)")
    private BigDecimal distance;

    @ApiModelProperty("是否已收藏")
    private Boolean isFavorited;

    @ApiModelProperty("服务分类列表")
    private List<ServiceCategoryVO> serviceCategories;

    @ApiModelProperty("最新价格列表")
    private List<LatestPriceVO> latestPrices;

    @ApiModelProperty("创建时间")
    private LocalDateTime createdAt;

    /**
     * 服务分类VO
     */
    @Data
    @ApiModel("服务分类信息")
    public static class ServiceCategoryVO {
        
        @ApiModelProperty("分类ID")
        private Long categoryId;
        
        @ApiModelProperty("分类名称")
        private String categoryName;
        
        @ApiModelProperty("分类图标")
        private String categoryIcon;
        
        @ApiModelProperty("产品数量")
        private Integer productCount;
    }

    /**
     * 最新价格VO
     */
    @Data
    @ApiModel("最新价格信息")
    public static class LatestPriceVO {
        
        @ApiModelProperty("产品ID")
        private Long productId;
        
        @ApiModelProperty("产品名称")
        private String productName;
        
        @ApiModelProperty("产品图片")
        private String productImage;
        
        @ApiModelProperty("最低价格")
        private BigDecimal minPrice;
        
        @ApiModelProperty("最高价格")
        private BigDecimal maxPrice;
        
        @ApiModelProperty("单位")
        private String unit;
        
        @ApiModelProperty("更新时间")
        private LocalDateTime updatedAt;
    }
}
