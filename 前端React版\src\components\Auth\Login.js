import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate, useLocation } from 'react-router-dom';
import { Form, Input, Button, Card, message, Divider } from 'antd';
import { UserOutlined, LockOutlined, WechatOutlined } from '@ant-design/icons';
import { wechatLogin, mockLogin } from '../../store/actions/authActions';
import './Login.css';

const Login = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  const { loading } = useSelector(state => state.auth);
  
  const [form] = Form.useForm();
  const [loginType, setLoginType] = useState('mock'); // 'wechat' | 'mock'

  // 获取重定向路径
  const from = location.state?.from?.pathname || '/';

  // 微信登录
  const handleWechatLogin = async () => {
    try {
      // 在实际应用中，这里应该调用微信SDK获取code
      const mockWechatData = {
        code: 'mock_wechat_code_' + Date.now(),
        nickname: '微信用户',
        avatar: 'https://via.placeholder.com/100',
        city: '北京市'
      };
      
      await dispatch(wechatLogin(mockWechatData));
      message.success('登录成功！');
      navigate(from, { replace: true });
    } catch (error) {
      message.error('微信登录失败：' + error.message);
    }
  };

  // 模拟登录
  const handleMockLogin = async (values) => {
    try {
      await dispatch(mockLogin(values));
      message.success('登录成功！');
      navigate(from, { replace: true });
    } catch (error) {
      message.error('登录失败：' + error.message);
    }
  };

  return (
    <div className="login-container">
      <Card className="login-card" title="C2B回收平台">
        <div className="login-tabs">
          <Button 
            type={loginType === 'wechat' ? 'primary' : 'default'}
            onClick={() => setLoginType('wechat')}
            style={{ marginRight: 8 }}
          >
            微信登录
          </Button>
          <Button 
            type={loginType === 'mock' ? 'primary' : 'default'}
            onClick={() => setLoginType('mock')}
          >
            模拟登录
          </Button>
        </div>

        <Divider />

        {loginType === 'wechat' ? (
          <div className="wechat-login">
            <div className="wechat-login-content">
              <WechatOutlined className="wechat-icon" />
              <p>点击下方按钮使用微信登录</p>
              <Button 
                type="primary" 
                size="large" 
                icon={<WechatOutlined />}
                loading={loading}
                onClick={handleWechatLogin}
                block
              >
                微信登录
              </Button>
            </div>
          </div>
        ) : (
          <Form
            form={form}
            name="mockLogin"
            onFinish={handleMockLogin}
            autoComplete="off"
            layout="vertical"
          >
            <Form.Item
              label="昵称"
              name="nickname"
              rules={[
                { required: true, message: '请输入昵称!' },
                { min: 2, max: 20, message: '昵称长度为2-20个字符!' }
              ]}
            >
              <Input 
                prefix={<UserOutlined />} 
                placeholder="请输入昵称" 
                size="large"
              />
            </Form.Item>

            <Form.Item
              label="手机号"
              name="phone"
              rules={[
                { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号!' }
              ]}
            >
              <Input 
                placeholder="请输入手机号（可选）" 
                size="large"
              />
            </Form.Item>

            <Form.Item
              label="城市"
              name="city"
            >
              <Input 
                placeholder="请输入所在城市（可选）" 
                size="large"
              />
            </Form.Item>

            <Form.Item
              label="头像URL"
              name="avatar"
            >
              <Input 
                placeholder="请输入头像URL（可选）" 
                size="large"
              />
            </Form.Item>

            <Form.Item>
              <Button 
                type="primary" 
                htmlType="submit" 
                loading={loading}
                size="large"
                block
              >
                登录
              </Button>
            </Form.Item>
          </Form>
        )}

        <div className="login-footer">
          <p>登录即表示同意《用户协议》和《隐私政策》</p>
        </div>
      </Card>
    </div>
  );
};

export default Login;
