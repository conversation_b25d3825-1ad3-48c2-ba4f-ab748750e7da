import { DownOutlined, SearchOutlined } from '@ant-design/icons';
import { headerLinks } from '../../common/localData';
import { Dropdown, Input, Menu } from 'antd';
import './style.css'
import { shallowEqual, useDispatch, useSelector } from 'react-redux';
import { clearLoginState } from '../../utils/loginUtils';
import { changeIsVisible } from '../themeLogin/store';
import ThemeLogin from '../themeLogin';
import { memo, useCallback, useEffect, useRef, useState } from 'react';
import { NavLink, Navigate } from 'react-router-dom';

import { debounce } from '../../utils/formatUtils';
import { getSearchSongListAction, changeFocusStateAction } from './store/actionCreators';
import { getSongDetailAction } from '../playerBar/store';
import { MusicDetail } from '../../models/music';



export default memo (function AppHeader() {
    type HeaderItemType = {
        title: string;
        link: string;
    }

    const [isNavigate, setIsNavigate] = useState(false);
    
    const [value, setValue] = useState('');

    const [recordActive, setRecordActive] = useState(-1)
    
    // redux hook
    const dispatch = useDispatch();


    const { searchSongList, focusState, isLogin, profile } = useSelector(
        (state: any) => ({
            searchSongList: state.getIn(['themeHeader', 'searchSongList']),
            focusState: state.getIn(['themeHeader', 'focusState']),
            isLogin: state.getIn(['loginState', 'isLogin']),
            profile: state.getIn(['loginState', 'profile']),
        }),
        shallowEqual
    );


    const showSelectItem = (item: HeaderItemType, index: number) => {
        if (index < 3) {
            return (
                <NavLink key={item.title} to={item.link} 
                className="appHeaderItem">
                
                    <em>{item.title}</em>
                    <i className="appHeaderItemIcon"></i>
                
                </NavLink>
            );
        } else {
            return (
                <a href={item.link} key={item.title} className="appHeaderItem">
                    {item.title}
                </a>
            );
        }
    };


    const inputRef: any = useRef<HTMLInputElement>();
    // (根据当前焦点状态设置input焦点)
    useEffect(() => {
        // 获取焦点
        if (focusState) inputRef.current.focus();
        // 失去焦点
        else inputRef.current.blur();
    }, [focusState]);

    
    const changeInput = (target: any) => {
        let value = target.value.trim();
        if (value.length < 1) return;
        // 显示下拉框
        dispatch(changeFocusStateAction(true));
        // 发送网络请求
        dispatch(getSearchSongListAction(value));
    };
    
    // 点击当前item歌曲项
    const changeCurrentSong = (id: number, item: MusicDetail) => {
        // 放到搜索文本框
        setValue(item.name + '-' + item.artists[0].name);
        //派发action
        dispatch(getSongDetailAction(id));
        // 隐藏下拉框
        dispatch(changeFocusStateAction(false));
        // 播放音乐
        (document.getElementById('audio') as any).autoplay = true;
    };

    // 表单回车:跳转到搜索详情
    const handleEnter = useCallback(
        () => {
            // 说明当前光标有”高亮当前行“
            if (recordActive >= 0) {
                // 保存value
                setValue(
                    searchSongList[recordActive].name +
                    '-' +
                    searchSongList[recordActive].artists[0].name
                );
            }
            dispatch(changeFocusStateAction(false));
            // 只要在搜索框回车: 都进行跳转
            if(value.trim().length >= 1)
                setIsNavigate(true);
            setTimeout(() => { setIsNavigate(false) }, 300)
        },
        [dispatch, recordActive, searchSongList]
    );
    
    // 获取焦点
    const handleFocus = useCallback(() => {
        // 当文本获取焦点时,文本被选中状态
        inputRef.current.select();
        // 更改为获取焦点状态
        dispatch(changeFocusStateAction(true));
        // 修改状态重定向状态
        setIsNavigate(false);
    }, [dispatch]);


    // 监控用户是否按: "上"或"下"键
    const watchKeyboard = useCallback(
        (even) => {
            let activeNumber = recordActive;
            if (even.keyCode === 38) {
                activeNumber--;
                activeNumber =
                    activeNumber < 0 ? searchSongList?.length - 1 : activeNumber;
                setRecordActive(activeNumber);
            } else if (even.keyCode === 40) {
                activeNumber++;
                activeNumber =
                    activeNumber >= searchSongList?.length ? 0 : activeNumber;
                setRecordActive(activeNumber);
            }
        },
        [recordActive, setRecordActive, searchSongList]
    );



    const profileDwonMenu: any = () => {
        return (
            isLogin ? (
                <Menu>
                    <Menu.Item key='headerUser'>
                        <a rel="noopener noreferrer" href="#/user"> 我的主页  </a>
                    </Menu.Item>
                    <Menu.Item key='headerExit' danger onClick={() => clearLoginState()}>退出登录</Menu.Item>
                </Menu>
            ) : ''
        );
    };

    const showProfileContent = () => {
        return (
            <div>
                <img src={profile.avatarUrl} alt="" className="appHeaderProfileImg" />
                <DownOutlined/>
            </div>
        )
    }

    return (
        <div className='appHeaderBox'>
            <div className="appHeaderContent width1100">
                <div className='appHeaderLeft'>
                    <h1>
                        <a href="#/" className="appHeaderLogo"></a>
                    </h1>
                    <div className="appHeaderGroup">
                        {headerLinks.map((item, index) => {
                            return showSelectItem(item, index);
                        })}
                    </div>
                </div>

                <div className="appHeaderRight">
                    <div className="appHeaderSearchBox">
                        <Input
                            ref={inputRef}
                            className="appHeaderSearch"
                            placeholder="音乐/歌手"
                            size="large"
                            prefix={<SearchOutlined />}
                            onChange={(e) => { setIsNavigate(false); setValue(e.target.value) }}
                            onInput={({ target }) => debounce(changeInput(target), 400)}
                            onFocus={handleFocus}
                            onPressEnter={() => handleEnter()}
                            value={value}
                            onKeyDown={watchKeyboard}
                        />

                        {isNavigate && (<Navigate  to={{
                                pathname: '/search/single',
                                search: `?song=${value}&type=1`,
                            }}
                            />)}
                        <div className="appHeaderSearchDownSlider"  style={{ display: focusState ? 'block' : 'none' }}>
                            <div className="appHeaderSearchHeader">
                                <span className="appHeaderSearchDiscover">搜"歌曲"相关用户&gt;</span>
                            </div>
                            <div className="appHeaderSearchContent">
                                <div className="appHeaderSearchContentLeft">
                                    <span className="appHeaderSearchSong">单曲</span>
                                </div>
                                <span className="appHeaderSearchContentMain">
                                    {searchSongList &&
                                        searchSongList.map((item: MusicDetail, index: number) => {
                                            return (
                                                <div
                                                    className={
                                                        'appHeaderSearchContentMainItem ' + (recordActive === index ? 'active' : '')
                                                    }
                                                    key={item.id}
                                                    onClick={() => changeCurrentSong(item.id, item)}
                                                >
                                                    <span>{item.name}</span>-{item.artists[0].name}
                                                </div>
                                            );
                                        })}
                                </span>
                            </div>
                        </div>
                    </div>
                    <div className="appHeaderCenter">创作者中心</div>
                    <Dropdown overlay={profileDwonMenu}>
                        <div className="appHeaderLogin" onClick={() => !isLogin && dispatch(changeIsVisible(true))}>
                            {isLogin ? showProfileContent() : "登录"}
                        </div>
                    </Dropdown>
                </div>
            </div>
            <div className="appHeaderRedLine"></div>
            <ThemeLogin />
        </div >
    );
})