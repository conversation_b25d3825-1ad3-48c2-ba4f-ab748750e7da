import React, { memo } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import propTypes from 'prop-types'
import { formatDate, getPlayUrl } from '../../../../utils/handleData'
import { removeSongId } from '../../../../utils/localstorage'
import { changePlaylistAndCount } from '../../store/actionCreators'
import { DownloadOutlined, DeleteOutlined, GithubOutlined, LikeOutlined } from '@ant-design/icons'
import './style.css'
function PlayListItem(props: any) {
  // props/state
  const { songName, singer, duration, isActive, clickItem, songId, nextMusic } = props

  // redux hook
  const dispatch = useDispatch()


  const { playList } = useSelector((state: any) => ({
    playList: state.getIn(['player', 'playList'])
  }))

  // other function
  // 清除当前播放音乐
  const clearCurrentSong = (e: any) => {
    // 从当前播放列表删除此音乐,然后派发action
    e.stopPropagation()
    // 移除歌曲
    removeSongId(songId)

    const currentSongIndex1 = playList.findIndex((song: any) => song.id === songId)

    const currentSongIndex = playList.findIndex((song: any) => song.id === songId)
    
    if (playList.length === 1) return
    
    playList.splice(currentSongIndex, 1)
    dispatch(changePlaylistAndCount(playList))

    dispatch(changePlaylistAndCount(playList))
    // 切换下一首音乐
    nextMusic()
  
  }

  return (
    <div className={isActive + " " + "playListItemBox"} onClick={clickItem}>
      
        <div className="playListItemSongName">{songName}</div>
        <div className="playListItemControlSinger">
          <LikeOutlined />
          <GithubOutlined />
          <DownloadOutlined onClick={() => window.open(getPlayUrl(songId))} />
          <DeleteOutlined onClick={(e) => clearCurrentSong(e)} />
          <span>{singer}</span>
        </div>
        <div className="playListItemDuration">{formatDate(duration, 'mm:ss')}</div>
   
    </div>
  )
}

PlayListItem.propTypes = {
  songName: propTypes.string.isRequired,
  singer: propTypes.string.isRequired,
  duration: propTypes.any.isRequired,
  isActive: propTypes.string,
  clickItem: propTypes.any,
  songId: propTypes.any,
  nextMusic: propTypes.any
}

export default memo(PlayListItem)