-- =============================================
-- 慢慢回收简化版示例数据插入脚本
-- 版本: V2.0
-- 创建日期: 2024-01-15
-- 说明: 简化版数据表的测试数据
-- =============================================

SET NAMES utf8mb4;

-- =============================================
-- 1. 用户领域数据
-- =============================================

-- 插入用户数据
INSERT INTO `users` (`openid`, `nickname`, `avatar`, `phone`, `city`, `status`) VALUES
('wx001', '环保达人', 'https://img.com/1.jpg', '13800138001', '北京市', 1),
('wx002', '绿色生活', 'https://img.com/2.jpg', '13800138002', '上海市', 1),
('wx003', '回收专家', 'https://img.com/3.jpg', '13800138003', '深圳市', 1),
('wx004', '废品收集', 'https://img.com/4.jpg', '13800138004', '杭州市', 1),
('wx005', '环保志愿', 'https://img.com/5.jpg', '13800138005', '成都市', 1);

-- 插入用户地址数据
INSERT INTO `user_addresses` (`user_id`, `name`, `phone`, `address`, `lng`, `lat`, `is_default`) VALUES
(1, '张三', '13800138001', '朝阳区建国门外大街1号', 116.407400, 39.904200, 1),
(1, '张三', '13800138001', '海淀区中关村大街27号', 116.311200, 39.999100, 0),
(2, '李四', '13800138002', '浦东新区陆家嘴环路1000号', 121.473700, 31.230400, 1),
(3, '王五', '13800138003', '南山区深南大道10000号', 113.930600, 22.532900, 1),
(4, '赵六', '13800138004', '西湖区文三路259号', 120.155100, 30.274100, 1);

-- =============================================
-- 2. 产品领域数据
-- =============================================

-- 插入产品数据
INSERT INTO `products` (`category_id`, `name`, `brand`, `model`, `image`, `keywords`) VALUES
(5, 'Intel处理器', 'Intel', 'i7-12700K', 'https://img.com/cpu1.jpg', 'Intel CPU i7'),
(5, 'NVIDIA显卡', 'NVIDIA', 'RTX 3080', 'https://img.com/gpu1.jpg', 'NVIDIA 显卡 RTX'),
(5, '金士顿内存', 'Kingston', 'DDR4-16GB', 'https://img.com/ram1.jpg', '金士顿 内存 DDR4'),
(6, 'iPhone手机', 'Apple', 'iPhone 13 Pro', 'https://img.com/ip1.jpg', 'iPhone 苹果 手机'),
(6, '华为手机', 'Huawei', 'Mate 40 Pro', 'https://img.com/hw1.jpg', '华为 手机 Mate'),
(6, '小米平板', 'Xiaomi', 'Mi Pad 5', 'https://img.com/mi1.jpg', '小米 平板'),
(7, '海尔冰箱', 'Haier', 'BCD-470L', 'https://img.com/hr1.jpg', '海尔 冰箱'),
(7, '格力空调', 'Gree', 'KFR-35GW', 'https://img.com/gl1.jpg', '格力 空调'),
(8, '废铜线', '通用', '电线电缆', 'https://img.com/cu1.jpg', '废铜 铜线'),
(9, '废铝制品', '通用', '铝合金', 'https://img.com/al1.jpg', '废铝 铝合金');

-- 插入价格数据
INSERT INTO `prices` (`product_id`, `station_id`, `min_price`, `max_price`, `unit`, `date`) VALUES
-- Intel处理器价格
(1, 1, 800.00, 1200.00, '台', '2024-01-15'),
(1, 2, 850.00, 1250.00, '台', '2024-01-15'),
(1, NULL, 825.00, 1225.00, '台', '2024-01-15'),

-- iPhone价格
(4, 1, 3000.00, 4500.00, '台', '2024-01-15'),
(4, 2, 3100.00, 4600.00, '台', '2024-01-15'),
(4, NULL, 3050.00, 4550.00, '台', '2024-01-15'),

-- 废铜价格
(9, 1, 45.00, 50.00, 'kg', '2024-01-15'),
(9, 4, 46.00, 51.00, 'kg', '2024-01-15'),
(9, NULL, 45.50, 50.50, 'kg', '2024-01-15'),

-- 废铝价格
(10, 1, 12.00, 15.00, 'kg', '2024-01-15'),
(10, 4, 12.50, 15.50, 'kg', '2024-01-15'),
(10, NULL, 12.25, 15.25, 'kg', '2024-01-15');

-- =============================================
-- 3. 站点领域数据
-- =============================================

-- 插入站点数据
INSERT INTO `stations` (`name`, `phone`, `address`, `lng`, `lat`, `city`, `hours`, `image`, `rating`, `review_count`, `status`) VALUES
('绿色回收站', '01012345678', '朝阳区建国门外大街100号', 116.407400, 39.904200, '北京市', '08:00-18:00', 'https://img.com/s1.jpg', 4.80, 156, 1),
('环保回收中心', '01087654321', '海淀区中关村大街200号', 116.311200, 39.999100, '北京市', '09:00-17:00', 'https://img.com/s2.jpg', 4.50, 89, 1),
('蓝天回收点', '01011223344', '朝阳区三里屯路300号', 116.455100, 39.928900, '北京市', '08:30-19:00', 'https://img.com/s3.jpg', 4.20, 67, 1),
('金属回收站', '02112345678', '浦东新区陆家嘴环路500号', 121.473700, 31.230400, '上海市', '07:00-16:00', 'https://img.com/s4.jpg', 4.60, 134, 1),
('智能回收站', '075512345678', '南山区深南大道1000号', 113.930600, 22.532900, '深圳市', '24小时', 'https://img.com/s5.jpg', 4.90, 203, 1);

-- 插入站点服务数据
INSERT INTO `station_services` (`station_id`, `category_id`, `min_price`, `max_price`, `unit`, `status`) VALUES
-- 绿色回收站服务
(1, 5, 50.00, 1500.00, '台', 1),
(1, 6, 100.00, 5000.00, '台', 1),
(1, 8, 40.00, 55.00, 'kg', 1),
(1, 9, 10.00, 18.00, 'kg', 1),

-- 环保回收中心服务
(2, 5, 80.00, 1600.00, '台', 1),
(2, 6, 150.00, 5200.00, '台', 1),
(2, 7, 200.00, 2000.00, '台', 1),

-- 蓝天回收点服务
(3, 3, 1.50, 3.50, 'kg', 1),
(3, 4, 0.80, 2.20, 'kg', 1),
(3, 9, 11.00, 16.00, 'kg', 1),

-- 金属回收站服务
(4, 8, 42.00, 52.00, 'kg', 1),
(4, 9, 11.50, 17.00, 'kg', 1),
(4, 10, 2.50, 4.50, 'kg', 1),

-- 智能回收站服务
(5, 5, 60.00, 1400.00, '台', 1),
(5, 6, 120.00, 4800.00, '台', 1),
(5, 3, 1.80, 3.20, 'kg', 1),
(5, 4, 1.00, 2.00, 'kg', 1);

-- 插入评价数据
INSERT INTO `reviews` (`user_id`, `station_id`, `rating`, `content`, `images`, `status`) VALUES
(1, 1, 5, '服务很好，价格公道！', '["https://img.com/r1.jpg"]', 1),
(2, 1, 4, '回收价格还可以，排队时间长', NULL, 1),
(3, 2, 5, '上门回收很方便，师傅专业', '["https://img.com/r2.jpg"]', 1),
(4, 3, 4, '分类做得好，环保意识强', NULL, 1),
(5, 4, 5, '专业金属回收，价格透明', NULL, 1),
(1, 5, 5, '24小时营业太方便了', '["https://img.com/r3.jpg"]', 1),
(2, 2, 3, '价格一般，服务需改进', NULL, 1),
(3, 1, 4, '总体不错，停车位紧张', NULL, 1);

-- =============================================
-- 4. 行为领域数据
-- =============================================

-- 插入搜索记录
INSERT INTO `search_logs` (`user_id`, `keyword`, `type`, `result_count`) VALUES
(1, 'iPhone', 1, 5),
(1, '回收站', 2, 8),
(2, '笔记本', 1, 12),
(2, '废铜价格', 1, 3),
(3, '显卡', 1, 7),
(3, '朝阳区', 2, 6),
(4, '冰箱', 1, 4),
(5, '手机回收', 1, 9);

-- 插入收藏记录
INSERT INTO `favorites` (`user_id`, `type`, `target_id`) VALUES
(1, 1, 1), -- 收藏绿色回收站
(1, 1, 2), -- 收藏环保回收中心
(1, 2, 4), -- 收藏iPhone产品
(2, 1, 4), -- 收藏金属回收站
(2, 2, 1), -- 收藏Intel处理器
(3, 1, 1), -- 收藏绿色回收站
(3, 2, 2), -- 收藏NVIDIA显卡
(4, 1, 3), -- 收藏蓝天回收点
(5, 1, 5); -- 收藏智能回收站

-- 插入浏览记录
INSERT INTO `browse_logs` (`user_id`, `type`, `target_id`, `duration`) VALUES
(1, 1, 1, 120), -- 浏览绿色回收站2分钟
(1, 2, 4, 45),  -- 浏览iPhone产品45秒
(2, 1, 4, 180), -- 浏览金属回收站3分钟
(2, 2, 1, 60),  -- 浏览Intel处理器1分钟
(3, 1, 1, 90),  -- 浏览绿色回收站1.5分钟
(3, 2, 2, 75),  -- 浏览NVIDIA显卡1分15秒
(4, 1, 3, 150), -- 浏览蓝天回收点2.5分钟
(5, 1, 5, 200); -- 浏览智能回收站3分20秒

-- =============================================
-- 5. 更新站点评分统计
-- =============================================

-- 更新各站点的评分和评价数
UPDATE `stations` s SET 
    s.rating = (SELECT ROUND(AVG(r.rating), 2) FROM `reviews` r WHERE r.station_id = s.id AND r.status = 1),
    s.review_count = (SELECT COUNT(*) FROM `reviews` r WHERE r.station_id = s.id AND r.status = 1);

-- =============================================
-- 6. 查询验证数据
-- =============================================

-- 验证数据插入结果
SELECT '用户数据' as '表名', COUNT(*) as '记录数' FROM users
UNION ALL
SELECT '地址数据', COUNT(*) FROM user_addresses
UNION ALL
SELECT '产品数据', COUNT(*) FROM products
UNION ALL
SELECT '价格数据', COUNT(*) FROM prices
UNION ALL
SELECT '站点数据', COUNT(*) FROM stations
UNION ALL
SELECT '服务数据', COUNT(*) FROM station_services
UNION ALL
SELECT '评价数据', COUNT(*) FROM reviews
UNION ALL
SELECT '搜索记录', COUNT(*) FROM search_logs
UNION ALL
SELECT '收藏记录', COUNT(*) FROM favorites
UNION ALL
SELECT '浏览记录', COUNT(*) FROM browse_logs;

-- 查看站点评分更新结果
SELECT id, name, rating, review_count FROM stations ORDER BY rating DESC;

-- 查看热门搜索关键词
SELECT keyword, COUNT(*) as search_count 
FROM search_logs 
GROUP BY keyword 
ORDER BY search_count DESC 
LIMIT 5;

-- 查看用户收藏统计
SELECT u.nickname, COUNT(f.id) as favorite_count
FROM users u
LEFT JOIN favorites f ON u.id = f.user_id
GROUP BY u.id, u.nickname
ORDER BY favorite_count DESC;

SELECT '示例数据插入完成！' as message;
