import { ManOutlined, PlayCircleOutlined, WomanOutlined } from '@ant-design/icons'
import React, { memo, useCallback, useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import ThemeRecommendRcm from '../../components/themeHeaderRCM'
import Authentication from '../../components/authentication'
import SongCover from '../../components/songCover'
import { getUserSongList, setCreateUserSongList } from '../../request/user'
import { changeIsVisible } from '../../components/themeLogin/store/actionCreators'
import { getCity, getSizeImage } from '../../utils/formatUtils'
import Modal from 'antd/lib/modal/Modal'
import { Input, message } from 'antd'
import { useNavigate } from 'react-router-dom'
import './style.css'

export default memo(function Profile(props) {
  // props/state
  const [playlist, setPlaylist] = useState([])
  const [isModalVisible, setIsModalVisible] = useState(false)
  const [playlistName, setPlaylistName] = useState('')

  // redux
  const dispatch = useDispatch()
  const { isLogin, userinfo, cookie } = useSelector((state: any) => ({
    isLogin: state.getIn(['loginState', 'isLogin']),
    userinfo: state.getIn(['loginState', 'profile']),
    cookie: state.getIn(['loginState', 'cookie']),
  }))

  // handle constant
  const userPic =
    userinfo && userinfo.avatarUrl && getSizeImage(userinfo.avatarUrl, 180)
  const vip = userinfo && userinfo.vipType
  const nickname = userinfo && userinfo.nickname
  const gender = userinfo && userinfo.gender === 1 ? 'man' : 'woman'
  const dynamic = [
    { name: '动态',
      value: userinfo && userinfo.authStatus},
    { name: '关注',
      value: userinfo && userinfo.follows},
    { name: '粉丝',
      value: userinfo && userinfo.followeds },
    ]
  const signature = userinfo && userinfo.signature
  const city = userinfo && userinfo.city && getCity(userinfo.city)
  const songlistCount = userinfo && userinfo.playlistCount
  const userId = userinfo && userinfo.userId

  // other hook
  useEffect(() => {
    getUserSongList(userId).then((res: any) => {
      setPlaylist(res.playlist)
    })
  }, [userId])
  // handle

  const navigate = useNavigate()

  const toNavigate = useCallback(() => {
    navigate('/user')
  }, [navigate])

  const showModal = useCallback(() => {
    dispatch(changeIsVisible(true))
  }, [dispatch])

  
  // modal function
  const showModalDom = () => {
    setIsModalVisible(true)
  }

  const handleOk = () => {
    setIsModalVisible(false)
    setCreateUserSongList(playlistName, cookie).then((res: any) => {
      if (res.code === 200) {
        message.success('创建成功').then(() => {
          window.location.reload()
        })
      }
    })
  }

  const handleCancel = () => {
    setIsModalVisible(false)
  }

  // template
  const renderDynamicList = () => {
    return dynamic.map((item) => {
      return (
        <div className="dynamicItem" key={item.name}>
          <strong className="dynamicCount">{item.value}</strong>
          <span>{item.name}</span>
        </div>
      )
    })
  }

  const renderCreatePlaylist = () => {
    return (
      <span className="userSongListCreator" onClick={showModalDom}>
        创建歌单
      </span>
    )
  }

  return (
      <div className='userBox'>
      <Authentication flag={isLogin} to={toNavigate} showModal={showModal} />
      <div className="userInfo">
        <div className="userPic">
          <img src={userPic} alt="" />
        </div>
        <div className="userDetail">
          <div className="userNicknameBox">
            <h3 className="userNickname">{nickname}</h3>
            <span className="userVip">
              {vip}
              <i></i>
            </span>
            <div className="userGender">
              {gender === 'man' ? (
                <ManOutlined className="userGender man" />
              ) : (
                <WomanOutlined className="userGender woman" color="#e60026" />
              )}
            </div>
          </div>
          <div className="userDynamic">{renderDynamicList()}</div>
          <div className="userRecommend">个人介绍：{signature}</div>
          <div className="userAddress">所在地区：{city}</div>
        </div>
      </div>
      <div className="userSongListBox">
        <ThemeRecommendRcm
          title={`我的歌单(${songlistCount})`}
          right={renderCreatePlaylist()}
          showIcon={true}
        />
        <div className="userSongList">
          {playlist &&
            playlist.map &&
            playlist.map((item: any) => {
              return <SongCover info={item} key={item.id} />
            })}
        </div>
      </div>
      <Modal
        title="创建歌单"
        okText="确认"
        cancelText="取消"
        visible={isModalVisible}
        onOk={handleOk}
        onCancel={handleCancel}
      >
        <Input
          size="large"
          placeholder="请输入歌单"
          prefix={<PlayCircleOutlined />}
          value={playlistName}
          onInput={({ target }: any) => setPlaylistName(target.value)}
        />
      </Modal>
      </div>
  )
})
