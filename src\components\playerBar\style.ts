import styled from 'styled-components'

export const PlayerBarBox = styled.div`
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  height: 53px;
  background: url(${require('../../static/images/playbar_sprite.png')}) repeat 0px 0px;

  .playerBarContent {
    display: flex;
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    bottom: 0;
    height: 47px;
    align-content: center;
  }

  /* 进入 */
  .playBarPlayList-enter,
  .playBarPlayList-appear {
    opacity: 0;
    transform: scale(0.6);
  }
  /* 执行动画 */
  .playBarPlayList-enter-active,
  .playBarPlayList-appear-active {
    transition: opacity 300ms, transform 300ms;
    opacity: 1;
    transform: scale(1);
  }

  /* 离开 */
  .playBarPlayList-exit {
    opacity: 1;
    transform: scale(1);
  }

  .playBarPlayList-exit-active {
    opacity: 0;
    transform: scale(0.6);
    transition: opacity 300ms, transform 300ms;
  }

  .playBarPlayList-exit-done {
    opacity: 0;
  }
`

export const Control: any = styled.div`
  display: flex;
  align-items: center;

  .playerBarButtonPre,
  .playerBarButtonPlay,
  .playerBarButtonNext {
    width: 28px;
    height: 28px;
    margin-right: 8px;
    margin-top: 5px;
    cursor: pointer;
    background: url(${require('../../static/images/playbar_sprite.png')});
  }

  .playerBarButtonPre {
    background-position: 0 -130px;
    &:hover {
      background-position: -30px -130px;
    }
  }

  .playerBarButtonPlay {
    width: 36px;
    height: 36px;
    background-position: 0 ${(props: any) => (props.isPlaying ? '-165px' : '-204px')};
    margin-top: 0;
    &:hover {
      background-position: -40px ${(props: any) => (props.isPlaying ? '-165px' : '-204px')};
    }
  }

  .playerBarButtonNext {
    background-position: -80px -130px;
    &:hover {
      background-position: -110px -130px;
    }
  }
`

export const PlayerBarInfo = styled.div`
  display: flex;
  .playerBarImage {
    width: 34px;
    height: 35px;
    border-radius: 5px;
    overflow: hidden;
    margin: 6px 15px 0 0;
  }

  .playerBarSongDetail {
    .playerBarSongInfo {
      width: 100%;
      height: 28px;
      overflow: hidden;
      text-shadow: 0 1px 0 #171717;
      line-height: 28px;
      .playerBarSongName {
        color: #e8e8e8;
        margin: 0 10px;
      }
      .playerBarSingerName {
        color: #9b9b9b;
      }
    }

    .ant-slider {
      width: 493px;
      height: 9px;

      margin-top: -2px;
      z-index: 100;

      .ant-slider-rail,
      .ant-slider-track,
      .ant-slider-step {
        height: 9px;
      }

      .ant-slider-rail {
        background: url(${require('../../static/images/progress_bar.png')}) 0 0;
      }

      .ant-slider-track {
        background: url(${require('../../static/images/progress_bar.png')});
        background-position: left -66px;
      }

      .ant-slider-handle {
        width: 20px;
        height: 22px;
        border: 0;
        background: url(${require('../../static/images/sprite_icon.png')});
        background-position: 0 -250px;
      }
    }
  }

  .playerBarSongTime {
    line-height: 68px;
    color: #a1a1a1;
    margin: 0 30px 0 9px;

    .playerBarTotalTime {
      color: #797979;
    }
  }
`

export const PlayerBarOperator: any = styled.div`
  display: flex;
  position: relative;
  top: 5px;

  .playBarOperatorRightButton {
    width: 25px;
    height: 25px;
    cursor: pointer;
    background: url(${require('../../static/images/playbar_sprite.png')});
  }

  .refresh {
    margin: 0 5px;
    cursor: pointer;
    font-size: 17px;
    color: #9f9f9f;
    &:hover {
      color: #ccc;
    }
  }


  .playBarOperatorLeft {
    display: flex;
    align-items: center;
  }

  .anticon-download {
    cursor: pointer;
    padding: 1px 6px;
    font-size: 19px;
    color: #adadad;
  }

  .playBarOperatorRight {
    display: flex;
    align-items: center;
    width: 126px;
    padding-left: 13px;
    background-position: -147px -240px;

    .volume {
      background-position: -2px -248px;
    }

    .loop {
      background-position: ${(props: any) => {
    switch (props.playSequence) {
      case 1:
        return '-66px -248px;'
      case 2:
        return '-66px -344px;'
      default:
        return '-3px -344px;'
    }
  }};
  }
    .playlist {
      /* position: relative; */
      padding-left: 18px;
      text-align: center;
      color: #ccc;
      width: 59px;
      background-position: -42px -68px;
    }
  }

  .playBarOperatorRightButtonTopVolume {
    position: absolute;
    top: -117px;
    left: 65px;
    clear: both;
    width: 32px;
    height: 113px;
    overflow: hidden;
    padding: 10px;
    background: url(${require('../../static/images/playbar_sprite.png')});
    background-position: 0 -503px;

    
    .ant-slider-vertical {
      margin: 0;
      .ant-slider-rail {
        background-color: transparent;
      }

      .ant-slider-track {
        background: url(${require('../../static/images/playbar_sprite.png')}) no-repeat
          0 9999px;
        background-position: -40px bottom;
      }

      .ant-slider-handle {
        border: 0;
        background: url(${require('../../static/images/sprite_icon.png')});
        background-position: -42px -250px;
      }
    }
  }
`
