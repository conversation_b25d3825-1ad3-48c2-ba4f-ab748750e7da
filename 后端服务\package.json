{"name": "c2b-recycle-api", "version": "1.0.0", "description": "C2B回收平台后端API服务 - Node.js版本", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "test": "jest", "lint": "eslint .", "lint:fix": "eslint . --fix"}, "keywords": ["recycle", "c2b", "api", "express", "nodejs"], "author": "C2B Recycle Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.0.0", "morgan": "^1.10.0", "compression": "^1.7.4", "express-rate-limit": "^6.8.1", "joi": "^17.9.2", "jsonwebtoken": "^9.0.1", "bcryptjs": "^2.4.3", "mysql2": "^3.6.0", "redis": "^4.6.7", "axios": "^1.4.0", "moment": "^2.29.4", "winston": "^3.10.0", "winston-daily-rotate-file": "^4.7.1", "multer": "^1.4.5-lts.1", "sharp": "^0.32.1", "socket.io": "^4.7.2", "node-cron": "^3.0.2", "dotenv": "^16.3.1"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.6.1", "supertest": "^6.3.3", "eslint": "^8.44.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^6.1.1"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}