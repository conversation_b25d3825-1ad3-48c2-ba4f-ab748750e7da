import request from '../utils/request';

/**
 * 用户相关API
 */

// 获取用户信息
export const getUserProfile = () => {
  return request.get('/user/profile');
};

// 更新用户信息
export const updateUserProfile = (data) => {
  return request.put('/user/profile', data);
};

// 获取用户地址列表
export const getUserAddresses = () => {
  return request.get('/user/addresses');
};

// 添加用户地址
export const addUserAddress = (data) => {
  return request.post('/user/addresses', data);
};

// 更新用户地址
export const updateUserAddress = (id, data) => {
  return request.put(`/user/addresses/${id}`, data);
};

// 删除用户地址
export const deleteUserAddress = (id) => {
  return request.delete(`/user/addresses/${id}`);
};

// 获取用户收藏列表
export const getUserFavorites = (params) => {
  return request.get('/user/favorites', { params });
};

// 添加收藏
export const addFavorite = (data) => {
  return request.post('/user/favorites', data);
};

// 取消收藏
export const removeFavorite = (id) => {
  return request.delete(`/user/favorites/${id}`);
};
