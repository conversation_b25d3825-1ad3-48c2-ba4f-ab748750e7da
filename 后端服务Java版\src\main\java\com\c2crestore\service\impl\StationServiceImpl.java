package com.c2crestore.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.c2crestore.common.exception.BusinessException;
import com.c2crestore.common.result.ResultCode;
import com.c2crestore.dto.NearbyStationDTO;
import com.c2crestore.dto.SearchStationDTO;
import com.c2crestore.entity.Station;
import com.c2crestore.mapper.FavoriteMapper;
import com.c2crestore.mapper.StationMapper;
import com.c2crestore.service.StationService;
import com.c2crestore.vo.StationDetailVO;
import com.c2crestore.vo.StationVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 站点服务实现类
 * 
 * <AUTHOR> Restore Team
 * @since 2024-01-15
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class StationServiceImpl implements StationService {

    private final StationMapper stationMapper;
    private final FavoriteMapper favoriteMapper;

    @Override
    public IPage<StationVO> getNearbyStations(NearbyStationDTO queryDTO) {
        log.info("查询附近站点，经度: {}, 纬度: {}, 半径: {}km, 状态: {}", 
                queryDTO.getLng(), queryDTO.getLat(), queryDTO.getRadius(), queryDTO.getStatus());

        // 构建分页参数
        Page<StationVO> page = new Page<>(queryDTO.getPage(), queryDTO.getSize());
        
        // 执行查询
        IPage<StationVO> result = stationMapper.selectNearbyStations(
                page, 
                queryDTO.getLng(), 
                queryDTO.getLat(), 
                queryDTO.getRadius(), 
                queryDTO.getStatus()
        );
        
        log.info("查询附近站点完成，总数: {}, 当前页数量: {}", result.getTotal(), result.getRecords().size());
        return result;
    }

    @Override
    public IPage<StationVO> searchStations(SearchStationDTO searchDTO) {
        log.info("搜索站点，关键词: {}, 城市: {}, 页码: {}, 大小: {}", 
                searchDTO.getKeyword(), searchDTO.getCity(), searchDTO.getPage(), searchDTO.getSize());

        // 构建分页参数
        Page<StationVO> page = new Page<>(searchDTO.getPage(), searchDTO.getSize());
        
        // 执行搜索
        IPage<StationVO> result = stationMapper.searchStations(page, searchDTO.getKeyword(), searchDTO.getCity());
        
        log.info("搜索站点完成，总数: {}, 当前页数量: {}", result.getTotal(), result.getRecords().size());
        return result;
    }

    @Override
    public StationDetailVO getStationDetail(Long stationId, Long userId) {
        log.info("获取站点详情，stationId: {}, userId: {}", stationId, userId);

        if (stationId == null || stationId <= 0) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "站点ID不能为空");
        }

        // 获取站点基本信息
        StationVO stationVO = stationMapper.selectStationDetail(stationId);
        if (stationVO == null) {
            throw new BusinessException(ResultCode.STATION_NOT_FOUND);
        }

        // 构建详情VO
        StationDetailVO detailVO = new StationDetailVO();
        detailVO.setId(stationVO.getId());
        detailVO.setName(stationVO.getName());
        detailVO.setPhone(stationVO.getPhone());
        detailVO.setAddress(stationVO.getAddress());
        detailVO.setLng(stationVO.getLng());
        detailVO.setLat(stationVO.getLat());
        detailVO.setCity(stationVO.getCity());
        detailVO.setHours(stationVO.getHours());
        detailVO.setImage(stationVO.getImage());
        detailVO.setRating(stationVO.getRating());
        detailVO.setReviewCount(stationVO.getReviewCount());
        detailVO.setStatus(stationVO.getStatus());
        detailVO.setDistance(stationVO.getDistance());
        detailVO.setCreatedAt(stationVO.getCreatedAt());

        // 检查是否已收藏
        if (userId != null) {
            var favorite = favoriteMapper.selectByUserAndTarget(userId, 1, stationId);
            detailVO.setIsFavorited(favorite != null);
        } else {
            detailVO.setIsFavorited(false);
        }

        // 获取服务分类列表（简化处理）
        detailVO.setServiceCategories(java.util.Collections.emptyList());

        // 获取最新价格列表（简化处理）
        detailVO.setLatestPrices(java.util.Collections.emptyList());

        log.info("获取站点详情成功，stationName: {}", detailVO.getName());
        return detailVO;
    }

    @Override
    public List<StationVO> getHotStations(String city, Integer limit) {
        log.info("获取热门站点，城市: {}, 数量: {}", city, limit);

        if (limit == null || limit <= 0) {
            limit = 10;
        }
        if (limit > 50) {
            limit = 50;
        }

        List<Station> hotStations = stationMapper.selectHotStations(city, limit);
        
        // 转换为VO
        List<StationVO> result = hotStations.stream().map(station -> {
            StationVO vo = new StationVO();
            vo.setId(station.getId());
            vo.setName(station.getName());
            vo.setPhone(station.getPhone());
            vo.setAddress(station.getAddress());
            vo.setLng(station.getLng());
            vo.setLat(station.getLat());
            vo.setCity(station.getCity());
            vo.setHours(station.getHours());
            vo.setImage(station.getImage());
            vo.setRating(station.getRating());
            vo.setReviewCount(station.getReviewCount());
            vo.setStatus(station.getStatus());
            vo.setCreatedAt(station.getCreatedAt());
            return vo;
        }).collect(Collectors.toList());
        
        log.info("获取热门站点完成，数量: {}", result.size());
        return result;
    }
}
