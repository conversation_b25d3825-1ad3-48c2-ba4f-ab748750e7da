import React, { useState, memo } from 'react';
import { useSelector, useDispatch, shallowEqual } from "react-redux";
import { PER_PAGE_NUMBER } from '../store/actionTypes';
import { getSongList } from "../store/actionCreators";
import SongCover from '../../../../components/songCover'
import Pagination from '../../../../components/pagination';
import { Skeleton } from 'antd';
import './style.css'
export default memo(function SongListMain() {
  // hooks
  const [currentPage, setCurrentPage] = useState(1);

  // redux
  const { categorySongs } = useSelector((state: any) => ({
    categorySongs: state.getIn(["songList", "categorySongs"])
  }), shallowEqual);
  const songList = categorySongs.playlists || [];
  const total = categorySongs.total || 0;
  const dispatch = useDispatch();

  function onPageChange(page: any, pageSize: any) {
    window.scroll(0, 0);
    setCurrentPage(page);
    dispatch(getSongList(page));
  }
  return (
    <div className="songListMainBox">
      {!songList.length? <Skeleton active /> : 
      <div className="songListMain">
        {
          songList.map((item: any, index: any) => {
            return (
              <SongCover info={item} key={item.id+index} />
            )
          })
        }
      </div>}
      <Pagination currentPage={currentPage} 
                    total={total} 
                    pageSize={PER_PAGE_NUMBER}
                    onPageChange={onPageChange}/>
    </div>
  )
})
