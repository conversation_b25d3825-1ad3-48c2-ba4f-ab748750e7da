import React, { memo } from 'react'
import { getCount, getSizeImage } from '../../utils/formatUtils'
import './style.css'

function SongCover(props: any) {
  const { info, songList, width = 140 } = props
  // pic
  const picUrl = (info && (info.picUrl || info.coverImgUrl)) || (songList && songList.coverImgUrl) 
  // playCount 播放次数 
  const playCount = (info && info.playCount) || (songList && songList.playCount) || 0
  // name
  const name = (info && info.name) || (songList && songList.name) 
  // id
  const songInfoId = (info && info.id) || (songList && songList.id)
  
  return (
    <a className='songCoverBox' href={`#/songList?songListId=${songInfoId}`} style={{width: width}}>
      <div className="songCover">
        <img src={getSizeImage(picUrl, 140)} alt="" />
        <div className="songCoverMask">
          <div className="songCoverBottomBar">
            <span>
              <i className="songCoverIconHeadset"></i>
              {getCount(playCount)}
            </span>
            <i className="songCoverIconPlay"></i>
          </div>
        </div>
      </div>
      <div className="songCoverTitle ">{name}</div>
    </a>
  )
}

export default memo(SongCover)
