/* 基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
    color: #333;
}

/* 演示容器 */
.demo-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.demo-header {
    text-align: center;
    margin-bottom: 30px;
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.demo-header h1 {
    color: #52C41A;
    font-size: 2.5em;
    margin-bottom: 10px;
}

.demo-header p {
    color: #666;
    font-size: 1.2em;
}

/* 导航 */
.demo-nav {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-bottom: 30px;
    flex-wrap: wrap;
}

.nav-btn {
    padding: 12px 24px;
    border: 2px solid #52C41A;
    background: white;
    color: #52C41A;
    border-radius: 25px;
    cursor: pointer;
    font-size: 16px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.nav-btn:hover {
    background: #f6ffed;
    transform: translateY(-2px);
}

.nav-btn.active {
    background: #52C41A;
    color: white;
}

/* 页面容器 */
.page {
    display: none;
    justify-content: center;
    align-items: flex-start;
    min-height: 600px;
}

.page.active {
    display: flex;
}

/* 手机模拟器 */
.phone-mockup {
    width: 375px;
    height: 812px;
    background: white;
    border-radius: 25px;
    box-shadow: 0 8px 40px rgba(0,0,0,0.15);
    overflow: hidden;
    position: relative;
    border: 8px solid #333;
}

/* 页面头部 */
.phone-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: white;
    border-bottom: 1px solid #f0f0f0;
    font-size: 14px;
}

.location {
    font-weight: 500;
    color: #333;
}

.header-actions {
    display: flex;
    gap: 15px;
}

.search-btn, .filter-btn {
    color: #52C41A;
    cursor: pointer;
}

.page-header {
    padding: 15px 20px;
    background: white;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    align-items: center;
    gap: 15px;
}

.page-header h2 {
    font-size: 18px;
    color: #333;
    flex: 1;
    text-align: center;
}

.back-btn {
    color: #52C41A;
    cursor: pointer;
    font-size: 16px;
}

/* 地图容器 */
.map-container {
    height: 300px;
    position: relative;
    background: #e8f5e8;
}

.map-placeholder {
    width: 100%;
    height: 100%;
    position: relative;
    background: linear-gradient(45deg, #e8f5e8 25%, #f0f9f0 25%, #f0f9f0 50%, #e8f5e8 50%, #e8f5e8 75%, #f0f9f0 75%);
    background-size: 20px 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.map-bg {
    font-size: 18px;
    color: #52C41A;
    font-weight: 500;
}

.map-marker {
    position: absolute;
    font-size: 20px;
    animation: bounce 2s infinite;
}

.map-label {
    position: absolute;
    top: 35%;
    left: 25%;
    background: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

/* 快捷操作 */
.quick-actions {
    display: flex;
    padding: 20px;
    gap: 15px;
}

.action-card {
    flex: 1;
    background: white;
    border-radius: 12px;
    padding: 20px 10px;
    text-align: center;
    box-shadow: 0 2px 8px rgba(0,0,0,0.06);
    border: 1px solid #f0f0f0;
    cursor: pointer;
    transition: all 0.3s ease;
}

.action-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0,0,0,0.1);
}

.action-card.highlight {
    background: linear-gradient(135deg, #52C41A, #73D13D);
    color: white;
    border: none;
}

.action-icon {
    font-size: 24px;
    margin-bottom: 8px;
}

.action-text {
    font-size: 14px;
    font-weight: 500;
}

/* 站点列表 */
.station-list {
    padding: 0 20px 100px;
}

.station-list h3 {
    margin-bottom: 15px;
    font-size: 16px;
    color: #333;
}

.station-card {
    background: white;
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.06);
    border: 1px solid #f0f0f0;
}

.station-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.station-name {
    font-weight: 600;
    font-size: 16px;
}

.station-meta {
    display: flex;
    gap: 10px;
    font-size: 14px;
}

.rating {
    color: #faad14;
}

.distance {
    color: #52C41A;
}

.station-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    font-size: 14px;
    color: #666;
}

.status.open {
    color: #52C41A;
}

.station-services {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.service-tag {
    background: #f6ffed;
    color: #52C41A;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    border: 1px solid #b7eb8f;
}

/* 底部导航 */
.bottom-nav {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: white;
    border-top: 1px solid #f0f0f0;
    display: flex;
    padding: 8px 0;
}

.nav-item {
    flex: 1;
    text-align: center;
    padding: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.nav-item.active {
    color: #52C41A;
}

.nav-icon {
    font-size: 20px;
    margin-bottom: 4px;
}

.nav-label {
    font-size: 12px;
}

/* 搜索框 */
.search-box {
    padding: 15px 20px;
}

.search-input {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e8e8e8;
    border-radius: 25px;
    background: #fafafa;
    font-size: 14px;
    outline: none;
    transition: all 0.3s ease;
}

.search-input:focus {
    border-color: #52C41A;
    background: white;
    box-shadow: 0 0 0 4px rgba(82, 196, 26, 0.1);
}

/* 分类网格 */
.section {
    padding: 0 20px 20px;
}

.section h3 {
    margin-bottom: 15px;
    font-size: 16px;
    color: #333;
}

.category-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
    margin-bottom: 20px;
}

.category-card {
    background: white;
    border-radius: 12px;
    padding: 16px 8px;
    text-align: center;
    box-shadow: 0 2px 8px rgba(0,0,0,0.06);
    border: 1px solid #f0f0f0;
    cursor: pointer;
    transition: all 0.3s ease;
}

.category-card:hover {
    transform: scale(0.95);
    box-shadow: 0 4px 16px rgba(0,0,0,0.1);
}

.category-icon {
    font-size: 32px;
    margin-bottom: 8px;
}

.category-name {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 4px;
}

.category-price {
    font-size: 12px;
    color: #52C41A;
    font-weight: 500;
}

/* 分类列表 */
.category-list {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.06);
}

.category-item {
    padding: 16px;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    transition: background 0.3s ease;
}

.category-item:last-child {
    border-bottom: none;
}

.category-item:hover {
    background: #f6ffed;
}

.item-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 8px;
}

.item-icon {
    font-size: 20px;
}

.item-name {
    flex: 1;
    font-weight: 600;
    font-size: 16px;
}

.item-price {
    color: #52C41A;
    font-weight: 500;
    font-size: 14px;
}

.item-desc {
    color: #666;
    font-size: 14px;
    margin-left: 32px;
}

/* AI路线规划样式 */
.route-info {
    padding: 20px;
    background: #f6ffed;
    border-bottom: 1px solid #f0f0f0;
}

.info-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
    font-size: 14px;
    color: #333;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
}

.stat-item {
    text-align: center;
}

.stat-value {
    font-size: 16px;
    font-weight: 600;
    color: #52C41A;
    margin-bottom: 4px;
}

.stat-label {
    font-size: 12px;
    color: #666;
}

.setting-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
}

.setting-card {
    background: white;
    border: 2px solid #e8e8e8;
    border-radius: 12px;
    padding: 16px 8px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.setting-card.active {
    border-color: #52C41A;
    background: #f6ffed;
}

.setting-icon {
    font-size: 24px;
    margin-bottom: 8px;
}

.setting-text {
    font-size: 12px;
    font-weight: 500;
}

.route-preview {
    padding: 20px;
    background: white;
}

.route-map {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 20px;
    text-align: center;
}

.route-line {
    font-size: 16px;
    margin-bottom: 10px;
    color: #333;
}

.route-distances {
    font-size: 12px;
    color: #666;
}

.route-details {
    padding: 0 20px 20px;
    max-height: 200px;
    overflow-y: auto;
}

.route-step {
    display: flex;
    gap: 12px;
    margin-bottom: 16px;
    padding: 12px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.06);
}

.step-number {
    font-size: 16px;
    flex-shrink: 0;
}

.step-content {
    flex: 1;
}

.step-header {
    font-weight: 600;
    margin-bottom: 4px;
    font-size: 14px;
}

.step-meta {
    font-size: 12px;
    color: #666;
    margin-bottom: 4px;
}

.step-desc {
    font-size: 12px;
    color: #52C41A;
}

.ai-suggestions {
    padding: 0 20px 20px;
}

.suggestion-list {
    background: #e6f7ff;
    border-radius: 12px;
    padding: 16px;
    margin: 0;
    list-style: none;
}

.suggestion-list li {
    font-size: 14px;
    color: #333;
    margin-bottom: 8px;
}

.suggestion-list li:last-child {
    margin-bottom: 0;
}

.action-buttons {
    padding: 20px;
    display: flex;
    gap: 12px;
}

.btn {
    flex: 1;
    padding: 12px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, #52C41A, #73D13D);
    color: white;
}

.btn-secondary {
    background: white;
    color: #52C41A;
    border: 2px solid #52C41A;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

/* 行情页样式 */
.date {
    font-size: 14px;
    color: #666;
}

.location-selector {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: #f6ffed;
    border-bottom: 1px solid #f0f0f0;
    font-size: 14px;
}

.change-location {
    color: #52C41A;
    cursor: pointer;
}

.trend-card {
    background: white;
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.06);
    border-left: 4px solid #52C41A;
}

.trend-card.down {
    border-left-color: #ff4d4f;
}

.trend-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.product-name {
    font-weight: 600;
    font-size: 16px;
}

.trend-indicator {
    font-size: 14px;
    font-weight: 500;
}

.trend-card.up .trend-indicator {
    color: #ff4d4f;
}

.trend-card.down .trend-indicator {
    color: #52C41A;
}

.price-range {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.trend-desc {
    font-size: 14px;
    color: #666;
}

.chart-container {
    background: white;
    border-radius: 12px;
    padding: 16px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.06);
}

.chart-title {
    text-align: center;
    font-weight: 600;
    margin-bottom: 16px;
    color: #333;
}

.chart-placeholder {
    height: 120px;
    position: relative;
    background: #f8f9fa;
    border-radius: 8px;
    padding: 16px;
}

.chart-line {
    position: relative;
    height: 80px;
}

.chart-point {
    position: absolute;
    width: 8px;
    height: 8px;
    background: #52C41A;
    border-radius: 50%;
    font-size: 10px;
    color: #333;
    font-weight: 500;
}

.chart-labels {
    display: flex;
    justify-content: space-between;
    margin-top: 8px;
    font-size: 12px;
    color: #666;
}

.price-list {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.06);
}

.price-item {
    display: flex;
    align-items: center;
    padding: 16px;
    border-bottom: 1px solid #f0f0f0;
    gap: 12px;
}

.price-item:last-child {
    border-bottom: none;
}

.item-icon {
    font-size: 20px;
    width: 24px;
}

.item-name {
    flex: 1;
    font-weight: 500;
}

.item-price {
    font-weight: 600;
    color: #333;
}

.price-change {
    font-size: 14px;
    font-weight: 500;
    min-width: 60px;
    text-align: right;
}

.price-change.up {
    color: #ff4d4f;
}

.price-change.down {
    color: #52C41A;
}

.price-change.stable {
    color: #8c8c8c;
}

/* 个人中心样式 */
.user-header {
    padding: 20px;
    background: linear-gradient(135deg, #52C41A, #73D13D);
    color: white;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
}

.avatar {
    width: 60px;
    height: 60px;
    background: rgba(255,255,255,0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
}

.username {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 4px;
}

.user-phone {
    font-size: 14px;
    opacity: 0.9;
}

.header-actions {
    display: flex;
    justify-content: space-between;
    font-size: 14px;
}

.settings, .messages {
    cursor: pointer;
    opacity: 0.9;
}

.achievement-card {
    margin: 20px;
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.06);
    border: 1px solid #f0f0f0;
}

.achievement-title {
    font-size: 18px;
    font-weight: 600;
    color: #52C41A;
    margin-bottom: 15px;
    text-align: center;
}

.achievement-stats {
    display: flex;
    justify-content: space-around;
    margin-bottom: 15px;
}

.stat {
    text-align: center;
}

.stat-label {
    display: block;
    font-size: 14px;
    color: #666;
    margin-bottom: 4px;
}

.stat-value {
    display: block;
    font-size: 20px;
    font-weight: 600;
    color: #333;
}

.eco-contribution {
    text-align: center;
    font-size: 14px;
    color: #52C41A;
    background: #f6ffed;
    padding: 12px;
    border-radius: 8px;
}

.service-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
    padding: 0 20px;
}

.service-item {
    background: white;
    border-radius: 12px;
    padding: 20px 10px;
    text-align: center;
    box-shadow: 0 2px 8px rgba(0,0,0,0.06);
    border: 1px solid #f0f0f0;
    cursor: pointer;
    transition: all 0.3s ease;
}

.service-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0,0,0,0.1);
}

.service-icon {
    font-size: 24px;
    margin-bottom: 8px;
    display: block;
}

.service-text {
    font-size: 14px;
    font-weight: 500;
    color: #333;
}

.tool-list {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.06);
    margin: 0 20px 100px;
}

.tool-item {
    display: flex;
    align-items: center;
    padding: 16px;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    transition: background 0.3s ease;
}

.tool-item:last-child {
    border-bottom: none;
}

.tool-item:hover {
    background: #f6ffed;
}

.tool-icon {
    font-size: 20px;
    margin-right: 12px;
    width: 24px;
}

.tool-name {
    flex: 1;
    font-size: 16px;
    color: #333;
}

.tool-arrow {
    color: #8c8c8c;
    font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .demo-container {
        padding: 10px;
    }
    
    .phone-mockup {
        width: 100%;
        max-width: 375px;
        height: 600px;
    }
    
    .demo-nav {
        gap: 5px;
    }
    
    .nav-btn {
        padding: 8px 16px;
        font-size: 14px;
    }
}
