import React, { memo } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import propTypes from 'prop-types'
import { getSongDetailAction } from '../../../../components/playerBar/store'
import { useAddPlaylist } from '../../../../hooks/changeMusic'
import { PlayCircleOutlined } from '@ant-design/icons'
import { message } from 'antd'
import './style.css'

function SingleSongItem(props: any) {
  // props/state
  const { songId, songName, singer, album, duration } = props

  // redux hook
  const dispatch = useDispatch()
  const { playlist } = useSelector((state: any) => ({
    playlist: state.getIn(['player', 'playList']),
  }))

  // 播放音乐
  const playMusic = () => {
    dispatch(getSongDetailAction(songId));
    (document.getElementById('audio') as any).autoplay = true
  }

  const addPlaylist = useAddPlaylist(playlist, message)

  return (
      <div className="singleSongItemBox">
        <div className="singleSongName">
        <PlayCircleOutlined onClick={() => playMusic()} />
        <a href={`#/song?id=${songId}`} onClick={() => playMusic()}>{songName}</a>
        <button
          className="singleSongButton"
          onClick={(e) => addPlaylist(e, songId)}
        ></button>
      </div>
      <span className='singleSongSinger'>
        {singer}
      </span>
      <div className="textNowrap singleSongAlbum">《{album}》</div>
      <div className="textNowrap singleSongDuration">{duration}</div>
      </div>
      
  )
}

SingleSongItem.propTypes = {
  songId: propTypes.number.isRequired,
  songName: propTypes.string.isRequired,
  singer: propTypes.string.isRequired,
  album: propTypes.string.isRequired,
  duration: propTypes.string.isRequired,
}

export default memo(SingleSongItem)
