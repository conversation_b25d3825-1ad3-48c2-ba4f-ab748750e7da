import React, { memo, useEffect, useRef } from 'react'
import { shallowEqual, useDispatch, useSelector } from 'react-redux'

import { getNewAlbumsAction } from '../store/actionCreators'

import ThemeHeaderRcm from '../../../../components/themeHeaderRCM'
import AlbumCover from '../../../../components/albumCover'
import { Carousel } from 'antd'
import './style.css'
import { useNavigate } from 'react-router-dom'
import { MusicDetail } from '../../../../models/music'

export default memo(function NewAlbum() {
    const navigate = useNavigate();
    const dispatch = useDispatch()
    const { newAlbums } = useSelector(
        (state: any) => ({
            newAlbums: state.getIn(['recommend', 'newAlbums']),
        }),
        shallowEqual
    )
    
    const albumRef: any = useRef()

    useEffect(() => {
        dispatch(getNewAlbumsAction())
    }, [dispatch])

    const handleTitleClick = () => {
        navigate('/discover/album')
    }

    return (
        <div className="newAlbum">
            <ThemeHeaderRcm title="新碟上架" titleClick={() => handleTitleClick()}/>
            <div className="newAlbumContent">
                <div className="newAlbumInner">
                    <Carousel dots={false} ref={albumRef}>
                        {[0, 1].map(item => {
                            return (
                                <div key={item} className="newAlbumInnerPage">
                                    {newAlbums && newAlbums.slice(item * 5, (item + 1) * 5).map((cItem: MusicDetail) => {
                                        return (
                                            <AlbumCover
                                                key={cItem.id}
                                                info={cItem}
                                                size={100}
                                                width={118}
                                                bgp="-570px"
                                            >
                                                {cItem.name}
                                            </AlbumCover>
                                        )
                                    })}
                                </div>
                            )
                        })}
                    </Carousel>
                </div>
                <div
                    className="newAlbumArrow newAlbumArrowLeft"
                    onClick={e => albumRef.current.prev()}
                ></div>
                <div
                    className="newAlbumArrow newAlbumArrowRight"
                    onClick={e => albumRef.current.next()}
                ></div>
            </div>
    </div>
    )
})
