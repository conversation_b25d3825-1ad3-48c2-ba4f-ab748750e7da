package com.c2brecycle.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 产品信息VO
 * 
 * <AUTHOR> Recycle Team
 * @since 2024-01-15
 */
@Data
@ApiModel("产品信息")
public class ProductVO {

    @ApiModelProperty("产品ID")
    private Long id;

    @ApiModelProperty("产品名称")
    private String name;

    @ApiModelProperty("产品图片")
    private String image;

    @ApiModelProperty("分类ID")
    private Long categoryId;

    @ApiModelProperty("分类名称")
    private String categoryName;

    @ApiModelProperty("产品描述")
    private String description;

    @ApiModelProperty("单位")
    private String unit;

    @ApiModelProperty("最低价格")
    private BigDecimal minPrice;

    @ApiModelProperty("最高价格")
    private BigDecimal maxPrice;

    @ApiModelProperty("平均价格")
    private BigDecimal avgPrice;

    @ApiModelProperty("搜索次数")
    private Integer searchCount;

    @ApiModelProperty("状态 0-下架 1-上架")
    private Integer status;

    @ApiModelProperty("创建时间")
    private LocalDateTime createdAt;
}
