package com.c2crestore.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 收藏信息VO
 * 
 * <AUTHOR> Restore Team
 * @since 2024-01-15
 */
@Data
@ApiModel("收藏信息")
public class FavoriteVO {

    @ApiModelProperty("收藏ID")
    private Long id;

    @ApiModelProperty("用户ID")
    private Long userId;

    @ApiModelProperty("收藏类型 1-站点 2-产品")
    private Integer type;

    @ApiModelProperty("目标ID")
    private Long targetId;

    @ApiModelProperty("目标名称")
    private String targetName;

    @ApiModelProperty("目标图片")
    private String targetImage;

    @ApiModelProperty("目标描述")
    private String targetDesc;

    @ApiModelProperty("收藏时间")
    private LocalDateTime createdAt;
}
