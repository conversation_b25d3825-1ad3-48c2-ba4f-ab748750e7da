package com.manmanrecycle.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.manmanrecycle.entity.User;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 用户Mapper接口
 * 
 * <AUTHOR> Team
 * @since 2024-01-15
 */
@Mapper
public interface UserMapper extends BaseMapper<User> {

    /**
     * 根据openid查询用户
     * 
     * @param openid 微信openid
     * @return 用户信息
     */
    @Select("SELECT * FROM users WHERE openid = #{openid} AND deleted = 0")
    User selectByOpenid(@Param("openid") String openid);

    /**
     * 根据手机号查询用户
     * 
     * @param phone 手机号
     * @return 用户信息
     */
    @Select("SELECT * FROM users WHERE phone = #{phone} AND deleted = 0")
    User selectByPhone(@Param("phone") String phone);

    /**
     * 更新用户最后活动时间
     * 
     * @param userId 用户ID
     * @return 影响行数
     */
    @Select("UPDATE users SET updated_at = NOW() WHERE id = #{userId}")
    int updateLastActiveTime(@Param("userId") Long userId);
}
