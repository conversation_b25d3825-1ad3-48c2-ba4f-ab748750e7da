import React from 'react';
import { useSelector } from 'react-redux';
import { Card, Avatar, Descriptions } from 'antd';
import { UserOutlined } from '@ant-design/icons';

const Profile = () => {
  const { user } = useSelector(state => state.auth);

  return (
    <div className="profile-page">
      <Card title="个人中心">
        <div style={{ textAlign: 'center', marginBottom: 24 }}>
          <Avatar 
            size={80} 
            src={user?.avatar} 
            icon={<UserOutlined />}
          />
          <h3 style={{ marginTop: 16 }}>{user?.nickname || '用户'}</h3>
        </div>
        
        <Descriptions title="用户信息" bordered>
          <Descriptions.Item label="昵称">
            {user?.nickname || '-'}
          </Descriptions.Item>
          <Descriptions.Item label="手机号">
            {user?.phone || '-'}
          </Descriptions.Item>
          <Descriptions.Item label="城市">
            {user?.city || '-'}
          </Descriptions.Item>
          <Descriptions.Item label="注册时间">
            {user?.createdAt || '-'}
          </Descriptions.Item>
        </Descriptions>
        
        <p style={{ marginTop: 24 }}>个人中心功能开发中...</p>
      </Card>
    </div>
  );
};

export default Profile;
