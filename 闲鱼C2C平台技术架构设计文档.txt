闲鱼C2C平台技术架构设计文档
=====================================

项目概述
--------
闲鱼是阿里巴巴旗下的C2C二手交易平台，为用户提供便捷的二手商品买卖服务。
本文档从技术架构角度全面分析闲鱼平台的核心功能、系统设计和实现方案。

目录
----
1. 业务功能拆解
2. 技术架构设计
3. 核心业务流程
4. 数据库设计
5. 系统架构图
6. 性能优化方案
7. 安全设计
8. 部署架构

=====================================
1. 业务功能拆解
=====================================

1.1 核心功能模块
-----------------

用户管理模块
├── 用户注册/登录
│   ├── 手机号注册
│   ├── 第三方登录（微信、支付宝、淘宝）
│   ├── 实名认证
│   └── 芝麻信用授权
├── 用户信息管理
│   ├── 个人资料维护
│   ├── 头像上传
│   ├── 地址管理
│   └── 隐私设置
└── 用户等级体系
    ├── 信誉等级
    ├── 交易积分
    └── 会员特权

商品管理模块
├── 商品发布
│   ├── 商品信息录入
│   ├── 图片上传（最多9张）
│   ├── 价格设置
│   ├── 分类选择
│   ├── 标签设置
│   └── 发布位置
├── 商品管理
│   ├── 商品编辑
│   ├── 商品下架
│   ├── 商品删除
│   ├── 库存管理
│   └── 价格调整
└── 商品展示
    ├── 商品详情页
    ├── 商品图片轮播
    ├── 商品描述
    ├── 卖家信息
    └── 相似商品推荐

交易管理模块
├── 订单管理
│   ├── 订单创建
│   ├── 订单支付
│   ├── 订单状态跟踪
│   ├── 订单取消
│   └── 订单完成
├── 支付系统
│   ├── 支付宝支付
│   ├── 微信支付
│   ├── 银行卡支付
│   └── 担保交易
└── 物流管理
    ├── 快递选择
    ├── 物流跟踪
    ├── 收货确认
    └── 退货处理

搜索推荐模块
├── 搜索功能
│   ├── 关键词搜索
│   ├── 分类搜索
│   ├── 价格筛选
│   ├── 地理位置筛选
│   └── 搜索历史
└── 推荐系统
    ├── 个性化推荐
    ├── 基于位置推荐
    ├── 热门商品推荐
    └── 相似商品推荐

消息通信模块
├── 即时通讯
│   ├── 文字消息
│   ├── 图片消息
│   ├── 语音消息
│   └── 商品卡片
├── 系统通知
│   ├── 交易通知
│   ├── 物流通知
│   ├── 活动通知
│   └── 系统公告
└── 消息管理
    ├── 消息列表
    ├── 消息删除
    ├── 消息置顶
    └── 消息免打扰

社区互动模块
├── 鱼塘功能
│   ├── 地理位置鱼塘
│   ├── 兴趣爱好鱼塘
│   ├── 同城鱼塘
│   └── 话题讨论
├── 动态分享
│   ├── 商品分享
│   ├── 生活动态
│   ├── 点赞评论
│   └── 转发分享
└── 用户关注
    ├── 关注用户
    ├── 粉丝管理
    └── 动态推送

评价反馈模块
├── 交易评价
│   ├── 商品评价
│   ├── 服务评价
│   ├── 物流评价
│   └── 综合评分
├── 举报投诉
│   ├── 商品举报
│   ├── 用户举报
│   ├── 欺诈举报
│   └── 投诉处理
└── 客服支持
    ├── 在线客服
    ├── 常见问题
    ├── 意见反馈
    └── 申诉处理

1.2 业务流程分析
-----------------

用户注册流程：
手机号验证 → 设置密码 → 完善资料 → 实名认证 → 注册完成

商品发布流程：
选择分类 → 录入信息 → 上传图片 → 设置价格 → 发布商品

交易流程：
浏览商品 → 联系卖家 → 确认交易 → 支付订单 → 发货收货 → 确认收货 → 评价

=====================================
2. 技术架构设计
=====================================

2.1 整体架构
--------------

分层架构设计：
┌─────────────────────────────────────┐
│              前端层                  │
├─────────────────────────────────────┤
│              网关层                  │
├─────────────────────────────────────┤
│              业务层                  │
├─────────────────────────────────────┤
│              数据层                  │
├─────────────────────────────────────┤
│              基础设施层               │
└─────────────────────────────────────┘

2.2 技术选型
--------------

前端技术栈：
- 移动端：React Native / Flutter
- Web端：React + TypeScript
- 小程序：支付宝小程序
- 状态管理：Redux / MobX
- UI框架：Ant Design Mobile

后端技术栈：
- 开发语言：Java / Node.js
- 框架：Spring Boot / Express
- 微服务：Spring Cloud / Dubbo
- 数据库：MySQL + Redis + MongoDB
- 消息队列：RocketMQ / Kafka
- 搜索引擎：Elasticsearch
- 缓存：Redis Cluster

基础设施：
- 容器化：Docker + Kubernetes
- 监控：Prometheus + Grafana
- 日志：ELK Stack
- 配置中心：Nacos / Apollo
- 注册中心：Eureka / Nacos
- 负载均衡：Nginx / HAProxy
- CDN：阿里云CDN

2.3 微服务架构
--------------

核心服务划分：
- 用户服务（User Service）
- 商品服务（Product Service）
- 交易服务（Order Service）
- 支付服务（Payment Service）
- 消息服务（Message Service）
- 搜索服务（Search Service）
- 推荐服务（Recommendation Service）
- 文件服务（File Service）
- 通知服务（Notification Service）
- 风控服务（Risk Service）

=====================================
3. 核心业务流程（PlantUML）
=====================================

3.1 用户注册流程图
------------------

```plantuml
@startuml 用户注册登录详细流程
skinparam sequence {
  ArrowColor #2196F3
  LifeLineBorderColor #1976D2
  ParticipantBorderColor #1976D2
}

title 用户注册登录完整时序图

actor 用户 as U
participant "前端页面" as FE
participant "API网关" as GW
participant "用户服务" as US
participant "短信服务" as SMS
participant "Redis缓存" as RC
participant "MySQL数据库" as DB
participant "风控服务" as RS

== 注册流程 ==
U -> FE: 访问注册页面
FE -> FE: 渲染注册表单

U -> FE: 输入手机号
FE -> FE: 手机号格式验证
FE -> GW: 检查手机号是否已注册
GW -> US: checkMobileExists(mobile)
US -> DB: SELECT * FROM users WHERE mobile=?
DB --> US: 返回查询结果
US --> GW: {exists: false}
GW --> FE: 手机号可用

U -> FE: 点击获取验证码
FE -> GW: 发送验证码请求
GW -> RS: 风控检测(IP,设备指纹)
RS --> GW: 通过检测
GW -> SMS: sendSmsCode(mobile)
SMS -> RC: 存储验证码(key:mobile,code:123456,ttl:5min)
SMS -> SMS: 调用短信API发送
SMS --> GW: 发送成功
GW --> FE: 验证码已发送

U -> FE: 输入验证码+密码
FE -> FE: 密码强度检测
FE -> GW: 提交注册信息
GW -> US: register(mobile,code,password)
US -> RC: 验证验证码
RC --> US: 验证码正确
US -> US: 密码加密(BCrypt)
US -> DB: INSERT INTO users(mobile,password,...)
DB --> US: 用户创建成功
US -> US: 生成JWT Token
US -> RC: 存储Token信息
US --> GW: {token, userInfo}
GW --> FE: 注册成功
FE -> FE: 存储Token到LocalStorage
FE -> FE: 跳转到首页

== 登录流程 ==
U -> FE: 访问登录页面
FE -> FE: 渲染登录表单

U -> FE: 输入手机号+密码
FE -> GW: 提交登录请求
GW -> RS: 登录风控检测
RS -> RS: 检查登录频率、异常设备
RS --> GW: 风控通过
GW -> US: login(mobile,password)
US -> DB: SELECT * FROM users WHERE mobile=?
DB --> US: 返回用户信息
US -> US: 验证密码(BCrypt.compare)
alt 密码正确
    US -> US: 生成JWT Token
    US -> RC: 存储用户Session
    US -> DB: UPDATE users SET last_login_time=NOW()
    US --> GW: {token, userInfo}
    GW --> FE: 登录成功
    FE -> FE: 存储Token
    FE -> FE: 跳转到个人中心
else 密码错误
    US --> GW: 密码错误
    GW --> FE: 登录失败
    FE -> FE: 显示错误提示
end

@enduml
```

3.2 商品发布流程图
------------------

```plantuml
@startuml 商品发布详细流程
skinparam sequence {
  ArrowColor #4CAF50
  LifeLineBorderColor #388E3C
  ParticipantBorderColor #388E3C
}

title 商品发布完整时序图

actor 卖家 as S
participant "前端页面" as FE
participant "API网关" as GW
participant "文件服务" as FS
participant "商品服务" as PS
participant "风控服务" as RS
participant "搜索服务" as SS
participant "推荐服务" as REC
participant "OSS存储" as OSS
participant "MySQL" as DB
participant "Elasticsearch" as ES
participant "消息队列" as MQ

S -> FE: 点击发布商品
FE -> GW: 验证用户登录状态
GW -> GW: JWT Token验证
GW --> FE: 用户已登录

FE -> FE: 渲染发布页面
S -> FE: 选择商品分类
FE -> GW: 获取分类列表
GW -> PS: getCategoryList()
PS -> DB: SELECT * FROM categories
DB --> PS: 分类数据
PS --> GW: 分类列表
GW --> FE: 显示分类选择器

S -> FE: 填写商品信息
note right: 标题、描述、价格、成色等

S -> FE: 上传商品图片
FE -> FE: 图片预览+压缩
loop 每张图片
    FE -> GW: 上传图片
    GW -> FS: uploadImage(file)
    FS -> FS: 生成唯一文件名
    FS -> FS: 图片格式转换(WebP)
    FS -> FS: 生成多尺寸缩略图
    FS -> OSS: 上传到OSS
    OSS --> FS: 返回图片URL
    FS -> RS: 图片内容审核
    RS -> RS: 调用AI图片识别
    RS --> FS: 审核结果
    FS --> GW: {url, thumbnails}
    GW --> FE: 图片上传成功
end

S -> FE: 设置商品位置
FE -> FE: 获取GPS定位
FE -> GW: 地理编码转换
GW -> GW: 调用地图API
GW --> FE: 返回地址信息

S -> FE: 提交发布
FE -> GW: 发布商品请求
GW -> PS: createProduct(productInfo)
PS -> RS: 内容审核
RS -> RS: 敏感词过滤
RS -> RS: 违禁品检测
RS --> PS: 审核通过

PS -> DB: BEGIN TRANSACTION
PS -> DB: INSERT INTO products(...)
DB --> PS: product_id
PS -> DB: INSERT INTO product_images(...)
PS -> DB: COMMIT TRANSACTION

PS -> MQ: 发送商品索引消息
MQ -> SS: 消费消息
SS -> ES: 创建商品索引
ES --> SS: 索引成功

PS -> MQ: 发送推荐更新消息
MQ -> REC: 消费消息
REC -> REC: 更新推荐模型

PS --> GW: 发布成功
GW --> FE: 跳转到商品详情页
FE -> FE: 显示发布成功提示

@enduml
```

3.3 交易流程图
--------------

```plantuml
@startuml 交易下单完整时序图
skinparam sequence {
  ArrowColor #9C27B0
  LifeLineBorderColor #7B1FA2
  ParticipantBorderColor #7B1FA2
}

title 交易下单完整时序图

actor 买家 as B
actor 卖家 as S
participant "商品详情页" as FE
participant "聊天页面" as CHAT
participant "下单页面" as ORDER
participant "API网关" as GW
participant "消息服务" as MS
participant "交易服务" as TS
participant "库存服务" as IS
participant "支付服务" as PS
participant "Redis" as RC
participant "MySQL" as DB
participant "消息队列" as MQ

B -> FE: 浏览商品详情
FE -> GW: 获取商品信息
GW -> GW: 检查商品状态
GW --> FE: 商品信息

B -> FE: 点击"我想要"
FE -> CHAT: 跳转聊天页面
CHAT -> GW: 创建会话
GW -> MS: createConversation(buyerId, sellerId, productId)
MS -> DB: INSERT INTO conversations
MS -> MS: 生成会话ID
MS --> GW: 会话创建成功

B -> CHAT: 发送消息"这个还在吗"
CHAT -> GW: 发送消息
GW -> MS: sendMessage(conversationId, content)
MS -> DB: INSERT INTO messages
MS -> RC: 缓存最新消息
MS -> MQ: 推送消息通知
MQ -> S: 实时推送给卖家

S -> CHAT: 回复"在的，可以下单"
CHAT -> GW: 发送消息
GW -> MS: sendMessage()
MS -> MQ: 推送给买家

B -> CHAT: 点击"立即购买"
CHAT -> ORDER: 跳转下单页面
ORDER -> GW: 获取商品信息+运费模板
GW --> ORDER: 商品和运费信息

B -> ORDER: 选择收货地址
B -> ORDER: 确认下单
ORDER -> GW: 创建订单请求
GW -> TS: createOrder(orderInfo)

TS -> IS: 锁定库存
IS -> RC: 使用分布式锁
IS -> DB: UPDATE products SET status='LOCKED'
IS --> TS: 库存锁定成功

TS -> DB: BEGIN TRANSACTION
TS -> DB: INSERT INTO orders(...)
TS -> DB: INSERT INTO order_items(...)
TS -> DB: COMMIT TRANSACTION

TS -> PS: 创建支付单
PS -> DB: INSERT INTO payments
PS -> PS: 生成支付参数
PS --> TS: 支付单创建成功

TS -> MQ: 发送订单创建消息
MQ -> MS: 通知卖家新订单
MS -> S: 推送订单通知

TS --> GW: 订单创建成功
GW --> ORDER: 跳转支付页面

B -> ORDER: 选择支付方式
ORDER -> GW: 发起支付
GW -> PS: 调用支付接口
PS -> PS: 调用支付宝/微信SDK
PS --> GW: 返回支付参数
GW --> ORDER: 唤起支付

note over B: 用户完成支付

PS -> MQ: 支付成功回调
MQ -> TS: 更新订单状态
TS -> DB: UPDATE orders SET payment_status='PAID'
TS -> IS: 确认库存扣减
IS -> DB: UPDATE products SET status='SOLD'

TS -> MQ: 发送支付成功消息
MQ -> MS: 通知双方支付成功
MS -> S: 推送发货提醒
MS -> B: 推送支付成功

@enduml
```

3.4 搜索推荐流程图
------------------

```plantuml
@startuml 搜索推荐流程
skinparam activity {
  BackgroundColor<<Search>> #E3F2FD
  BackgroundColor<<Recommend>> #F3E5F5
  BackgroundColor<<Filter>> #E8F5E8
}

title 搜索推荐流程图

start

:用户输入搜索关键词 <<Search>>;

fork
  :关键词搜索 <<Search>>;
  :Elasticsearch查询;
  :返回搜索结果;
fork again
  :个性化推荐 <<Recommend>>;
  :用户画像分析;
  :推荐算法计算;
  :返回推荐结果;
fork again
  :位置推荐 <<Filter>>;
  :获取用户位置;
  :地理位置过滤;
  :返回附近商品;
end fork

:结果排序算法 <<Filter>>;
:价格筛选 <<Filter>>;
:分类筛选 <<Filter>>;
:时间筛选 <<Filter>>;

:合并排序结果;
:返回最终结果;

stop
@enduml
```

=====================================
4. 数据库设计
=====================================

4.1 数据库架构
--------------

数据存储方案：
- 主数据库：MySQL 8.0 (主从复制)
- 缓存数据库：Redis Cluster
- 文档数据库：MongoDB (存储商品详情、用户动态)
- 搜索引擎：Elasticsearch (商品搜索、用户搜索)
- 消息队列：RocketMQ (异步处理)

4.2 核心数据表设计（UML类图）
-----------------------------

```plantuml
@startuml 闲鱼数据库设计
skinparam class {
  BackgroundColor<<User>> #E3F2FD
  BackgroundColor<<Product>> #E8F5E8
  BackgroundColor<<Order>> #FFF3E0
  BackgroundColor<<Message>> #F3E5F5
}

title 闲鱼平台数据库设计 - UML类图

class User <<User>> {
  - user_id: bigint(20) PK
  - mobile: varchar(11) UK
  - username: varchar(50)
  - avatar: varchar(255)
  - gender: tinyint(1)
  - birthday: date
  - email: varchar(100)
  - real_name: varchar(20)
  - id_card: varchar(18)
  - credit_score: int(11)
  - level: tinyint(4)
  - status: tinyint(1)
  - created_at: datetime
  - updated_at: datetime
  + register()
  + login()
  + updateProfile()
  + getRealNameAuth()
}

class UserAddress <<User>> {
  - address_id: bigint(20) PK
  - user_id: bigint(20) FK
  - receiver_name: varchar(20)
  - receiver_mobile: varchar(11)
  - province: varchar(20)
  - city: varchar(20)
  - district: varchar(20)
  - detail_address: varchar(200)
  - is_default: tinyint(1)
  - created_at: datetime
  + addAddress()
  + updateAddress()
  + setDefault()
}

class Product <<Product>> {
  - product_id: bigint(20) PK
  - user_id: bigint(20) FK
  - title: varchar(100)
  - description: text
  - price: decimal(10,2)
  - original_price: decimal(10,2)
  - category_id: int(11) FK
  - brand: varchar(50)
  - condition: tinyint(1)
  - images: text
  - location: varchar(100)
  - latitude: decimal(10,6)
  - longitude: decimal(10,6)
  - view_count: int(11)
  - like_count: int(11)
  - status: tinyint(1)
  - created_at: datetime
  - updated_at: datetime
  + publish()
  + edit()
  + delete()
  + updateViewCount()
}

class Category <<Product>> {
  - category_id: int(11) PK
  - parent_id: int(11)
  - category_name: varchar(50)
  - category_icon: varchar(255)
  - sort_order: int(11)
  - status: tinyint(1)
  - created_at: datetime
  + addCategory()
  + updateCategory()
}

class ProductImage <<Product>> {
  - image_id: bigint(20) PK
  - product_id: bigint(20) FK
  - image_url: varchar(255)
  - image_order: tinyint(4)
  - created_at: datetime
  + uploadImage()
  + deleteImage()
}

class Order <<Order>> {
  - order_id: bigint(20) PK
  - order_no: varchar(32) UK
  - buyer_id: bigint(20) FK
  - seller_id: bigint(20) FK
  - product_id: bigint(20) FK
  - product_title: varchar(100)
  - product_image: varchar(255)
  - price: decimal(10,2)
  - quantity: int(11)
  - total_amount: decimal(10,2)
  - receiver_name: varchar(20)
  - receiver_mobile: varchar(11)
  - receiver_address: varchar(300)
  - order_status: tinyint(1)
  - payment_status: tinyint(1)
  - shipping_status: tinyint(1)
  - created_at: datetime
  - updated_at: datetime
  + createOrder()
  + cancelOrder()
  + payOrder()
  + confirmReceipt()
}

class Payment <<Order>> {
  - payment_id: bigint(20) PK
  - order_id: bigint(20) FK
  - payment_method: tinyint(1)
  - payment_amount: decimal(10,2)
  - payment_status: tinyint(1)
  - transaction_id: varchar(64)
  - payment_time: datetime
  - created_at: datetime
  + processPayment()
  + refund()
}

class Message <<Message>> {
  - message_id: bigint(20) PK
  - conversation_id: varchar(64)
  - sender_id: bigint(20) FK
  - receiver_id: bigint(20) FK
  - message_type: tinyint(1)
  - content: text
  - product_id: bigint(20) FK
  - is_read: tinyint(1)
  - created_at: datetime
  + sendMessage()
  + readMessage()
  + deleteMessage()
}

class Review <<Order>> {
  - review_id: bigint(20) PK
  - order_id: bigint(20) FK
  - reviewer_id: bigint(20) FK
  - reviewed_id: bigint(20) FK
  - product_id: bigint(20) FK
  - rating: tinyint(1)
  - content: text
  - images: text
  - created_at: datetime
  + addReview()
  + updateReview()
}

class UserFollow <<User>> {
  - follow_id: bigint(20) PK
  - follower_id: bigint(20) FK
  - followed_id: bigint(20) FK
  - created_at: datetime
  + follow()
  + unfollow()
}

class ProductFavorite <<Product>> {
  - favorite_id: bigint(20) PK
  - user_id: bigint(20) FK
  - product_id: bigint(20) FK
  - created_at: datetime
  + addFavorite()
  + removeFavorite()
}

' 关系定义
User ||--o{ UserAddress : "用户地址"
User ||--o{ Product : "发布商品"
User ||--o{ Order : "买家订单"
User ||--o{ Order : "卖家订单"
User ||--o{ Message : "发送消息"
User ||--o{ Message : "接收消息"
User ||--o{ Review : "评价者"
User ||--o{ Review : "被评价者"
User ||--o{ UserFollow : "关注者"
User ||--o{ UserFollow : "被关注者"
User ||--o{ ProductFavorite : "收藏商品"

Product }o--|| Category : "商品分类"
Product ||--o{ ProductImage : "商品图片"
Product ||--o{ Order : "订单商品"
Product ||--o{ Message : "商品消息"
Product ||--o{ Review : "商品评价"
Product ||--o{ ProductFavorite : "商品收藏"

Order ||--|| Payment : "订单支付"
Order ||--o{ Review : "订单评价"

Category ||--o{ Category : "父子分类"
@enduml
```

4.3 数据表详细设计
------------------

用户表 (users)
字段名              类型            约束        描述
user_id            bigint(20)      PK          用户ID
mobile             varchar(11)     UK,NOT NULL 手机号
username           varchar(50)     NOT NULL    用户名
password           varchar(255)    NOT NULL    密码(加密)
avatar             varchar(255)    NULL        头像URL
gender             tinyint(1)      DEFAULT 0   性别 0未知 1男 2女
birthday           date            NULL        生日
email              varchar(100)    NULL        邮箱
real_name          varchar(20)     NULL        真实姓名
id_card            varchar(18)     NULL        身份证号
credit_score       int(11)         DEFAULT 0   信用分
level              tinyint(4)      DEFAULT 1   用户等级
status             tinyint(1)      DEFAULT 1   状态 1正常 2禁用
created_at         datetime        NOT NULL    创建时间
updated_at         datetime        NOT NULL    更新时间

商品表 (products)
字段名              类型            约束        描述
product_id         bigint(20)      PK          商品ID
user_id            bigint(20)      FK,NOT NULL 发布者ID
title              varchar(100)    NOT NULL    商品标题
description        text            NULL        商品描述
price              decimal(10,2)   NOT NULL    价格
original_price     decimal(10,2)   NULL        原价
category_id        int(11)         FK,NOT NULL 分类ID
brand              varchar(50)     NULL        品牌
condition          tinyint(1)      DEFAULT 1   成色 1全新 2几乎全新 3轻微使用痕迹 4明显使用痕迹
images             text            NULL        图片URL数组(JSON)
location           varchar(100)    NULL        位置
latitude           decimal(10,6)   NULL        纬度
longitude          decimal(10,6)   NULL        经度
view_count         int(11)         DEFAULT 0   浏览次数
like_count         int(11)         DEFAULT 0   点赞次数
status             tinyint(1)      DEFAULT 1   状态 1在售 2已售 3下架 4删除
created_at         datetime        NOT NULL    创建时间
updated_at         datetime        NOT NULL    更新时间

订单表 (orders)
字段名              类型            约束        描述
order_id           bigint(20)      PK          订单ID
order_no           varchar(32)     UK,NOT NULL 订单号
buyer_id           bigint(20)      FK,NOT NULL 买家ID
seller_id          bigint(20)      FK,NOT NULL 卖家ID
product_id         bigint(20)      FK,NOT NULL 商品ID
product_title      varchar(100)    NOT NULL    商品标题
product_image      varchar(255)    NULL        商品图片
price              decimal(10,2)   NOT NULL    单价
quantity           int(11)         DEFAULT 1   数量
total_amount       decimal(10,2)   NOT NULL    总金额
receiver_name      varchar(20)     NOT NULL    收货人姓名
receiver_mobile    varchar(11)     NOT NULL    收货人手机
receiver_address   varchar(300)    NOT NULL    收货地址
order_status       tinyint(1)      DEFAULT 1   订单状态 1待支付 2已支付 3已发货 4已完成 5已取消
payment_status     tinyint(1)      DEFAULT 0   支付状态 0未支付 1已支付 2已退款
shipping_status    tinyint(1)      DEFAULT 0   物流状态 0未发货 1已发货 2运输中 3已送达
created_at         datetime        NOT NULL    创建时间
updated_at         datetime        NOT NULL    更新时间

消息表 (messages)
字段名              类型            约束        描述
message_id         bigint(20)      PK          消息ID
conversation_id    varchar(64)     NOT NULL    会话ID
sender_id          bigint(20)      FK,NOT NULL 发送者ID
receiver_id        bigint(20)      FK,NOT NULL 接收者ID
message_type       tinyint(1)      DEFAULT 1   消息类型 1文本 2图片 3语音 4商品卡片
content            text            NULL        消息内容
product_id         bigint(20)      FK,NULL     关联商品ID
is_read            tinyint(1)      DEFAULT 0   是否已读 0未读 1已读
created_at         datetime        NOT NULL    创建时间

=====================================
5. 系统架构图（PlantUML）
=====================================

5.1 微服务架构图
-----------------

```plantuml
@startuml 微服务架构图
skinparam component {
  BackgroundColor<<Gateway>> #FFE0B2
  BackgroundColor<<Service>> #E8F5E8
  BackgroundColor<<Storage>> #E3F2FD
  BackgroundColor<<Middleware>> #F3E5F5
}

title 闲鱼平台微服务架构图

cloud "客户端" {
  component "iOS APP" as iOS
  component "Android APP" as Android
  component "小程序" as MiniApp
  component "H5页面" as H5
}

package "网关层" {
  component "API网关" as Gateway <<Gateway>> {
    + 路由转发
    + 负载均衡
    + 限流熔断
    + 身份认证
    + 日志记录
  }
}

package "业务服务层" {
  component "用户服务" as UserService <<Service>> {
    + 用户注册登录
    + 用户信息管理
    + 实名认证
    + 信用评级
  }
  
  component "商品服务" as ProductService <<Service>> {
    + 商品发布
    + 商品管理
    + 分类管理
    + 商品审核
  }
  
  component "交易服务" as OrderService <<Service>> {
    + 订单管理
    + 交易流程
    + 订单状态
    + 售后处理
  }
  
  component "支付服务" as PaymentService <<Service>> {
    + 支付处理
    + 资金结算
    + 退款处理
    + 账户管理
  }
  
  component "消息服务" as MessageService <<Service>> {
    + 即时通讯
    + 消息推送
    + 通知管理
    + 消息存储
  }
  
  component "搜索服务" as SearchService <<Service>> {
    + 商品搜索
    + 用户搜索
    + 搜索推荐
    + 搜索统计
  }
  
  component "推荐服务" as RecommendService <<Service>> {
    + 个性化推荐
    + 协同过滤
    + 内容推荐
    + 位置推荐
  }
  
  component "文件服务" as FileService <<Service>> {
    + 图片上传
    + 文件存储
    + 图片处理
    + CDN分发
  }
  
  component "风控服务" as RiskService <<Service>> {
    + 内容审核
    + 反欺诈
    + 风险评估
    + 黑名单管理
  }
}

package "数据存储层" {
  database "MySQL集群" as MySQL <<Storage>> {
    + 用户数据
    + 商品数据
    + 订单数据
    + 交易数据
  }
  
  database "Redis集群" as Redis <<Storage>> {
    + 缓存数据
    + 会话数据
    + 热点数据
    + 分布式锁
  }
  
  database "MongoDB" as MongoDB <<Storage>> {
    + 商品详情
    + 用户动态
    + 日志数据
    + 文档数据
  }
  
  database "Elasticsearch" as ES <<Storage>> {
    + 商品索引
    + 用户索引
    + 搜索数据
    + 日志索引
  }
}

package "中间件层" {
  component "消息队列" as MQ <<Middleware>> {
    + 异步处理
    + 解耦服务
    + 流量削峰
    + 消息持久化
  }
  
  component "配置中心" as Config <<Middleware>> {
    + 配置管理
    + 动态配置
    + 环境隔离
    + 版本管理
  }
  
  component "注册中心" as Registry <<Middleware>> {
    + 服务注册
    + 服务发现
    + 健康检查
    + 负载均衡
  }
}

package "第三方服务" {
  component "支付宝" as Alipay
  component "微信支付" as WeChatPay
  component "短信服务" as SMS
  component "推送服务" as Push
  component "地图服务" as Map
}

' 连接关系
iOS --> Gateway
Android --> Gateway
MiniApp --> Gateway
H5 --> Gateway

Gateway --> UserService
Gateway --> ProductService
Gateway --> OrderService
Gateway --> PaymentService
Gateway --> MessageService
Gateway --> SearchService
Gateway --> RecommendService
Gateway --> FileService
Gateway --> RiskService

UserService --> MySQL
UserService --> Redis
ProductService --> MySQL
ProductService --> MongoDB
ProductService --> ES
OrderService --> MySQL
OrderService --> MQ
PaymentService --> MySQL
PaymentService --> Alipay
PaymentService --> WeChatPay
MessageService --> MongoDB
MessageService --> Redis
SearchService --> ES
SearchService --> Redis
RecommendService --> MongoDB
RecommendService --> Redis
FileService --> MongoDB
RiskService --> MySQL

UserService --> Config
UserService --> Registry
ProductService --> Config
ProductService --> Registry
OrderService --> Config
OrderService --> Registry
@enduml
```

5.2 技术选型架构图
------------------

```plantuml
@startuml 技术选型架构图
skinparam node {
  BackgroundColor<<Frontend>> #E3F2FD
  BackgroundColor<<Backend>> #E8F5E8
  BackgroundColor<<Database>> #FFF3E0
  BackgroundColor<<Middleware>> #F3E5F5
  BackgroundColor<<DevOps>> #FFEBEE
}

title 闲鱼平台技术选型架构图

package "前端技术栈" <<Frontend>> {
  node "移动端" {
    component "React Native"
    component "Flutter"
    component "原生开发"
  }
  
  node "Web端" {
    component "React"
    component "TypeScript"
    component "Ant Design"
  }
  
  node "小程序" {
    component "支付宝小程序"
    component "微信小程序"
  }
}

package "后端技术栈" <<Backend>> {
  node "开发语言" {
    component "Java 11"
    component "Node.js"
    component "Python"
  }
  
  node "框架" {
    component "Spring Boot"
    component "Spring Cloud"
    component "Express"
  }
  
  node "微服务" {
    component "Dubbo"
    component "Spring Cloud Alibaba"
    component "Nacos"
  }
}

package "数据存储" <<Database>> {
  node "关系型数据库" {
    component "MySQL 8.0"
    component "分库分表"
    component "读写分离"
  }
  
  node "NoSQL数据库" {
    component "Redis Cluster"
    component "MongoDB"
    component "Elasticsearch"
  }
  
  node "对象存储" {
    component "阿里云OSS"
    component "腾讯云COS"
    component "AWS S3"
  }
}

package "中间件" <<Middleware>> {
  node "消息队列" {
    component "Apache RocketMQ"
    component "Apache Kafka"
  }
  
  node "服务治理" {
    component "Nacos"
    component "Sentinel"
    component "Seata"
  }
  
  node "网关" {
    component "Spring Cloud Gateway"
    component "Nginx"
  }
}

package "DevOps" <<DevOps>> {
  node "容器化" {
    component "Docker"
    component "Kubernetes"
    component "Helm"
  }
  
  node "监控" {
    component "Prometheus"
    component "Grafana"
    component "Skywalking"
  }
  
  node "日志" {
    component "ELK Stack"
    component "Filebeat"
    component "Logstash"
  }
}

package "云服务" {
  node "阿里云" {
    component "ECS"
    component "RDS"
    component "OSS"
    component "CDN"
  }
}
@enduml
```

=====================================
6. 性能优化方案
=====================================

6.1 缓存策略
--------------

多级缓存架构：
- L1缓存：应用内存缓存 (Caffeine)
- L2缓存：Redis分布式缓存
- L3缓存：CDN边缘缓存
- L4缓存：浏览器本地缓存

缓存场景：
- 热点商品信息缓存
- 用户信息缓存
- 搜索结果缓存
- 分类数据缓存
- 推荐结果缓存

6.2 数据库优化
--------------

分库分表策略：
- 用户表：按user_id取模分表
- 商品表：按category_id分表
- 订单表：按时间分表
- 消息表：按conversation_id分表

读写分离：
- 主库：写操作
- 从库：读操作
- 中间件：Mycat/Sharding-JDBC

索引优化：
- 主键索引：所有表的主键
- 唯一索引：手机号、订单号等
- 普通索引：查询频繁的字段
- 复合索引：多条件查询优化

6.3 搜索优化
--------------

Elasticsearch优化：
- 索引分片策略
- 搜索结果缓存
- 搜索词分析器
- 相关性评分优化
- 搜索建议实现

6.4 图片优化
--------------

图片处理策略：
- 图片压缩：WebP格式
- 图片尺寸：多规格生成
- 图片加载：懒加载
- 图片缓存：CDN缓存
- 图片优化：智能裁剪

=====================================
7. 安全设计
=====================================

7.1 认证授权
--------------

认证方式：
- JWT Token认证
- OAuth2.0第三方登录
- 短信验证码验证
- 人脸识别验证
- 设备指纹识别

授权机制：
- RBAC权限模型
- API接口权限控制
- 数据权限控制
- 操作权限审计

7.2 数据安全
--------------

数据加密：
- 密码加密：BCrypt
- 敏感信息加密：AES-256
- 通信加密：HTTPS/TLS
- 数据库加密：透明数据加密

数据脱敏：
- 手机号脱敏：中间4位*
- 身份证号脱敏：中间12位*
- 银行卡号脱敏：中间8位*
- 地址信息脱敏：详细地址*

7.3 风控策略
--------------

实时风控：
- 设备风险识别
- 行为异常检测
- IP地址风险评估
- 账户风险评级

反欺诈：
- 虚假商品识别
- 刷单行为检测
- 恶意用户识别
- 风险交易拦截

内容安全：
- 敏感词过滤
- 图片内容审核
- 垃圾信息识别
- 违规内容检测

=====================================
8. 部署架构
=====================================

8.1 容器化部署
--------------

Docker化：
- 服务容器化
- 镜像版本管理
- 容器编排
- 资源限制

Kubernetes集群：
- 服务编排
- 自动扩缩容
- 负载均衡
- 故障自愈
- 滚动更新

8.2 环境管理
--------------

环境划分：
- 开发环境 (DEV)
- 测试环境 (TEST)
- 预发环境 (PRE)
- 生产环境 (PROD)

配置管理：
- 环境变量配置
- 配置文件管理
- 密钥管理
- 配置版本控制

8.3 监控体系
--------------

应用监控：
- 应用性能监控 (APM)
- 业务指标监控
- 错误日志监控
- 用户行为监控

基础设施监控：
- 服务器监控
- 数据库监控
- 网络监控
- 存储监控

日志管理：
- 日志收集：Filebeat
- 日志处理：Logstash
- 日志存储：Elasticsearch
- 日志分析：Kibana

8.4 灾备方案
--------------

数据备份：
- 数据库定期备份
- 增量备份策略
- 跨地域备份
- 备份恢复测试

高可用：
- 多机房部署
- 数据库主从同步
- 服务多实例部署
- 负载均衡配置

容灾恢复：
- 容灾预案
- 故障转移
- 数据恢复
- 业务连续性

=====================================
9. 开发流程
=====================================

9.1 研发流程
--------------

需求分析 → 技术设计 → 开发实现 → 单元测试 → 集成测试 → 系统测试 → 部署上线 → 运维监控

9.2 代码规范
--------------

编码规范：
- Java编码规范：阿里巴巴Java开发手册
- 数据库设计规范
- 接口设计规范
- 代码注释规范

版本控制：
- Git Flow工作流
- 分支管理策略
- 代码审查流程
- 版本发布流程

9.3 质量保证
--------------

测试策略：
- 单元测试：JUnit + Mockito
- 集成测试：TestNG
- 接口测试：Postman + Newman
- 性能测试：JMeter
- 自动化测试：Selenium

代码质量：
- 静态代码分析：SonarQube
- 代码覆盖率：JaCoCo
- 代码review：GitLab MR
- 持续集成：Jenkins/GitLab CI

=====================================
10. 总结
=====================================

本文档从业务功能、技术架构、数据设计、性能优化、安全保障等多个维度，
全面分析了闲鱼C2C平台的技术实现方案。

核心技术亮点：
1. 微服务架构设计，实现系统的高可用和可扩展性
2. 多级缓存策略，提升系统性能和用户体验
3. 分库分表方案，解决大数据量的存储和查询问题
4. 实时风控系统，保障平台交易安全
5. 容器化部署，提高运维效率和系统稳定性

该方案可以支撑千万级用户、亿级商品的C2C交易平台，
为二手交易提供稳定、安全、高效的技术支撑。

文档版本：v1.0
创建时间：2024年
更新时间：2024年 