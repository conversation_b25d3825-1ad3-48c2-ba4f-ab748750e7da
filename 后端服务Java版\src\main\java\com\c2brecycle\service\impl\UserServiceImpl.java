package com.c2brecycle.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.c2brecycle.common.exception.BusinessException;
import com.c2brecycle.common.result.ResultCode;
import com.c2brecycle.dto.AddressDTO;
import com.c2brecycle.dto.FavoriteDTO;
import com.c2brecycle.dto.UpdateUserDTO;
import com.c2brecycle.entity.Favorite;
import com.c2brecycle.entity.User;
import com.c2brecycle.entity.UserAddress;
import com.c2brecycle.mapper.FavoriteMapper;
import com.c2brecycle.mapper.UserAddressMapper;
import com.c2brecycle.mapper.UserMapper;
import com.c2brecycle.service.UserService;
import com.c2brecycle.vo.FavoriteVO;
import com.c2brecycle.vo.UserProfileVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户服务实现类
 * 
 * <AUTHOR> Recycle Team
 * @since 2024-01-15
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserServiceImpl implements UserService {

    private final UserMapper userMapper;
    private final UserAddressMapper userAddressMapper;
    private final FavoriteMapper favoriteMapper;

    @Override
    public UserProfileVO getUserProfile(Long userId) {
        log.info("获取用户信息，userId: {}", userId);
        
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new BusinessException(ResultCode.USER_NOT_FOUND);
        }
        
        UserProfileVO profile = new UserProfileVO();
        BeanUtils.copyProperties(user, profile);
        
        return profile;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateUserProfile(Long userId, UpdateUserDTO updateDTO) {
        log.info("更新用户信息，userId: {}", userId);
        
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new BusinessException(ResultCode.USER_NOT_FOUND);
        }
        
        // 更新用户信息
        if (updateDTO.getNickname() != null) {
            user.setNickname(updateDTO.getNickname());
        }
        if (updateDTO.getAvatar() != null) {
            user.setAvatar(updateDTO.getAvatar());
        }
        if (updateDTO.getPhone() != null) {
            user.setPhone(updateDTO.getPhone());
        }
        if (updateDTO.getCity() != null) {
            user.setCity(updateDTO.getCity());
        }
        
        user.setUpdatedAt(LocalDateTime.now());
        userMapper.updateById(user);
    }

    @Override
    public List<UserAddress> getUserAddresses(Long userId) {
        log.info("获取用户地址列表，userId: {}", userId);
        
        LambdaQueryWrapper<UserAddress> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserAddress::getUserId, userId)
               .orderByDesc(UserAddress::getIsDefault)
               .orderByDesc(UserAddress::getCreatedAt);
        
        return userAddressMapper.selectList(wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addUserAddress(Long userId, AddressDTO addressDTO) {
        log.info("添加用户地址，userId: {}", userId);
        
        UserAddress address = new UserAddress();
        BeanUtils.copyProperties(addressDTO, address);
        address.setUserId(userId);
        address.setCreatedAt(LocalDateTime.now());
        address.setUpdatedAt(LocalDateTime.now());
        
        // 如果设置为默认地址，先取消其他默认地址
        if (addressDTO.getIsDefault() != null && addressDTO.getIsDefault()) {
            userAddressMapper.clearDefaultAddress(userId);
            address.setIsDefault(1);
        } else {
            address.setIsDefault(0);
        }
        
        userAddressMapper.insert(address);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateUserAddress(Long userId, Long addressId, AddressDTO addressDTO) {
        log.info("更新用户地址，userId: {}, addressId: {}", userId, addressId);
        
        UserAddress address = userAddressMapper.selectById(addressId);
        if (address == null || !address.getUserId().equals(userId)) {
            throw new BusinessException(ResultCode.ADDRESS_NOT_FOUND);
        }
        
        BeanUtils.copyProperties(addressDTO, address);
        address.setUpdatedAt(LocalDateTime.now());
        
        // 如果设置为默认地址，先取消其他默认地址
        if (addressDTO.getIsDefault() != null && addressDTO.getIsDefault()) {
            userAddressMapper.clearDefaultAddress(userId);
            address.setIsDefault(1);
        }
        
        userAddressMapper.updateById(address);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteUserAddress(Long userId, Long addressId) {
        log.info("删除用户地址，userId: {}, addressId: {}", userId, addressId);
        
        UserAddress address = userAddressMapper.selectById(addressId);
        if (address == null || !address.getUserId().equals(userId)) {
            throw new BusinessException(ResultCode.ADDRESS_NOT_FOUND);
        }
        
        userAddressMapper.deleteById(addressId);
    }

    @Override
    public IPage<FavoriteVO> getUserFavorites(Long userId, Integer type, Integer page, Integer size) {
        log.info("获取用户收藏，userId: {}, type: {}, page: {}, size: {}", userId, type, page, size);
        
        Page<FavoriteVO> pageParam = new Page<>(page, size);
        
        return favoriteMapper.selectUserFavorites(pageParam, userId, type);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addFavorite(Long userId, FavoriteDTO favoriteDTO) {
        log.info("添加收藏，userId: {}, type: {}, targetId: {}", 
                userId, favoriteDTO.getType(), favoriteDTO.getTargetId());
        
        // 检查是否已收藏
        LambdaQueryWrapper<Favorite> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Favorite::getUserId, userId)
               .eq(Favorite::getType, favoriteDTO.getType())
               .eq(Favorite::getTargetId, favoriteDTO.getTargetId());
        
        Favorite existingFavorite = favoriteMapper.selectOne(wrapper);
        if (existingFavorite != null) {
            throw new BusinessException(ResultCode.FAVORITE_ALREADY_EXISTS);
        }
        
        // 添加收藏
        Favorite favorite = new Favorite();
        favorite.setUserId(userId);
        favorite.setType(favoriteDTO.getType());
        favorite.setTargetId(favoriteDTO.getTargetId());
        favorite.setCreatedAt(LocalDateTime.now());
        
        favoriteMapper.insert(favorite);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeFavorite(Long userId, Long favoriteId) {
        log.info("取消收藏，userId: {}, favoriteId: {}", userId, favoriteId);
        
        Favorite favorite = favoriteMapper.selectById(favoriteId);
        if (favorite == null || !favorite.getUserId().equals(userId)) {
            throw new BusinessException(ResultCode.FAVORITE_NOT_FOUND);
        }
        
        favoriteMapper.deleteById(favoriteId);
    }
}
