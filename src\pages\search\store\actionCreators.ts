import { getSearchSongData } from '../../../request/header'
import * as actionTypes from './actionTypes'

// 改变歌曲列表Action
const changeSongListAction = (songs: any) => ({
  type: actionTypes.CHANGE_SEARCH_SONG_LIST,
  songs
})

// 改变歌手列表
const changeSingerListAction = (singerList: any) => ({
  type: actionTypes.CHANGE_SINGER_LIST,
  singerList: singerList
})

// 搜索歌曲列表network
export const getSearchSongListAction = (songName: any, limit: any, offset = 1 , type = 1) => {
  return (dispatch: any) => {
    getSearchSongData(songName, limit, (offset - 1) * 30, type).then((res: any) => {
      const songs = res && res.result.songs
      dispatch(changeSongListAction(songs))
    })
  } 
}

// 搜索歌手列表network
export const getSearchSingerListAction = (song: any, limit: any, type: any) => {
  return (dispatch: any) => {
    getSearchSongData(song, 20, type).then((res: any) => {
      const singerList = res.result && res.result.artists
      console.log(singerList)
      dispatch(changeSingerListAction(singerList))
    })
  }
}