package com.c2brecycle.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 收藏信息VO
 * 
 * <AUTHOR> Recycle Team
 * @since 2024-01-15
 */
@Data
@ApiModel("收藏信息")
public class FavoriteVO {

    @ApiModelProperty("收藏ID")
    private Long id;

    @ApiModelProperty("收藏类型 1-站点 2-产品")
    private Integer type;

    @ApiModelProperty("目标ID")
    private Long targetId;

    @ApiModelProperty("目标名称")
    private String targetName;

    @ApiModelProperty("目标图片")
    private String targetImage;

    @ApiModelProperty("目标描述")
    private String targetDescription;

    @ApiModelProperty("额外信息")
    private String extraInfo;

    @ApiModelProperty("价格信息(产品类型)")
    private BigDecimal price;

    @ApiModelProperty("评分信息(站点类型)")
    private BigDecimal rating;

    @ApiModelProperty("收藏时间")
    private LocalDateTime createdAt;
}
