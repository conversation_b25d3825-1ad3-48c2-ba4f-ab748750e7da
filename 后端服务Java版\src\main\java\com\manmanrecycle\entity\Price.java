package com.manmanrecycle.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 产品价格实体类
 * 
 * <AUTHOR> Team
 * @since 2024-01-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("prices")
public class Price implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 价格ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 产品ID
     */
    @TableField("product_id")
    private Long productId;

    /**
     * 站点ID
     */
    @TableField("station_id")
    private Long stationId;

    /**
     * 最低价
     */
    @TableField("min_price")
    private BigDecimal minPrice;

    /**
     * 最高价
     */
    @TableField("max_price")
    private BigDecimal maxPrice;

    /**
     * 单位
     */
    @TableField("unit")
    private String unit;

    /**
     * 日期
     */
    @TableField("date")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate date;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 逻辑删除标记
     */
    @TableLogic
    @TableField("deleted")
    private Integer deleted;
}
