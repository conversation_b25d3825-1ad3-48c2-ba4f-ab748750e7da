body {
  background-color: rgb(245,245,245);
}

.radioCategoryBox {
    width: 980px;
    margin: 0 auto;
    background-color: rgb(255,255,255);
    display: flex;
    align-items: center;
    border: 1px solid #d3d3d3;
    border-width: 0 1px;
    padding: 20px 0 0;
}
.djRadioArrow {
  width: 20px;
  height: 30px;
  background-image: url(../../../../static/images/radio_slide.png);
  cursor: pointer;
}
.arrowLeft {
  margin-left: 15px;
  background-position: 0 -30px;
}
.arrowRight {
  margin-right: 15px;
  background-position: -30px -30px;
}

.radioCategoryContent {
  flex: 1;
  width: 900px;
  margin: 20px 0;
}
.radioCategoryPage {
  display: flex !important;
  flex-wrap: wrap;
  padding-bottom: 20px;
}
.radioCategoryItem {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 10px 20px;
  width: 70px;
  height: 70px;
  font-size: 12px;
  cursor: pointer;
  border-radius: 5px;
  text-align: center;
  border: 2px solid transparent;
}
.radioCategoryItem:hover {
  background-color: #eee;
}
.radioCategoryPage .active {
  color: #C20C0C;
  border: 2px solid #d35757;
}
.radioCategoryIcon{
  width: 48px;
  height: 48px;
}
.active .radioCategoryIcon{
  background-position: -48px 0;
}

.changeDot {
  bottom: 5px!important;
}
.changeDot li {
  display: flex!important;
  align-items: center!important;
}
.changeDot li button {
  width: 6px!important;
  height: 6px!important;
  border-radius: 50%!important;
  background-color: #aaa!important;
}
.changeDot .slick-active {
  width: 20px!important;
}
.changeDot .slick-active button {
  background-color: #C20C0C!important;
}

