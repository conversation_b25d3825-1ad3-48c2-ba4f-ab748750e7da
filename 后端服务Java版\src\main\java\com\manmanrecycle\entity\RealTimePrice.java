package com.manmanrecycle.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 实时价格实体类
 * 
 * <AUTHOR> Team
 * @since 2024-01-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("real_time_prices")
public class RealTimePrice implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 价格ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 产品ID
     */
    @TableField("product_id")
    private Long productId;

    /**
     * 站点ID(NULL表示市场价)
     */
    @TableField("station_id")
    private Long stationId;

    /**
     * 价格类型 1-收购价 2-零售价 3-批发价
     */
    @TableField("price_type")
    private Integer priceType;

    /**
     * 当前价格
     */
    @TableField("current_price")
    private BigDecimal currentPrice;

    /**
     * 最低价
     */
    @TableField("min_price")
    private BigDecimal minPrice;

    /**
     * 最高价
     */
    @TableField("max_price")
    private BigDecimal maxPrice;

    /**
     * 单位
     */
    @TableField("unit")
    private String unit;

    /**
     * 品质等级
     */
    @TableField("quality_grade")
    private String qualityGrade;

    /**
     * 价格来源
     */
    @TableField("price_source")
    private String priceSource;

    /**
     * 价格可信度 0-1
     */
    @TableField("confidence_score")
    private BigDecimal confidenceScore;

    /**
     * 最后更新时间
     */
    @TableField("last_updated")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastUpdated;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 逻辑删除标记
     */
    @TableLogic
    @TableField("deleted")
    private Integer deleted;
}
