package com.c2crestore.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.c2crestore.dto.SearchProductDTO;
import com.c2crestore.vo.ProductDetailVO;
import com.c2crestore.vo.ProductVO;

import java.util.List;

/**
 * 产品服务接口
 * 
 * <AUTHOR> Restore Team
 * @since 2024-01-15
 */
public interface ProductService {

    /**
     * 搜索产品
     * 
     * @param searchDTO 搜索参数
     * @return 产品列表
     */
    IPage<ProductVO> searchProducts(SearchProductDTO searchDTO);

    /**
     * 获取产品详情
     * 
     * @param productId 产品ID
     * @param userId 用户ID（可选）
     * @return 产品详情
     */
    ProductDetailVO getProductDetail(Long productId, Long userId);

    /**
     * 获取热门产品
     * 
     * @param categoryId 分类ID
     * @param limit 数量限制
     * @return 热门产品列表
     */
    List<ProductVO> getHotProducts(Long categoryId, Integer limit);

    /**
     * 获取产品价格信息
     * 
     * @param productId 产品ID
     * @param stationId 站点ID
     * @param city 城市
     * @return 价格信息
     */
    Object getProductPrices(Long productId, Long stationId, String city);
}
