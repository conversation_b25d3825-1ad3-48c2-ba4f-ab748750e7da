import { Artists } from '../../models/artist';
import { MusicDetail } from '../../models/music';
import { getSizeImage } from '../../utils/formatUtils'
import './style.css'


type AlbumCoverProps = {
  info: MusicDetail;
  children?: string;
  size?: number;
  width?: number;
  bgp?: string;
};


export default function AlbumCover(props: AlbumCoverProps) {
  const { info, size = 130 ,width = 153, bgp = -845 } = props
  return (
    
    <div className='albumItem' style = {{width:width}}>
      <div className="albumIcon" style = {{width:width, height:size}}>
        <img src={getSizeImage(info.picUrl, size)} alt="" />
        <a href={`#/album?albumId=${info.id}`} className='albumCover' style={{backgroundPositionY: bgp}}></a>
      </div>
      <div className="albumInfo">
        <a className='albumName textNowrap' href={`#/album?albumId=${info.id}`}>{info.name}</a>
        <div className="albumNickNameBox textNowrap">
          {
            info.artists.map((item: Artists) => {
              return <a key={item.id} className='albumNickName' href={`#/artist?artistId=${item.id}`}>{item.name}</a>
            })
          }
        </div>
      </div>
    </div>
  )
}
