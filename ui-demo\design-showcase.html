<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>慢慢回收小程序 UI 设计展示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            color: #333;
        }

        .showcase-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .showcase-header {
            text-align: center;
            margin-bottom: 40px;
            background: white;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }

        .showcase-header h1 {
            color: #52C41A;
            font-size: 3em;
            margin-bottom: 15px;
            background: linear-gradient(135deg, #52C41A, #73D13D);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .showcase-header p {
            color: #666;
            font-size: 1.3em;
            margin-bottom: 20px;
        }

        .design-stats {
            display: flex;
            justify-content: center;
            gap: 40px;
            margin-top: 20px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 2em;
            font-weight: 700;
            color: #52C41A;
            display: block;
        }

        .stat-label {
            font-size: 0.9em;
            color: #666;
            margin-top: 5px;
        }

        .design-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .design-card {
            background: white;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .design-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .card-header {
            background: linear-gradient(135deg, #52C41A, #73D13D);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .card-title {
            font-size: 1.5em;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .card-subtitle {
            opacity: 0.9;
            font-size: 0.9em;
        }

        .card-content {
            padding: 30px;
        }

        .feature-list {
            list-style: none;
            margin: 0;
            padding: 0;
        }

        .feature-list li {
            padding: 12px 0;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .feature-list li:last-child {
            border-bottom: none;
        }

        .feature-icon {
            font-size: 1.2em;
            width: 24px;
        }

        .feature-text {
            flex: 1;
            color: #333;
        }

        .color-palette {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .color-item {
            text-align: center;
        }

        .color-swatch {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            margin: 0 auto 10px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .color-name {
            font-size: 0.9em;
            font-weight: 500;
            margin-bottom: 5px;
        }

        .color-code {
            font-size: 0.8em;
            color: #666;
            font-family: monospace;
        }

        .ui-preview {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }

        .phone-preview {
            width: 200px;
            height: 400px;
            background: white;
            border-radius: 20px;
            margin: 0 auto;
            box-shadow: 0 8px 24px rgba(0,0,0,0.15);
            border: 4px solid #333;
            position: relative;
            overflow: hidden;
        }

        .preview-header {
            background: #52C41A;
            color: white;
            padding: 10px;
            font-size: 0.8em;
            text-align: center;
        }

        .preview-content {
            padding: 15px;
            height: calc(100% - 80px);
            overflow: hidden;
        }

        .preview-nav {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            border-top: 1px solid #f0f0f0;
            display: flex;
            padding: 8px 0;
        }

        .preview-nav-item {
            flex: 1;
            text-align: center;
            font-size: 0.7em;
            color: #666;
        }

        .preview-nav-item.active {
            color: #52C41A;
        }

        .tech-specs {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
        }

        .spec-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .spec-item {
            background: white;
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.06);
        }

        .spec-icon {
            font-size: 2em;
            margin-bottom: 10px;
            display: block;
        }

        .spec-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: #333;
        }

        .spec-desc {
            font-size: 0.9em;
            color: #666;
        }

        .cta-section {
            background: linear-gradient(135deg, #52C41A, #73D13D);
            color: white;
            padding: 50px;
            border-radius: 20px;
            text-align: center;
            margin-top: 40px;
        }

        .cta-title {
            font-size: 2.5em;
            font-weight: 700;
            margin-bottom: 20px;
        }

        .cta-desc {
            font-size: 1.2em;
            opacity: 0.9;
            margin-bottom: 30px;
        }

        .cta-button {
            background: white;
            color: #52C41A;
            padding: 15px 40px;
            border: none;
            border-radius: 50px;
            font-size: 1.1em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .cta-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.2);
        }

        @media (max-width: 768px) {
            .design-grid {
                grid-template-columns: 1fr;
            }
            
            .design-stats {
                flex-direction: column;
                gap: 20px;
            }
            
            .showcase-header h1 {
                font-size: 2em;
            }
            
            .cta-title {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="showcase-container">
        <header class="showcase-header">
            <h1>🌿 慢慢回收小程序</h1>
            <p>专业的废品回收信息服务平台 UI 设计展示</p>
            <div class="design-stats">
                <div class="stat-item">
                    <span class="stat-number">12</span>
                    <span class="stat-label">功能模块</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">5</span>
                    <span class="stat-label">核心页面</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">1</span>
                    <span class="stat-label">AI核心功能</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">100%</span>
                    <span class="stat-label">环保理念</span>
                </div>
            </div>
        </header>

        <div class="design-grid">
            <!-- 设计理念卡片 -->
            <div class="design-card">
                <div class="card-header">
                    <div class="card-title">🎨 设计理念</div>
                    <div class="card-subtitle">环保绿色 + 现代简约</div>
                </div>
                <div class="card-content">
                    <ul class="feature-list">
                        <li>
                            <span class="feature-icon">🌿</span>
                            <span class="feature-text">环保导向的视觉设计</span>
                        </li>
                        <li>
                            <span class="feature-icon">📱</span>
                            <span class="feature-text">现代简约的界面风格</span>
                        </li>
                        <li>
                            <span class="feature-icon">🎯</span>
                            <span class="feature-text">信息清晰的层次结构</span>
                        </li>
                        <li>
                            <span class="feature-icon">⚡</span>
                            <span class="feature-text">操作便捷的交互体验</span>
                        </li>
                        <li>
                            <span class="feature-icon">🔒</span>
                            <span class="feature-text">专业可信的品牌形象</span>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- 色彩系统卡片 -->
            <div class="design-card">
                <div class="card-header">
                    <div class="card-title">🎨 色彩系统</div>
                    <div class="card-subtitle">环保绿色主题配色</div>
                </div>
                <div class="card-content">
                    <div class="color-palette">
                        <div class="color-item">
                            <div class="color-swatch" style="background: #52C41A;"></div>
                            <div class="color-name">主绿色</div>
                            <div class="color-code">#52C41A</div>
                        </div>
                        <div class="color-item">
                            <div class="color-swatch" style="background: #73D13D;"></div>
                            <div class="color-name">浅绿色</div>
                            <div class="color-code">#73D13D</div>
                        </div>
                        <div class="color-item">
                            <div class="color-swatch" style="background: #1890FF;"></div>
                            <div class="color-name">信息蓝</div>
                            <div class="color-code">#1890FF</div>
                        </div>
                        <div class="color-item">
                            <div class="color-swatch" style="background: #FAAD14;"></div>
                            <div class="color-name">警告黄</div>
                            <div class="color-code">#FAAD14</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 核心功能卡片 -->
            <div class="design-card">
                <div class="card-header">
                    <div class="card-title">🚀 核心功能</div>
                    <div class="card-subtitle">AI智能路线规划</div>
                </div>
                <div class="card-content">
                    <ul class="feature-list">
                        <li>
                            <span class="feature-icon">🤖</span>
                            <span class="feature-text">AI智能路线生成</span>
                        </li>
                        <li>
                            <span class="feature-icon">🎯</span>
                            <span class="feature-text">个性化推荐算法</span>
                        </li>
                        <li>
                            <span class="feature-icon">🧭</span>
                            <span class="feature-text">实时导航优化</span>
                        </li>
                        <li>
                            <span class="feature-icon">📊</span>
                            <span class="feature-text">效率统计分析</span>
                        </li>
                        <li>
                            <span class="feature-icon">💰</span>
                            <span class="feature-text">收益最大化</span>
                        </li>
                    </ul>
                    <div class="ui-preview">
                        <div class="phone-preview">
                            <div class="preview-header">🤖 AI智能路线规划</div>
                            <div class="preview-content">
                                <div style="font-size: 0.7em; margin-bottom: 10px;">
                                    📅 今日路线规划<br>
                                    🕐 预计用时: 2小时30分钟<br>
                                    💰 预计收益: ¥180-220<br>
                                    📏 总距离: 12.5公里
                                </div>
                                <div style="background: #f6ffed; padding: 8px; border-radius: 6px; font-size: 0.6em; margin: 10px 0;">
                                    🗺️ 智能路线预览<br>
                                    🏠 → 📍1 → 📍2 → 📍3 → 🏠
                                </div>
                                <div style="font-size: 0.6em; color: #666;">
                                    🤖 AI优化建议<br>
                                    • 建议09:00出发，避开早高峰<br>
                                    • 路线可节省30%时间
                                </div>
                            </div>
                            <div class="preview-nav">
                                <div class="preview-nav-item">🏠</div>
                                <div class="preview-nav-item">📂</div>
                                <div class="preview-nav-item active">🤖</div>
                                <div class="preview-nav-item">📈</div>
                                <div class="preview-nav-item">👤</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 页面架构卡片 -->
            <div class="design-card">
                <div class="card-header">
                    <div class="card-title">📱 页面架构</div>
                    <div class="card-subtitle">5个核心页面设计</div>
                </div>
                <div class="card-content">
                    <ul class="feature-list">
                        <li>
                            <span class="feature-icon">🏠</span>
                            <span class="feature-text">首页 - 地图为核心</span>
                        </li>
                        <li>
                            <span class="feature-icon">📂</span>
                            <span class="feature-text">分类 - 产品分类展示</span>
                        </li>
                        <li>
                            <span class="feature-icon">🤖</span>
                            <span class="feature-text">AI路线 - 智能规划</span>
                        </li>
                        <li>
                            <span class="feature-icon">📈</span>
                            <span class="feature-text">行情 - 价格趋势</span>
                        </li>
                        <li>
                            <span class="feature-icon">👤</span>
                            <span class="feature-text">我的 - 个人中心</span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 技术规格 -->
        <div class="tech-specs">
            <h2 style="text-align: center; margin-bottom: 30px; color: #333;">🔧 技术规格</h2>
            <div class="spec-grid">
                <div class="spec-item">
                    <span class="spec-icon">📱</span>
                    <div class="spec-title">响应式设计</div>
                    <div class="spec-desc">适配多种屏幕尺寸</div>
                </div>
                <div class="spec-item">
                    <span class="spec-icon">🎨</span>
                    <div class="spec-title">组件化设计</div>
                    <div class="spec-desc">可复用的UI组件库</div>
                </div>
                <div class="spec-item">
                    <span class="spec-icon">⚡</span>
                    <div class="spec-title">流畅动效</div>
                    <div class="spec-desc">丰富的交互动画</div>
                </div>
                <div class="spec-item">
                    <span class="spec-icon">🔍</span>
                    <div class="spec-title">无障碍设计</div>
                    <div class="spec-desc">考虑特殊用户需求</div>
                </div>
            </div>
        </div>

        <!-- 行动号召 -->
        <div class="cta-section">
            <div class="cta-title">🌟 体验完整UI设计</div>
            <div class="cta-desc">
                点击下方按钮，体验"慢慢回收"小程序的完整交互式UI设计演示
            </div>
            <a href="index.html" class="cta-button">🚀 立即体验</a>
        </div>
    </div>

    <script>
        // 添加滚动动画
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // 初始化动画
        document.addEventListener('DOMContentLoaded', () => {
            const cards = document.querySelectorAll('.design-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                card.style.transition = 'all 0.6s ease';
                card.style.transitionDelay = `${index * 0.1}s`;
                observer.observe(card);
            });

            // 统计数字动画
            const statNumbers = document.querySelectorAll('.stat-number');
            statNumbers.forEach(stat => {
                const finalValue = stat.textContent;
                if (!isNaN(parseInt(finalValue))) {
                    let currentValue = 0;
                    const increment = parseInt(finalValue) / 50;
                    const timer = setInterval(() => {
                        currentValue += increment;
                        if (currentValue >= parseInt(finalValue)) {
                            stat.textContent = finalValue;
                            clearInterval(timer);
                        } else {
                            stat.textContent = Math.floor(currentValue);
                        }
                    }, 30);
                }
            });
        });
    </script>
</body>
</html>
