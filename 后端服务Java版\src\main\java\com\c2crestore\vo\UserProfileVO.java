package com.c2crestore.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户信息VO
 * 
 * <AUTHOR> Restore Team
 * @since 2024-01-15
 */
@Data
@ApiModel("用户信息")
public class UserProfileVO {

    @ApiModelProperty("用户ID")
    private Long id;

    @ApiModelProperty("昵称")
    private String nickname;

    @ApiModelProperty("头像")
    private String avatar;

    @ApiModelProperty("手机号")
    private String phone;

    @ApiModelProperty("城市")
    private String city;

    @ApiModelProperty("状态 0-禁用 1-正常")
    private Integer status;

    @ApiModelProperty("地址数量")
    private Integer addressCount;

    @ApiModelProperty("收藏数量")
    private Integer favoriteCount;

    @ApiModelProperty("注册时间")
    private LocalDateTime createdAt;

    @ApiModelProperty("最后更新时间")
    private LocalDateTime updatedAt;
}
