const WebSocket = require('ws');
const jwt = require('jsonwebtoken');
const logger = require('../utils/logger');
const db = require('../config/database');

/**
 * WebSocket实时价格推送服务
 */
class WebSocketService {
  constructor() {
    this.wss = null;
    this.clients = new Map(); // 存储客户端连接信息
    this.subscriptions = new Map(); // 存储订阅信息
    this.heartbeatInterval = null;
  }

  /**
   * 初始化WebSocket服务器
   * @param {Object} server - HTTP服务器实例
   */
  initialize(server) {
    this.wss = new WebSocket.Server({
      server,
      path: '/ws/prices',
      verifyClient: this.verifyClient.bind(this)
    });

    this.wss.on('connection', this.handleConnection.bind(this));
    this.startHeartbeat();

    logger.info('WebSocket价格推送服务已启动');
  }

  /**
   * 验证客户端连接
   * @param {Object} info - 连接信息
   * @returns {boolean} 是否允许连接
   */
  verifyClient(info) {
    try {
      const url = new URL(info.req.url, `http://${info.req.headers.host}`);
      const token = url.searchParams.get('token');

      if (!token) {
        logger.warn('WebSocket连接缺少token');
        return false;
      }

      // 验证JWT token
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      info.req.userId = decoded.userId;
      
      return true;
    } catch (error) {
      logger.error('WebSocket token验证失败:', error);
      return false;
    }
  }

  /**
   * 处理新的WebSocket连接
   * @param {WebSocket} ws - WebSocket连接
   * @param {Object} req - 请求对象
   */
  handleConnection(ws, req) {
    const clientId = this.generateClientId();
    const userId = req.userId;

    // 存储客户端信息
    this.clients.set(clientId, {
      ws,
      userId,
      subscriptions: new Set(),
      lastPing: Date.now(),
      isAlive: true
    });

    logger.info('WebSocket客户端连接:', { clientId, userId });

    // 设置消息处理
    ws.on('message', (data) => {
      this.handleMessage(clientId, data);
    });

    // 设置连接关闭处理
    ws.on('close', () => {
      this.handleDisconnection(clientId);
    });

    // 设置错误处理
    ws.on('error', (error) => {
      logger.error('WebSocket连接错误:', { clientId, error });
    });

    // 设置心跳响应
    ws.on('pong', () => {
      const client = this.clients.get(clientId);
      if (client) {
        client.isAlive = true;
        client.lastPing = Date.now();
      }
    });

    // 发送连接成功消息
    this.sendMessage(clientId, {
      type: 'connection',
      data: {
        clientId,
        message: '连接成功',
        timestamp: Date.now()
      }
    });
  }

  /**
   * 处理客户端消息
   * @param {string} clientId - 客户端ID
   * @param {Buffer} data - 消息数据
   */
  handleMessage(clientId, data) {
    try {
      const message = JSON.parse(data.toString());
      const client = this.clients.get(clientId);

      if (!client) {
        logger.warn('收到未知客户端消息:', clientId);
        return;
      }

      switch (message.action) {
        case 'subscribe':
          this.handleSubscribe(clientId, message);
          break;
        case 'unsubscribe':
          this.handleUnsubscribe(clientId, message);
          break;
        case 'ping':
          this.handlePing(clientId);
          break;
        default:
          logger.warn('未知消息类型:', message.action);
      }

    } catch (error) {
      logger.error('处理WebSocket消息失败:', error);
    }
  }

  /**
   * 处理订阅请求
   * @param {string} clientId - 客户端ID
   * @param {Object} message - 订阅消息
   */
  handleSubscribe(clientId, message) {
    const client = this.clients.get(clientId);
    if (!client) return;

    const { channels = [] } = message;

    for (const channel of channels) {
      if (this.isValidChannel(channel)) {
        client.subscriptions.add(channel);
        
        // 添加到全局订阅映射
        if (!this.subscriptions.has(channel)) {
          this.subscriptions.set(channel, new Set());
        }
        this.subscriptions.get(channel).add(clientId);

        logger.info('客户端订阅频道:', { clientId, channel });
      }
    }

    // 发送订阅确认
    this.sendMessage(clientId, {
      type: 'subscribe_ack',
      data: {
        channels: Array.from(client.subscriptions),
        timestamp: Date.now()
      }
    });
  }

  /**
   * 处理取消订阅请求
   * @param {string} clientId - 客户端ID
   * @param {Object} message - 取消订阅消息
   */
  handleUnsubscribe(clientId, message) {
    const client = this.clients.get(clientId);
    if (!client) return;

    const { channels = [] } = message;

    for (const channel of channels) {
      client.subscriptions.delete(channel);
      
      // 从全局订阅映射中移除
      if (this.subscriptions.has(channel)) {
        this.subscriptions.get(channel).delete(clientId);
        
        // 如果没有客户端订阅该频道，删除频道
        if (this.subscriptions.get(channel).size === 0) {
          this.subscriptions.delete(channel);
        }
      }

      logger.info('客户端取消订阅频道:', { clientId, channel });
    }

    // 发送取消订阅确认
    this.sendMessage(clientId, {
      type: 'unsubscribe_ack',
      data: {
        channels: Array.from(client.subscriptions),
        timestamp: Date.now()
      }
    });
  }

  /**
   * 处理心跳请求
   * @param {string} clientId - 客户端ID
   */
  handlePing(clientId) {
    this.sendMessage(clientId, {
      type: 'pong',
      data: {
        timestamp: Date.now()
      }
    });
  }

  /**
   * 处理客户端断开连接
   * @param {string} clientId - 客户端ID
   */
  handleDisconnection(clientId) {
    const client = this.clients.get(clientId);
    if (!client) return;

    // 清理订阅
    for (const channel of client.subscriptions) {
      if (this.subscriptions.has(channel)) {
        this.subscriptions.get(channel).delete(clientId);
        
        if (this.subscriptions.get(channel).size === 0) {
          this.subscriptions.delete(channel);
        }
      }
    }

    // 移除客户端
    this.clients.delete(clientId);

    logger.info('WebSocket客户端断开连接:', { clientId, userId: client.userId });
  }

  /**
   * 发送消息给指定客户端
   * @param {string} clientId - 客户端ID
   * @param {Object} message - 消息内容
   */
  sendMessage(clientId, message) {
    const client = this.clients.get(clientId);
    if (!client || client.ws.readyState !== WebSocket.OPEN) {
      return false;
    }

    try {
      client.ws.send(JSON.stringify(message));
      return true;
    } catch (error) {
      logger.error('发送WebSocket消息失败:', { clientId, error });
      return false;
    }
  }

  /**
   * 广播价格更新
   * @param {Object} priceUpdate - 价格更新数据
   */
  broadcastPriceUpdate(priceUpdate) {
    const { productId, stationId, newPrice, oldPrice, changeRate } = priceUpdate;

    // 构建频道名称
    const channels = [
      `product.${productId}.price`,
      `market.price_updates`
    ];

    if (stationId) {
      channels.push(`station.${stationId}.price`);
    }

    // 构建消息
    const message = {
      type: 'price_update',
      data: {
        productId,
        stationId,
        newPrice,
        oldPrice,
        changeRate,
        timestamp: Date.now()
      }
    };

    // 广播到相关频道
    for (const channel of channels) {
      this.broadcastToChannel(channel, message);
    }
  }

  /**
   * 广播价格预警
   * @param {Object} alert - 预警数据
   */
  broadcastPriceAlert(alert) {
    const { userId, productId, alertType, message: alertMessage } = alert;

    const message = {
      type: 'price_alert',
      data: {
        productId,
        alertType,
        message: alertMessage,
        timestamp: Date.now()
      }
    };

    // 发送给特定用户
    this.sendToUser(userId, message);

    // 也广播到市场预警频道
    this.broadcastToChannel('market.alerts', message);
  }

  /**
   * 广播市场趋势更新
   * @param {Object} trendData - 趋势数据
   */
  broadcastMarketTrend(trendData) {
    const message = {
      type: 'market_trend',
      data: {
        ...trendData,
        timestamp: Date.now()
      }
    };

    this.broadcastToChannel('market.trends', message);
  }

  /**
   * 向指定频道广播消息
   * @param {string} channel - 频道名称
   * @param {Object} message - 消息内容
   */
  broadcastToChannel(channel, message) {
    const subscribers = this.subscriptions.get(channel);
    if (!subscribers || subscribers.size === 0) {
      return;
    }

    const messageWithChannel = {
      channel,
      ...message
    };

    let successCount = 0;
    for (const clientId of subscribers) {
      if (this.sendMessage(clientId, messageWithChannel)) {
        successCount++;
      }
    }

    logger.debug('频道广播完成:', { 
      channel, 
      subscribers: subscribers.size, 
      success: successCount 
    });
  }

  /**
   * 向指定用户发送消息
   * @param {number} userId - 用户ID
   * @param {Object} message - 消息内容
   */
  sendToUser(userId, message) {
    let sentCount = 0;
    
    for (const [clientId, client] of this.clients) {
      if (client.userId === userId) {
        if (this.sendMessage(clientId, message)) {
          sentCount++;
        }
      }
    }

    logger.debug('用户消息发送完成:', { userId, sentCount });
  }

  /**
   * 验证频道名称是否有效
   * @param {string} channel - 频道名称
   * @returns {boolean} 是否有效
   */
  isValidChannel(channel) {
    const validPatterns = [
      /^product\.\d+\.price$/,
      /^station\.\d+\.price$/,
      /^category\.\d+\.trend$/,
      /^market\.(alerts|trends|price_updates)$/,
      /^user\.\d+\.notifications$/
    ];

    return validPatterns.some(pattern => pattern.test(channel));
  }

  /**
   * 生成客户端ID
   * @returns {string} 客户端ID
   */
  generateClientId() {
    return `client_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 启动心跳检测
   */
  startHeartbeat() {
    this.heartbeatInterval = setInterval(() => {
      const now = Date.now();
      
      for (const [clientId, client] of this.clients) {
        if (!client.isAlive || (now - client.lastPing) > 60000) {
          // 超过1分钟没有响应，断开连接
          logger.info('心跳超时，断开客户端:', clientId);
          client.ws.terminate();
          this.handleDisconnection(clientId);
        } else {
          // 发送心跳
          client.isAlive = false;
          client.ws.ping();
        }
      }
    }, 30000); // 每30秒检查一次
  }

  /**
   * 停止心跳检测
   */
  stopHeartbeat() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
  }

  /**
   * 获取连接统计信息
   * @returns {Object} 统计信息
   */
  getStats() {
    return {
      totalClients: this.clients.size,
      totalSubscriptions: this.subscriptions.size,
      channels: Array.from(this.subscriptions.keys()),
      clientsByUser: this.getClientsByUser()
    };
  }

  /**
   * 按用户分组获取客户端数量
   * @returns {Object} 用户客户端映射
   */
  getClientsByUser() {
    const userClients = {};
    
    for (const client of this.clients.values()) {
      const userId = client.userId;
      userClients[userId] = (userClients[userId] || 0) + 1;
    }

    return userClients;
  }

  /**
   * 关闭WebSocket服务
   */
  close() {
    this.stopHeartbeat();
    
    if (this.wss) {
      this.wss.close();
    }

    // 断开所有客户端连接
    for (const client of this.clients.values()) {
      client.ws.close();
    }

    this.clients.clear();
    this.subscriptions.clear();

    logger.info('WebSocket价格推送服务已关闭');
  }
}

module.exports = new WebSocketService();
