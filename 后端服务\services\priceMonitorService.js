const cron = require('node-cron');
const axios = require('axios');
const logger = require('../utils/logger');
const db = require('../config/database');
const priceService = require('./priceService');
const websocketService = require('./websocketService');

/**
 * 价格监控和同步服务
 */
class PriceMonitorService {
  constructor() {
    this.tasks = new Map();
    this.isRunning = false;
    this.dataSources = this.initializeDataSources();
  }

  /**
   * 初始化数据源配置
   */
  initializeDataSources() {
    return [
      {
        id: 'official_market',
        name: '官方市场价',
        url: process.env.OFFICIAL_MARKET_API,
        weight: 0.4,
        updateFrequency: '0 */1 * * *', // 每小时
        reliability: 0.95,
        enabled: true
      },
      {
        id: 'station_reports',
        name: '站点上报价',
        weight: 0.3,
        updateFrequency: '*/5 * * * *', // 每5分钟
        reliability: 0.85,
        enabled: true
      },
      {
        id: 'crawler_data',
        name: '爬虫数据',
        url: process.env.CRAWLER_API,
        weight: 0.2,
        updateFrequency: '0 */4 * * *', // 每4小时
        reliability: 0.60,
        enabled: true
      },
      {
        id: 'user_feedback',
        name: '用户反馈价',
        weight: 0.1,
        updateFrequency: '*/10 * * * *', // 每10分钟
        reliability: 0.70,
        enabled: true
      }
    ];
  }

  /**
   * 启动价格监控服务
   */
  start() {
    if (this.isRunning) {
      logger.warn('价格监控服务已在运行');
      return;
    }

    this.isRunning = true;

    // 启动各种定时任务
    this.startPriceUpdateTasks();
    this.startPriceAnalysisTasks();
    this.startAlertCheckTasks();
    this.startDataCleanupTasks();

    logger.info('价格监控服务已启动');
  }

  /**
   * 停止价格监控服务
   */
  stop() {
    if (!this.isRunning) {
      return;
    }

    // 停止所有定时任务
    for (const [taskName, task] of this.tasks) {
      task.stop();
      logger.info(`定时任务已停止: ${taskName}`);
    }

    this.tasks.clear();
    this.isRunning = false;

    logger.info('价格监控服务已停止');
  }

  /**
   * 启动价格更新任务
   */
  startPriceUpdateTasks() {
    // 官方市场价格同步
    this.tasks.set('official_market_sync', cron.schedule('0 */1 * * *', async () => {
      await this.syncOfficialMarketPrices();
    }, { scheduled: false }));

    // 站点价格聚合
    this.tasks.set('station_price_aggregate', cron.schedule('*/5 * * * *', async () => {
      await this.aggregateStationPrices();
    }, { scheduled: false }));

    // 爬虫数据同步
    this.tasks.set('crawler_data_sync', cron.schedule('0 */4 * * *', async () => {
      await this.syncCrawlerData();
    }, { scheduled: false }));

    // 用户反馈价格处理
    this.tasks.set('user_feedback_process', cron.schedule('*/10 * * * *', async () => {
      await this.processUserFeedbackPrices();
    }, { scheduled: false }));

    // 启动所有价格更新任务
    for (const [taskName, task] of this.tasks) {
      if (taskName.includes('sync') || taskName.includes('aggregate') || taskName.includes('process')) {
        task.start();
        logger.info(`价格更新任务已启动: ${taskName}`);
      }
    }
  }

  /**
   * 启动价格分析任务
   */
  startPriceAnalysisTasks() {
    // 价格趋势分析
    this.tasks.set('price_trend_analysis', cron.schedule('0 */2 * * *', async () => {
      await this.analyzePriceTrends();
    }, { scheduled: false }));

    // 市场热度分析
    this.tasks.set('market_heat_analysis', cron.schedule('0 */6 * * *', async () => {
      await this.analyzeMarketHeat();
    }, { scheduled: false }));

    // 价格异常检测
    this.tasks.set('price_anomaly_detection', cron.schedule('*/15 * * * *', async () => {
      await this.detectPriceAnomalies();
    }, { scheduled: false }));

    // 启动分析任务
    for (const [taskName, task] of this.tasks) {
      if (taskName.includes('analysis') || taskName.includes('detection')) {
        task.start();
        logger.info(`价格分析任务已启动: ${taskName}`);
      }
    }
  }

  /**
   * 启动预警检查任务
   */
  startAlertCheckTasks() {
    // 价格预警检查
    this.tasks.set('price_alert_check', cron.schedule('*/1 * * * *', async () => {
      await this.checkPriceAlerts();
    }, { scheduled: false }));

    // 波动预警检查
    this.tasks.set('volatility_alert_check', cron.schedule('*/5 * * * *', async () => {
      await this.checkVolatilityAlerts();
    }, { scheduled: false }));

    // 启动预警任务
    for (const [taskName, task] of this.tasks) {
      if (taskName.includes('alert')) {
        task.start();
        logger.info(`预警检查任务已启动: ${taskName}`);
      }
    }
  }

  /**
   * 启动数据清理任务
   */
  startDataCleanupTasks() {
    // 历史数据清理
    this.tasks.set('history_data_cleanup', cron.schedule('0 2 * * *', async () => {
      await this.cleanupHistoryData();
    }, { scheduled: false }));

    // 缓存清理
    this.tasks.set('cache_cleanup', cron.schedule('0 */12 * * *', async () => {
      await this.cleanupCache();
    }, { scheduled: false }));

    // 启动清理任务
    for (const [taskName, task] of this.tasks) {
      if (taskName.includes('cleanup')) {
        task.start();
        logger.info(`数据清理任务已启动: ${taskName}`);
      }
    }
  }

  /**
   * 同步官方市场价格
   */
  async syncOfficialMarketPrices() {
    try {
      logger.info('开始同步官方市场价格');

      const officialSource = this.dataSources.find(s => s.id === 'official_market');
      if (!officialSource || !officialSource.enabled || !officialSource.url) {
        logger.warn('官方市场价格源未配置或已禁用');
        return;
      }

      // 调用官方API获取价格数据
      const response = await axios.get(officialSource.url, {
        timeout: 30000,
        headers: {
          'Authorization': `Bearer ${process.env.OFFICIAL_API_TOKEN}`,
          'User-Agent': 'ManManRecycle/1.0'
        }
      });

      const priceData = response.data;
      if (!priceData || !priceData.prices) {
        logger.warn('官方API返回数据格式错误');
        return;
      }

      // 批量更新价格
      const updateData = {
        source: 'official_market',
        timestamp: new Date(),
        prices: priceData.prices.map(item => ({
          productId: item.product_id,
          stationId: null, // 官方价格不关联特定站点
          priceType: 1,
          price: item.price,
          volume: item.volume || 0,
          qualityGrade: item.quality_grade,
          changeReason: '官方市场价格更新'
        }))
      };

      const result = await priceService.batchUpdatePrices(updateData);
      
      // 广播价格更新
      for (const priceUpdate of result.results) {
        if (priceUpdate.updated) {
          websocketService.broadcastPriceUpdate(priceUpdate);
        }
      }

      logger.info('官方市场价格同步完成:', { 
        updatedCount: result.updatedCount 
      });

    } catch (error) {
      logger.error('同步官方市场价格失败:', error);
    }
  }

  /**
   * 聚合站点价格
   */
  async aggregateStationPrices() {
    try {
      logger.info('开始聚合站点价格');

      // 获取所有活跃站点的最新价格
      const sql = `
        SELECT 
          rtp.product_id,
          AVG(rtp.current_price) as avg_price,
          MIN(rtp.current_price) as min_price,
          MAX(rtp.current_price) as max_price,
          COUNT(*) as station_count,
          SUM(ph.volume) as total_volume
        FROM real_time_prices rtp
        LEFT JOIN price_history ph ON rtp.product_id = ph.product_id 
          AND ph.recorded_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
        WHERE rtp.station_id IS NOT NULL 
          AND rtp.last_updated >= DATE_SUB(NOW(), INTERVAL 2 HOUR)
        GROUP BY rtp.product_id
        HAVING station_count >= 2
      `;

      const aggregatedData = await db.query(sql);

      for (const data of aggregatedData) {
        // 更新市场平均价格
        await db.query(`
          INSERT INTO real_time_prices 
          (product_id, station_id, price_type, current_price, min_price, max_price, 
           unit, price_source, confidence_score, last_updated)
          VALUES (?, NULL, 1, ?, ?, ?, 'kg', 'station_aggregate', ?, NOW())
          ON DUPLICATE KEY UPDATE
          current_price = VALUES(current_price),
          min_price = VALUES(min_price),
          max_price = VALUES(max_price),
          confidence_score = VALUES(confidence_score),
          last_updated = VALUES(last_updated)
        `, [
          data.product_id,
          data.avg_price,
          data.min_price,
          data.max_price,
          Math.min(0.9, 0.5 + (data.station_count * 0.1)) // 基于站点数量计算置信度
        ]);

        // 记录价格历史
        await db.insert('price_history', {
          product_id: data.product_id,
          station_id: null,
          price_type: 1,
          price: data.avg_price,
          volume: data.total_volume || 0,
          change_reason: '站点价格聚合',
          recorded_at: new Date()
        });
      }

      logger.info('站点价格聚合完成:', { 
        processedProducts: aggregatedData.length 
      });

    } catch (error) {
      logger.error('聚合站点价格失败:', error);
    }
  }

  /**
   * 检查价格预警
   */
  async checkPriceAlerts() {
    try {
      // 获取所有活跃的价格预警
      const alerts = await db.query(`
        SELECT 
          pa.*,
          rtp.current_price,
          p.name as product_name
        FROM price_alerts pa
        JOIN real_time_prices rtp ON pa.product_id = rtp.product_id AND rtp.station_id IS NULL
        JOIN products p ON pa.product_id = p.id
        WHERE pa.status = 1 AND pa.is_triggered = 0
      `);

      for (const alert of alerts) {
        const currentPrice = alert.current_price;
        let shouldTrigger = false;
        let alertMessage = '';

        // 获取价格变化率
        const changeData = await priceService.calculatePriceChange(alert.product_id, 1);

        switch (alert.alert_type) {
          case 1: // 价格上涨
            shouldTrigger = changeData.changeRate >= alert.threshold_rate;
            alertMessage = `${alert.product_name}价格上涨${changeData.changeRate.toFixed(1)}%，当前价格${currentPrice}元`;
            break;
          case 2: // 价格下跌
            shouldTrigger = changeData.changeRate <= -alert.threshold_rate;
            alertMessage = `${alert.product_name}价格下跌${Math.abs(changeData.changeRate).toFixed(1)}%，当前价格${currentPrice}元`;
            break;
          case 3: // 达到目标价
            shouldTrigger = currentPrice >= alert.target_price;
            alertMessage = `${alert.product_name}价格达到目标价${alert.target_price}元，当前价格${currentPrice}元`;
            break;
        }

        if (shouldTrigger) {
          // 触发预警
          await db.update('price_alerts', {
            is_triggered: 1,
            current_price: currentPrice,
            triggered_at: new Date()
          }, 'id = ?', [alert.id]);

          // 发送WebSocket通知
          websocketService.broadcastPriceAlert({
            userId: alert.user_id,
            productId: alert.product_id,
            alertType: alert.alert_type,
            message: alertMessage
          });

          logger.info('价格预警触发:', {
            alertId: alert.id,
            userId: alert.user_id,
            productId: alert.product_id,
            message: alertMessage
          });
        }
      }

    } catch (error) {
      logger.error('检查价格预警失败:', error);
    }
  }

  /**
   * 检测价格异常
   */
  async detectPriceAnomalies() {
    try {
      logger.info('开始检测价格异常');

      // 获取最近更新的价格
      const recentPrices = await db.query(`
        SELECT 
          rtp.*,
          p.name as product_name
        FROM real_time_prices rtp
        JOIN products p ON rtp.product_id = p.id
        WHERE rtp.last_updated >= DATE_SUB(NOW(), INTERVAL 15 MINUTE)
      `);

      for (const price of recentPrices) {
        // 获取历史价格用于异常检测
        const historicalPrices = await db.query(`
          SELECT price 
          FROM price_history 
          WHERE product_id = ? 
            AND station_id = ? 
            AND recorded_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
          ORDER BY recorded_at DESC
          LIMIT 50
        `, [price.product_id, price.station_id]);

        if (historicalPrices.length < 10) {
          continue; // 数据不足，跳过检测
        }

        const prices = historicalPrices.map(h => h.price);
        const avgPrice = prices.reduce((sum, p) => sum + p, 0) / prices.length;
        const deviation = Math.abs(price.current_price - avgPrice) / avgPrice;

        // 如果价格偏差超过30%，记录为异常
        if (deviation > 0.3) {
          logger.warn('检测到价格异常:', {
            productId: price.product_id,
            productName: price.product_name,
            stationId: price.station_id,
            currentPrice: price.current_price,
            avgPrice: avgPrice.toFixed(2),
            deviation: (deviation * 100).toFixed(1) + '%'
          });

          // 可以在这里添加异常处理逻辑，如：
          // 1. 降低该价格的置信度
          // 2. 发送异常通知
          // 3. 标记为需要人工审核
        }
      }

    } catch (error) {
      logger.error('检测价格异常失败:', error);
    }
  }

  /**
   * 清理历史数据
   */
  async cleanupHistoryData() {
    try {
      logger.info('开始清理历史数据');

      // 删除90天前的价格历史记录
      const deletedRows = await db.query(`
        DELETE FROM price_history 
        WHERE recorded_at < DATE_SUB(NOW(), INTERVAL 90 DAY)
      `);

      // 删除已触发且超过30天的价格预警
      const deletedAlerts = await db.query(`
        DELETE FROM price_alerts 
        WHERE is_triggered = 1 
          AND triggered_at < DATE_SUB(NOW(), INTERVAL 30 DAY)
      `);

      logger.info('历史数据清理完成:', {
        deletedHistoryRows: deletedRows.affectedRows || 0,
        deletedAlerts: deletedAlerts.affectedRows || 0
      });

    } catch (error) {
      logger.error('清理历史数据失败:', error);
    }
  }

  /**
   * 获取服务状态
   */
  getStatus() {
    return {
      isRunning: this.isRunning,
      activeTasks: Array.from(this.tasks.keys()),
      dataSources: this.dataSources.map(source => ({
        id: source.id,
        name: source.name,
        enabled: source.enabled,
        weight: source.weight,
        reliability: source.reliability
      }))
    };
  }
}

module.exports = new PriceMonitorService();
