import * as actionTypes from './actionTypes'
import { getToplistInfo, getToplistDetail } from '../../../../request/toplist'

// 改变榜单数据Action
const changeToplistAction = (toplistInfo: any) => ({
  type: actionTypes.CHANGE_TOPLIST_COUNT,
  toplistInfo,
})

// 改变当前索引Action
export const changeCurrentIndexAction = (index: any) => ({
  type: actionTypes.CHANGE_CURRENT_INDEX,
  index,
})

// 改变当前歌单的ID_Action
export const changeCurrentToplistIdAction = (id: any) => ({
  type: actionTypes.CHANGE_CURRENT_TOPLIST_ID,
  id,
})

// 改变榜单标题详情Action
const changeToplistTitleInfo = (titleInfo: any) => ({
  type: actionTypes.CHANGE_CURRENT_TOPLIST_TITLE_INFO,
  titleInfo,
})

// 改变不同榜单列表Action
const changeCurrentToplist = (toplist: any) => ({
  type: actionTypes.CHANGE_CURRENT_TOPLIST,
  toplist,
})

// 榜单network
export const getToplistInfoAction = () => {
  return (dispatch: any) => {
    getToplistInfo().then((res: any) => {
      dispatch(changeToplistAction(res.list))
    })
  }
}

// 榜单标题信息 network
export const getToplistTitleInfoAction = (id: any) => {
  return (dispatch: any) => {
    getToplistDetail(id).then((res: any) => {
      // 取出榜单标题详情信息
      const {
        coverImgUrl,
        name,
        trackNumberUpdateTime,
        playCount,
        subscribedCount,
        commentCount,
        shareCount,
      } = res && res.playlist
      const toplistTitleInfo = {
        coverImgUrl,
        name,
        trackNumberUpdateTime,
        playCount,
        subscribedCount,
        commentCount,
        shareCount,
      }
      dispatch(changeToplistTitleInfo(toplistTitleInfo))
    })
  }
}

// 榜单列表详情信息 network
export const getToplistItemAction = (id: any) => {
  return (dispatch: any) => {
    getToplistDetail(id).then((res: any) => {
        // 榜单列表详情信息
      const currentToplist = res && res.playlist && res.playlist.tracks
      dispatch(changeCurrentToplist(currentToplist))
    })
  }
}
