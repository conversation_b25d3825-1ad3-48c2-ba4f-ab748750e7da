import {
  AUTH_LOGIN_REQUEST,
  AUTH_LOGIN_SUCCESS,
  AUTH_LOGIN_FAILURE,
  AUTH_LOGOUT,
  AUTH_REFRESH_TOKEN_REQUEST,
  AUTH_REFRESH_TOKEN_SUCCESS,
  AUTH_REFRESH_TOKEN_FAILURE,
  AUTH_VERIFY_TOKEN_REQUEST,
  AUTH_VERIFY_TOKEN_SUCCESS,
  AUTH_VERIFY_TOKEN_FAILURE
} from '../actions/authActions';

const initialState = {
  // 用户信息
  user: null,
  
  // 认证状态
  isAuthenticated: false,
  token: localStorage.getItem('token'),
  refreshToken: localStorage.getItem('refreshToken'),
  
  // 加载状态
  loading: false,
  
  // 错误信息
  error: null,
  
  // 其他状态
  isNewUser: false,
  expiresIn: null
};

const authReducer = (state = initialState, action) => {
  switch (action.type) {
    case AUTH_LOGIN_REQUEST:
    case AUTH_REFRESH_TOKEN_REQUEST:
    case AUTH_VERIFY_TOKEN_REQUEST:
      return {
        ...state,
        loading: true,
        error: null
      };

    case AUTH_LOGIN_SUCCESS:
      return {
        ...state,
        loading: false,
        isAuthenticated: true,
        user: action.payload.user,
        token: action.payload.token,
        refreshToken: action.payload.refreshToken,
        isNewUser: action.payload.isNewUser,
        expiresIn: action.payload.expiresIn,
        error: null
      };

    case AUTH_REFRESH_TOKEN_SUCCESS:
      return {
        ...state,
        loading: false,
        token: action.payload.token,
        refreshToken: action.payload.refreshToken,
        expiresIn: action.payload.expiresIn,
        error: null
      };

    case AUTH_VERIFY_TOKEN_SUCCESS:
      return {
        ...state,
        loading: false,
        isAuthenticated: true,
        error: null
      };

    case AUTH_LOGIN_FAILURE:
    case AUTH_REFRESH_TOKEN_FAILURE:
    case AUTH_VERIFY_TOKEN_FAILURE:
      return {
        ...state,
        loading: false,
        error: action.payload
      };

    case AUTH_LOGOUT:
      return {
        ...initialState,
        token: null,
        refreshToken: null
      };

    default:
      return state;
  }
};

export default authReducer;
