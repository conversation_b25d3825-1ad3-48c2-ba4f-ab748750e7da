package com.c2brecycle.controller;

import com.c2brecycle.common.result.Result;
import com.c2brecycle.dto.RefreshTokenDTO;
import com.c2brecycle.dto.WechatLoginDTO;
import com.c2brecycle.service.AuthService;
import com.c2brecycle.vo.LoginVO;
import io.swagger.annotations.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

/**
 * 认证控制器
 * 
 * <AUTHOR> Recycle Team
 * @since 2024-01-15
 */
@Slf4j
@RestController
@RequestMapping("/auth")
@RequiredArgsConstructor
@Api(tags = "🔐 认证模块", description = "用户认证相关接口")
public class AuthController {

    private final AuthService authService;

    /**
     * 微信小程序登录
     */
    @PostMapping("/wechat-login")
    @ApiOperation(value = "微信小程序登录", notes = "通过微信授权码进行用户登录，返回JWT令牌")
    @ApiResponses({
        @ApiResponse(code = 200, message = "登录成功"),
        @ApiResponse(code = 400, message = "请求参数错误"),
        @ApiResponse(code = 3001, message = "微信授权码无效"),
        @ApiResponse(code = 3002, message = "微信API调用失败")
    })
    public Result<LoginVO> wechatLogin(
            @ApiParam(value = "微信登录请求参数", required = true)
            @Valid @RequestBody WechatLoginDTO loginDTO) {
        log.info("微信登录请求，code: {}", loginDTO.getCode());
        
        LoginVO loginVO = authService.wechatLogin(loginDTO);
        
        return Result.success("登录成功", loginVO);
    }

    /**
     * 刷新Token
     */
    @PostMapping("/refresh-token")
    @ApiOperation(value = "刷新访问令牌", notes = "使用刷新令牌获取新的访问令牌")
    @ApiResponses({
        @ApiResponse(code = 200, message = "Token刷新成功"),
        @ApiResponse(code = 400, message = "请求参数错误"),
        @ApiResponse(code = 2001, message = "Token无效"),
        @ApiResponse(code = 2002, message = "Token已过期")
    })
    public Result<LoginVO> refreshToken(
            @ApiParam(value = "刷新Token请求参数", required = true)
            @Valid @RequestBody RefreshTokenDTO refreshTokenDTO) {
        log.info("刷新Token请求");
        
        LoginVO loginVO = authService.refreshToken(refreshTokenDTO.getRefreshToken());
        
        return Result.success("Token刷新成功", loginVO);
    }

    /**
     * 用户登出
     */
    @PostMapping("/logout")
    @ApiOperation(value = "用户登出", notes = "清除用户登录状态，使Token失效")
    @ApiImplicitParam(name = "Authorization", value = "Bearer Token", required = true, dataType = "string", paramType = "header")
    @ApiResponses({
        @ApiResponse(code = 200, message = "登出成功"),
        @ApiResponse(code = 401, message = "未授权访问")
    })
    public Result<Void> logout(HttpServletRequest request) {
        Long userId = (Long) request.getAttribute("userId");
        log.info("用户登出请求，userId: {}", userId);
        
        authService.logout(userId);
        
        return Result.success("登出成功");
    }

    /**
     * 验证Token
     */
    @GetMapping("/verify")
    @ApiOperation(value = "验证Token有效性", notes = "验证当前Token是否有效")
    @ApiImplicitParam(name = "Authorization", value = "Bearer Token", required = true, dataType = "string", paramType = "header")
    @ApiResponses({
        @ApiResponse(code = 200, message = "Token验证成功"),
        @ApiResponse(code = 401, message = "Token无效或已过期")
    })
    public Result<Object> verifyToken(HttpServletRequest request) {
        Long userId = (Long) request.getAttribute("userId");
        
        return Result.success("Token验证成功", new Object() {
            public final Long userId = (Long) request.getAttribute("userId");
            public final boolean valid = true;
        });
    }
}
