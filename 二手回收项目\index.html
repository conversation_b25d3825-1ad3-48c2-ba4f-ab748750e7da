<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>慢慢回收 - 专业的废品回收信息服务平台</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>🌿</text></svg>">
</head>
<body>
    <div class="demo-container">
        <!-- 项目介绍头部 -->
        <header class="project-header">
            <div class="header-content">
                <div class="logo-section">
                    <div class="logo">🌿</div>
                    <div class="brand-info">
                        <h1>慢慢回收</h1>
                        <p>专业的废品回收信息服务平台</p>
                    </div>
                </div>
                <div class="project-stats">
                    <div class="stat-item">
                        <span class="stat-number">12</span>
                        <span class="stat-label">功能模块</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">5</span>
                        <span class="stat-label">核心页面</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">1</span>
                        <span class="stat-label">AI核心功能</span>
                    </div>
                </div>
            </div>
        </header>

        <!-- 导航菜单 -->
        <nav class="demo-nav">
            <button class="nav-btn active" onclick="showPage('home')" data-page="home">
                🏠 首页地图
            </button>
            <button class="nav-btn" onclick="showPage('category')" data-page="category">
                📂 分类查询
            </button>
            <button class="nav-btn" onclick="showPage('ai-route')" data-page="ai-route">
                🤖 AI路线规划
            </button>
            <button class="nav-btn" onclick="showPage('market')" data-page="market">
                📈 价格行情
            </button>
            <button class="nav-btn" onclick="showPage('profile')" data-page="profile">
                👤 个人中心
            </button>
        </nav>

        <!-- 页面容器 -->
        <div class="pages-container">
            <!-- 首页 -->
            <div id="home" class="page active">
                <div class="phone-mockup">
                    <div class="phone-header">
                        <span class="location">📍 北京市朝阳区</span>
                        <div class="header-actions">
                            <span class="search-btn">🔍 搜索</span>
                            <span class="filter-btn">☰ 筛选</span>
                        </div>
                    </div>

                    <div class="map-container">
                        <div class="map-placeholder">
                            <div class="map-bg">🗺️ 地图显示区域</div>
                            <div class="map-marker" style="top: 30%; left: 20%;">📍</div>
                            <div class="map-marker" style="top: 50%; left: 60%;">📍</div>
                            <div class="map-marker" style="top: 70%; left: 40%;">📍</div>
                            <div class="map-label">绿色回收站</div>
                            <div class="map-label" style="top: 55%; left: 65%;">环保回收中心</div>
                            <div class="map-label" style="top: 75%; left: 45%;">蓝天回收点</div>
                        </div>
                    </div>

                    <div class="quick-actions">
                        <div class="action-card" onclick="showPage('category')">
                            <div class="action-icon">📱</div>
                            <div class="action-text">快速查价</div>
                        </div>
                        <div class="action-card highlight" onclick="showPage('ai-route')">
                            <div class="action-icon">🤖</div>
                            <div class="action-text">AI路线</div>
                        </div>
                        <div class="action-card">
                            <div class="action-icon">📚</div>
                            <div class="action-text">回收指南</div>
                        </div>
                    </div>

                    <div class="station-list">
                        <h3>📋 附近回收站点</h3>
                        <div class="station-card" onclick="showStationDetail('绿色回收站')">
                            <div class="station-header">
                                <span class="station-name">🏪 绿色回收站</span>
                                <div class="station-meta">
                                    <span class="rating">⭐ 4.8</span>
                                    <span class="distance">📍 500m</span>
                                </div>
                            </div>
                            <div class="station-info">
                                <div class="contact">📞 138****8888</div>
                                <div class="status open">🕐 营业中</div>
                            </div>
                            <div class="station-services">
                                <span class="service-tag">💰 电脑配件</span>
                                <span class="service-tag">🔩 金属</span>
                                <span class="service-tag">📄 纸类</span>
                            </div>
                        </div>

                        <div class="station-card" onclick="showStationDetail('环保回收中心')">
                            <div class="station-header">
                                <span class="station-name">🏪 环保回收中心</span>
                                <div class="station-meta">
                                    <span class="rating">⭐ 4.5</span>
                                    <span class="distance">📍 800m</span>
                                </div>
                            </div>
                            <div class="station-info">
                                <div class="contact">📞 139****9999</div>
                                <div class="status open">🕐 营业中</div>
                            </div>
                            <div class="station-services">
                                <span class="service-tag">📱 手机数码</span>
                                <span class="service-tag">🔩 金属类</span>
                                <span class="service-tag">🥤 塑料</span>
                            </div>
                        </div>
                    </div>

                    <div class="bottom-nav">
                        <div class="nav-item active" onclick="showPage('home')">
                            <div class="nav-icon">🏠</div>
                            <div class="nav-label">首页</div>
                        </div>
                        <div class="nav-item" onclick="showPage('category')">
                            <div class="nav-icon">📂</div>
                            <div class="nav-label">分类</div>
                        </div>
                        <div class="nav-item" onclick="showPage('market')">
                            <div class="nav-icon">📈</div>
                            <div class="nav-label">行情</div>
                        </div>
                        <div class="nav-item" onclick="showPage('profile')">
                            <div class="nav-icon">👤</div>
                            <div class="nav-label">我的</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 分类页 -->
            <div id="category" class="page">
                <div class="phone-mockup">
                    <div class="page-header">
                        <h2>废品分类回收</h2>
                    </div>

                    <div class="search-box">
                        <input type="text" placeholder="🔍 搜索产品型号或名称..." class="search-input">
                    </div>

                    <div class="section">
                        <h3>🔥 热门分类</h3>
                        <div class="category-grid">
                            <div class="category-card" onclick="showCategoryDetail('电脑配件')">
                                <div class="category-icon">💻</div>
                                <div class="category-name">电脑配件</div>
                                <div class="category-price">45-200元</div>
                                <div class="category-trend up">📈 +5%</div>
                            </div>
                            <div class="category-card" onclick="showCategoryDetail('手机数码')">
                                <div class="category-icon">📱</div>
                                <div class="category-name">手机数码</div>
                                <div class="category-price">20-800元</div>
                                <div class="category-trend down">📉 -2%</div>
                            </div>
                            <div class="category-card" onclick="showCategoryDetail('金属类')">
                                <div class="category-icon">🔩</div>
                                <div class="category-name">金属类</div>
                                <div class="category-price">2-45元/kg</div>
                                <div class="category-trend up">📈 +3%</div>
                            </div>
                        </div>
                    </div>

                    <div class="section">
                        <h3>📂 全部分类</h3>
                        <div class="category-list">
                            <div class="category-item" onclick="showCategoryDetail('电脑及配件')">
                                <div class="item-header">
                                    <span class="item-icon">💻</span>
                                    <span class="item-name">电脑及配件</span>
                                    <span class="item-price">45-200元</span>
                                </div>
                                <div class="item-desc">主机 | 显卡 | CPU | 内存 | 硬盘</div>
                            </div>
                            <div class="category-item" onclick="showCategoryDetail('手机数码')">
                                <div class="item-header">
                                    <span class="item-icon">📱</span>
                                    <span class="item-name">手机数码</span>
                                    <span class="item-price">20-800元</span>
                                </div>
                                <div class="item-desc">手机 | 平板 | 耳机 | 充电器 | 音响</div>
                            </div>
                            <div class="category-item" onclick="showCategoryDetail('金属废料')">
                                <div class="item-header">
                                    <span class="item-icon">🔩</span>
                                    <span class="item-name">金属废料</span>
                                    <span class="item-price">2-45元/kg</span>
                                </div>
                                <div class="item-desc">铜 | 铝 | 铁 | 不锈钢 | 废线缆</div>
                            </div>
                            <div class="category-item" onclick="showCategoryDetail('纸类废品')">
                                <div class="item-header">
                                    <span class="item-icon">📄</span>
                                    <span class="item-name">纸类废品</span>
                                    <span class="item-price">1-3元/kg</span>
                                </div>
                                <div class="item-desc">报纸 | 纸箱 | 书本 | 杂志 | 办公纸</div>
                            </div>
                        </div>
                    </div>

                    <div class="bottom-nav">
                        <div class="nav-item" onclick="showPage('home')">
                            <div class="nav-icon">🏠</div>
                            <div class="nav-label">首页</div>
                        </div>
                        <div class="nav-item active">
                            <div class="nav-icon">📂</div>
                            <div class="nav-label">分类</div>
                        </div>
                        <div class="nav-item" onclick="showPage('market')">
                            <div class="nav-icon">📈</div>
                            <div class="nav-label">行情</div>
                        </div>
                        <div class="nav-item" onclick="showPage('profile')">
                            <div class="nav-icon">👤</div>
                            <div class="nav-label">我的</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- AI路线规划页 -->
            <div id="ai-route" class="page">
                <div class="phone-mockup">
                    <div class="page-header">
                        <span class="back-btn" onclick="showPage('home')">◀ 返回</span>
                        <h2>🤖 AI智能路线规划</h2>
                    </div>

                    <div class="route-info">
                        <div class="info-row">
                            <span>📅 今日路线规划</span>
                            <span>📍 当前位置: 朝阳区</span>
                        </div>
                        <div class="stats-grid">
                            <div class="stat-item">
                                <div class="stat-value">2小时30分钟</div>
                                <div class="stat-label">🕐 预计用时</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">¥180-220</div>
                                <div class="stat-label">💰 预计收益</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">12.5公里</div>
                                <div class="stat-label">📏 总距离</div>
                            </div>
                        </div>
                    </div>

                    <div class="section">
                        <h3>🎯 快速设置</h3>
                        <div class="setting-grid">
                            <div class="setting-card active" onclick="toggleSetting(this)">
                                <div class="setting-icon">🚗</div>
                                <div class="setting-text">驾车出行</div>
                            </div>
                            <div class="setting-card" onclick="toggleSetting(this)">
                                <div class="setting-icon">💰</div>
                                <div class="setting-text">收益优先</div>
                            </div>
                            <div class="setting-card" onclick="toggleSetting(this)">
                                <div class="setting-icon">⏰</div>
                                <div class="setting-text">2小时内完成</div>
                            </div>
                        </div>
                    </div>

                    <div class="route-preview">
                        <h3>🗺️ 智能路线预览</h3>
                        <div class="route-map">
                            <div class="route-line">
                                🏠 → 📍1 → 📍2 → 📍3 → 📍4 → 🏠
                            </div>
                            <div class="route-distances">
                                2km&nbsp;&nbsp;&nbsp;3km&nbsp;&nbsp;&nbsp;2km&nbsp;&nbsp;&nbsp;4km&nbsp;&nbsp;&nbsp;1.5km
                            </div>
                        </div>
                    </div>

                    <div class="route-details">
                        <h3>📋 推荐路线详情</h3>
                        <div class="route-step" onclick="showStepDetail('绿色回收站')">
                            <div class="step-number">1️⃣</div>
                            <div class="step-content">
                                <div class="step-header">09:00-09:20 绿色回收站</div>
                                <div class="step-meta">📍 2.1km | 💰 ¥50-80 | ⭐4.8</div>
                                <div class="step-desc">📱 电子产品回收</div>
                            </div>
                        </div>
                        <div class="route-step" onclick="showStepDetail('环保回收中心')">
                            <div class="step-number">2️⃣</div>
                            <div class="step-content">
                                <div class="step-header">09:35-09:50 环保回收中心</div>
                                <div class="step-meta">📍 3.2km | 💰 ¥30-50 | ⭐4.5</div>
                                <div class="step-desc">🔩 金属类回收</div>
                            </div>
                        </div>
                        <div class="route-step" onclick="showStepDetail('蓝天回收点')">
                            <div class="step-number">3️⃣</div>
                            <div class="step-content">
                                <div class="step-header">10:10-10:25 蓝天回收点</div>
                                <div class="step-meta">📍 2.8km | 💰 ¥20-40 | ⭐4.2</div>
                                <div class="step-desc">📄 纸类废品回收</div>
                            </div>
                        </div>
                    </div>

                    <div class="ai-suggestions">
                        <h3>🤖 AI优化建议</h3>
                        <ul class="suggestion-list">
                            <li>• 建议09:00出发，避开早高峰</li>
                            <li>• 第2站可顺路回收纸类，增收¥20</li>
                            <li>• 路线可节省30%时间相比自选路线</li>
                            <li>• 建议携带分类袋，提高回收效率</li>
                        </ul>
                    </div>

                    <div class="action-buttons">
                        <button class="btn btn-secondary" onclick="editRoute()">✏️ 自定义编辑</button>
                        <button class="btn btn-secondary" onclick="replanRoute()">🔄 重新规划</button>
                        <button class="btn btn-primary" onclick="startNavigation()">🚀 开始导航</button>
                    </div>
                </div>
            </div>

            <!-- 行情页 -->
            <div id="market" class="page">
                <div class="phone-mockup">
                    <div class="page-header">
                        <h2>📈 今日回收行情</h2>
                        <span class="date">📅 2024-01-15</span>
                    </div>

                    <div class="location-selector">
                        <span>📍 北京市朝阳区</span>
                        <span class="change-location" onclick="changeLocation()">🔄 切换地区</span>
                    </div>

                    <div class="section">
                        <h3>🔥 热门产品</h3>
                        <div class="trend-card up" onclick="showTrendDetail('笔记本电脑')">
                            <div class="trend-header">
                                <span class="product-name">💻 笔记本电脑</span>
                                <span class="trend-indicator">📈 +5%</span>
                            </div>
                            <div class="price-range">500-2000元/台</div>
                            <div class="trend-desc">今日涨幅较大</div>
                        </div>
                        <div class="trend-card down" onclick="showTrendDetail('智能手机')">
                            <div class="trend-header">
                                <span class="product-name">📱 智能手机</span>
                                <span class="trend-indicator">📉 -2%</span>
                            </div>
                            <div class="price-range">50-800元/台</div>
                            <div class="trend-desc">价格小幅下跌</div>
                        </div>
                    </div>

                    <div class="section">
                        <h3>📊 价格趋势图</h3>
                        <div class="chart-container">
                            <div class="chart-title">废铜价格走势图</div>
                            <div class="chart-placeholder">
                                <div class="chart-line">
                                    <div class="chart-point" style="left: 10%; bottom: 60%;">50</div>
                                    <div class="chart-point" style="left: 30%; bottom: 80%;">45</div>
                                    <div class="chart-point" style="left: 50%; bottom: 70%;">40</div>
                                    <div class="chart-point" style="left: 70%; bottom: 90%;">35</div>
                                    <div class="chart-point" style="left: 90%; bottom: 50%;">30</div>
                                </div>
                                <div class="chart-labels">
                                    <span>1月</span>
                                    <span>2月</span>
                                    <span>3月</span>
                                    <span>4月</span>
                                    <span>5月</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="section">
                        <h3>📋 详细行情</h3>
                        <div class="price-list">
                            <div class="price-item" onclick="showPriceDetail('废铜')">
                                <span class="item-icon">🔩</span>
                                <span class="item-name">废铜</span>
                                <span class="item-price">45-50元/kg</span>
                                <span class="price-change up">↗ +2%</span>
                            </div>
                            <div class="price-item" onclick="showPriceDetail('废纸')">
                                <span class="item-icon">📄</span>
                                <span class="item-name">废纸</span>
                                <span class="item-price">2-3元/kg</span>
                                <span class="price-change down">↘ -1%</span>
                            </div>
                            <div class="price-item" onclick="showPriceDetail('塑料瓶')">
                                <span class="item-icon">🥤</span>
                                <span class="item-name">塑料瓶</span>
                                <span class="item-price">1-2元/kg</span>
                                <span class="price-change stable">→ 0%</span>
                            </div>
                            <div class="price-item" onclick="showPriceDetail('手机')">
                                <span class="item-icon">📱</span>
                                <span class="item-name">手机</span>
                                <span class="item-price">50-800元/台</span>
                                <span class="price-change up">↗ +3%</span>
                            </div>
                        </div>
                    </div>

                    <div class="bottom-nav">
                        <div class="nav-item" onclick="showPage('home')">
                            <div class="nav-icon">🏠</div>
                            <div class="nav-label">首页</div>
                        </div>
                        <div class="nav-item" onclick="showPage('category')">
                            <div class="nav-icon">📂</div>
                            <div class="nav-label">分类</div>
                        </div>
                        <div class="nav-item active">
                            <div class="nav-icon">📈</div>
                            <div class="nav-label">行情</div>
                        </div>
                        <div class="nav-item" onclick="showPage('profile')">
                            <div class="nav-icon">👤</div>
                            <div class="nav-label">我的</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 个人中心页 -->
            <div id="profile" class="page">
                <div class="phone-mockup">
                    <div class="user-header">
                        <div class="user-info">
                            <div class="avatar">👤</div>
                            <div class="user-details">
                                <div class="username">张三</div>
                                <div class="user-phone">📞 138****8888</div>
                            </div>
                        </div>
                        <div class="header-actions">
                            <span class="settings" onclick="openSettings()">⚙️ 设置</span>
                            <span class="messages" onclick="openMessages()">🔔 消息 (3)</span>
                        </div>
                    </div>

                    <div class="achievement-card">
                        <div class="achievement-title">🏆 环保达人</div>
                        <div class="achievement-stats">
                            <div class="stat">
                                <span class="stat-label">本月回收</span>
                                <span class="stat-value">15次</span>
                            </div>
                            <div class="stat">
                                <span class="stat-label">累计收益</span>
                                <span class="stat-value">¥328</span>
                            </div>
                        </div>
                        <div class="eco-contribution">
                            环保贡献: 减少碳排放 12.5kg CO₂
                        </div>
                    </div>

                    <div class="section">
                        <h3>📋 我的服务</h3>
                        <div class="service-grid">
                            <div class="service-item" onclick="openMyFavorites()">
                                <div class="service-icon">⭐</div>
                                <div class="service-text">我的收藏</div>
                            </div>
                            <div class="service-item" onclick="openRecycleHistory()">
                                <div class="service-icon">📝</div>
                                <div class="service-text">回收记录</div>
                            </div>
                            <div class="service-item" onclick="openSellGoods()">
                                <div class="service-icon">💰</div>
                                <div class="service-text">我要卖货</div>
                            </div>
                            <div class="service-item" onclick="openMessages()">
                                <div class="service-icon">🔔</div>
                                <div class="service-text">消息通知</div>
                            </div>
                            <div class="service-item" onclick="openDataStats()">
                                <div class="service-icon">📊</div>
                                <div class="service-text">数据统计</div>
                            </div>
                            <div class="service-item" onclick="openInviteFriends()">
                                <div class="service-icon">🎁</div>
                                <div class="service-text">邀请好友</div>
                            </div>
                        </div>
                    </div>

                    <div class="section">
                        <h3>🛠️ 工具与帮助</h3>
                        <div class="tool-list">
                            <div class="tool-item" onclick="openRecycleGuide()">
                                <span class="tool-icon">📚</span>
                                <span class="tool-name">回收指南</span>
                                <span class="tool-arrow">></span>
                            </div>
                            <div class="tool-item" onclick="openCalculator()">
                                <span class="tool-icon">🧮</span>
                                <span class="tool-name">价值计算器</span>
                                <span class="tool-arrow">></span>
                            </div>
                            <div class="tool-item" onclick="openCustomerService()">
                                <span class="tool-icon">💬</span>
                                <span class="tool-name">在线客服</span>
                                <span class="tool-arrow">></span>
                            </div>
                            <div class="tool-item" onclick="openFAQ()">
                                <span class="tool-icon">❓</span>
                                <span class="tool-name">常见问题</span>
                                <span class="tool-arrow">></span>
                            </div>
                            <div class="tool-item" onclick="openAbout()">
                                <span class="tool-icon">ℹ️</span>
                                <span class="tool-name">关于我们</span>
                                <span class="tool-arrow">></span>
                            </div>
                        </div>
                    </div>

                    <div class="bottom-nav">
                        <div class="nav-item" onclick="showPage('home')">
                            <div class="nav-icon">🏠</div>
                            <div class="nav-label">首页</div>
                        </div>
                        <div class="nav-item" onclick="showPage('category')">
                            <div class="nav-icon">📂</div>
                            <div class="nav-label">分类</div>
                        </div>
                        <div class="nav-item" onclick="showPage('market')">
                            <div class="nav-icon">📈</div>
                            <div class="nav-label">行情</div>
                        </div>
                        <div class="nav-item active">
                            <div class="nav-icon">👤</div>
                            <div class="nav-label">我的</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="js/main.js"></script>
</body>
</html>
