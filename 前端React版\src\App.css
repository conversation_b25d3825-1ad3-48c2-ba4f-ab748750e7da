.App {
  min-height: 100vh;
}

/* 首页样式 */
.home-page .welcome-banner {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.home-page .welcome-banner .ant-card-body {
  background: transparent;
}

.home-page .quick-action-card {
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.home-page .quick-action-card:hover {
  border-color: #1890ff;
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(24, 144, 255, 0.2);
}

/* 产品页面样式 */
.products-page .ant-card {
  border-radius: 8px;
}

.products-page .ant-list-item {
  padding: 0;
}

.products-page .ant-card-cover {
  border-radius: 8px 8px 0 0;
}

/* 通用页面样式 */
.page-container {
  max-width: 1200px;
  margin: 0 auto;
}

/* 卡片悬停效果 */
.hover-card {
  transition: all 0.3s ease;
  cursor: pointer;
}

.hover-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .home-page .welcome-banner .ant-statistic {
    text-align: center;
  }
  
  .products-page .ant-card-meta-title {
    font-size: 14px;
  }
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
