package com.c2brecycle.service;

import com.c2brecycle.dto.WechatLoginDTO;
import com.c2brecycle.vo.LoginVO;

/**
 * 认证服务接口
 * 
 * <AUTHOR> Recycle Team
 * @since 2024-01-15
 */
public interface AuthService {

    /**
     * 微信小程序登录
     * 
     * @param loginDTO 登录参数
     * @return 登录结果
     */
    LoginVO wechatLogin(WechatLoginDTO loginDTO);

    /**
     * 刷新Token
     * 
     * @param refreshToken 刷新令牌
     * @return 新的Token信息
     */
    LoginVO refreshToken(String refreshToken);

    /**
     * 用户登出
     * 
     * @param userId 用户ID
     */
    void logout(Long userId);

    /**
     * 验证Token
     * 
     * @param token JWT令牌
     * @return 用户ID
     */
    Long verifyToken(String token);
}
