package com.c2brecycle.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.c2brecycle.entity.Favorite;
import com.c2brecycle.vo.FavoriteVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 收藏Mapper接口
 * 
 * <AUTHOR> Recycle Team
 * @since 2024-01-15
 */
@Mapper
public interface FavoriteMapper extends BaseMapper<Favorite> {

    /**
     * 获取用户收藏列表
     * 
     * @param page 分页参数
     * @param userId 用户ID
     * @param type 收藏类型
     * @return 收藏列表
     */
    IPage<FavoriteVO> selectUserFavorites(Page<FavoriteVO> page, 
                                         @Param("userId") Long userId, 
                                         @Param("type") Integer type);
}
