<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>慢慢回收 - 专业的废品回收信息服务平台</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>🌿</text></svg>">
    <style>
        /* 基础样式重置 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            color: #333;
            line-height: 1.6;
        }

        /* 主容器 */
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        /* 项目头部 */
        .project-header {
            background: linear-gradient(135deg, #52C41A, #73D13D);
            color: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(82, 196, 26, 0.3);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 30px;
        }

        .logo-section {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .logo {
            font-size: 4em;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            width: 80px;
            height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .brand-info h1 {
            font-size: 2.5em;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .brand-info p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .project-stats {
            display: flex;
            gap: 30px;
        }

        .stat-item {
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }

        .stat-number {
            display: block;
            font-size: 2.5em;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 0.9em;
            opacity: 0.9;
        }

        /* 导航菜单 */
        .demo-nav {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-bottom: 30px;
            flex-wrap: wrap;
            background: white;
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .nav-btn {
            padding: 15px 25px;
            border: 2px solid #52C41A;
            background: white;
            color: #52C41A;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .nav-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #52C41A, #73D13D);
            transition: left 0.3s ease;
            z-index: -1;
        }

        .nav-btn:hover::before,
        .nav-btn.active::before {
            left: 0;
        }

        .nav-btn:hover,
        .nav-btn.active {
            color: white;
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(82, 196, 26, 0.3);
        }

        /* 页面容器 */
        .pages-container {
            display: flex;
            justify-content: center;
            min-height: 600px;
        }

        .page {
            display: none;
            width: 100%;
            justify-content: center;
            align-items: flex-start;
        }

        .page.active {
            display: flex;
        }

        /* 手机模拟器 */
        .phone-mockup {
            width: 375px;
            height: 812px;
            background: white;
            border-radius: 30px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
            overflow: hidden;
            position: relative;
            border: 8px solid #333;
            transform: scale(0.9);
            transition: all 0.3s ease;
        }

        .phone-mockup:hover {
            transform: scale(0.92);
            box-shadow: 0 25px 80px rgba(0, 0, 0, 0.25);
        }

        /* 页面头部样式 */
        .phone-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            background: white;
            border-bottom: 1px solid #f0f0f0;
            font-size: 14px;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .location {
            font-weight: 600;
            color: #333;
        }

        .header-actions {
            display: flex;
            gap: 15px;
        }

        .search-btn, .filter-btn {
            color: #52C41A;
            cursor: pointer;
            padding: 5px 10px;
            border-radius: 15px;
            transition: all 0.3s ease;
        }

        .search-btn:hover, .filter-btn:hover {
            background: #f6ffed;
        }

        .page-header {
            padding: 15px 20px;
            background: linear-gradient(135deg, #52C41A, #73D13D);
            color: white;
            display: flex;
            align-items: center;
            gap: 15px;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .page-header h2 {
            font-size: 18px;
            font-weight: 600;
            flex: 1;
            text-align: center;
        }

        .back-btn {
            color: white;
            cursor: pointer;
            font-size: 16px;
            padding: 5px 10px;
            border-radius: 15px;
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .date {
            font-size: 14px;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <!-- 项目介绍头部 -->
        <header class="project-header">
            <div class="header-content">
                <div class="logo-section">
                    <div class="logo">🌿</div>
                    <div class="brand-info">
                        <h1>慢慢回收</h1>
                        <p>专业的废品回收信息服务平台</p>
                    </div>
                </div>
                <div class="project-stats">
                    <div class="stat-item">
                        <span class="stat-number">12</span>
                        <span class="stat-label">功能模块</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">5</span>
                        <span class="stat-label">核心页面</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">1</span>
                        <span class="stat-label">AI核心功能</span>
                    </div>
                </div>
            </div>
        </header>

        <!-- 导航菜单 -->
        <nav class="demo-nav">
            <button class="nav-btn active" onclick="showPage('home')" data-page="home">
                🏠 首页地图
            </button>
            <button class="nav-btn" onclick="showPage('category')" data-page="category">
                📂 分类查询
            </button>
            <button class="nav-btn" onclick="showPage('ai-route')" data-page="ai-route">
                🤖 AI路线规划
            </button>
            <button class="nav-btn" onclick="showPage('market')" data-page="market">
                📈 价格行情
            </button>
            <button class="nav-btn" onclick="showPage('profile')" data-page="profile">
                👤 个人中心
            </button>
        </nav>

        <!-- 页面容器 -->
        <div class="pages-container">
            <!-- 首页 -->
            <div id="home" class="page active">
                <div class="phone-mockup">
                    <div class="phone-header">
                        <span class="location">📍 北京市朝阳区</span>
                        <div class="header-actions">
                            <span class="search-btn">🔍 搜索</span>
                            <span class="filter-btn">☰ 筛选</span>
                        </div>
                    </div>

                    <div style="height: 300px; position: relative; background: linear-gradient(45deg, #e8f5e8 25%, #f0f9f0 25%, #f0f9f0 50%, #e8f5e8 50%, #e8f5e8 75%, #f0f9f0 75%); background-size: 20px 20px; animation: mapMove 20s linear infinite;">
                        <div style="width: 100%; height: 100%; position: relative; display: flex; align-items: center; justify-content: center;">
                            <div style="font-size: 20px; color: #52C41A; font-weight: 600; text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);">🗺️ 地图显示区域</div>
                            <div style="position: absolute; top: 30%; left: 20%; font-size: 24px; animation: bounce 2s infinite; cursor: pointer;">📍</div>
                            <div style="position: absolute; top: 50%; left: 60%; font-size: 24px; animation: bounce 2s infinite; cursor: pointer;">📍</div>
                            <div style="position: absolute; top: 70%; left: 40%; font-size: 24px; animation: bounce 2s infinite; cursor: pointer;">📍</div>
                            <div style="position: absolute; top: 35%; left: 25%; background: white; padding: 6px 12px; border-radius: 15px; font-size: 12px; font-weight: 500; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); border: 2px solid #52C41A; color: #52C41A;">绿色回收站</div>
                        </div>
                    </div>

                    <div style="display: flex; padding: 20px; gap: 15px; background: #fafafa;">
                        <div style="flex: 1; background: white; border-radius: 15px; padding: 25px 15px; text-align: center; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08); cursor: pointer; transition: all 0.3s ease;" onclick="showPage('category')">
                            <div style="font-size: 28px; margin-bottom: 10px;">📱</div>
                            <div style="font-size: 14px; font-weight: 600;">快速查价</div>
                        </div>
                        <div style="flex: 1; background: linear-gradient(135deg, #52C41A, #73D13D); color: white; border-radius: 15px; padding: 25px 15px; text-align: center; box-shadow: 0 8px 25px rgba(82, 196, 26, 0.4); cursor: pointer; transition: all 0.3s ease;" onclick="showPage('ai-route')">
                            <div style="font-size: 28px; margin-bottom: 10px;">🤖</div>
                            <div style="font-size: 14px; font-weight: 600;">AI路线</div>
                        </div>
                        <div style="flex: 1; background: white; border-radius: 15px; padding: 25px 15px; text-align: center; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08); cursor: pointer; transition: all 0.3s ease;">
                            <div style="font-size: 28px; margin-bottom: 10px;">📚</div>
                            <div style="font-size: 14px; font-weight: 600;">回收指南</div>
                        </div>
                    </div>

                    <div style="padding: 20px; padding-bottom: 100px; background: white;">
                        <h3 style="margin-bottom: 20px; font-size: 18px; color: #333; font-weight: 600;">📋 附近回收站点</h3>
                        <div style="background: white; border-radius: 15px; padding: 20px; margin-bottom: 15px; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08); border: 2px solid #f0f0f0; cursor: pointer; transition: all 0.3s ease;">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                                <span style="font-weight: 700; font-size: 16px; color: #333;">🏪 绿色回收站</span>
                                <div style="display: flex; gap: 12px; font-size: 14px;">
                                    <span style="color: #faad14; font-weight: 600;">⭐ 4.8</span>
                                    <span style="color: #52C41A; font-weight: 600;">📍 500m</span>
                                </div>
                            </div>
                            <div style="display: flex; justify-content: space-between; margin-bottom: 12px; font-size: 14px; color: #666;">
                                <div>📞 138****8888</div>
                                <div style="color: #52C41A; font-weight: 600;">🕐 营业中</div>
                            </div>
                            <div style="display: flex; gap: 8px; flex-wrap: wrap;">
                                <span style="background: linear-gradient(135deg, #f6ffed, #e6f7ff); color: #52C41A; padding: 6px 12px; border-radius: 15px; font-size: 12px; font-weight: 500; border: 1px solid #b7eb8f;">💰 电脑配件</span>
                                <span style="background: linear-gradient(135deg, #f6ffed, #e6f7ff); color: #52C41A; padding: 6px 12px; border-radius: 15px; font-size: 12px; font-weight: 500; border: 1px solid #b7eb8f;">🔩 金属</span>
                                <span style="background: linear-gradient(135deg, #f6ffed, #e6f7ff); color: #52C41A; padding: 6px 12px; border-radius: 15px; font-size: 12px; font-weight: 500; border: 1px solid #b7eb8f;">📄 纸类</span>
                            </div>
                        </div>
                    </div>

                    <div style="position: absolute; bottom: 0; left: 0; right: 0; background: white; border-top: 1px solid #f0f0f0; display: flex; padding: 10px 0; backdrop-filter: blur(10px);">
                        <div style="flex: 1; text-align: center; padding: 10px; cursor: pointer; color: #52C41A; background: #f6ffed; border-radius: 15px; margin: 0 5px;" onclick="showPage('home')">
                            <div style="font-size: 22px; margin-bottom: 4px;">🏠</div>
                            <div style="font-size: 12px; font-weight: 500;">首页</div>
                        </div>
                        <div style="flex: 1; text-align: center; padding: 10px; cursor: pointer; border-radius: 15px; margin: 0 5px;" onclick="showPage('category')">
                            <div style="font-size: 22px; margin-bottom: 4px;">📂</div>
                            <div style="font-size: 12px; font-weight: 500;">分类</div>
                        </div>
                        <div style="flex: 1; text-align: center; padding: 10px; cursor: pointer; border-radius: 15px; margin: 0 5px;" onclick="showPage('market')">
                            <div style="font-size: 22px; margin-bottom: 4px;">📈</div>
                            <div style="font-size: 12px; font-weight: 500;">行情</div>
                        </div>
                        <div style="flex: 1; text-align: center; padding: 10px; cursor: pointer; border-radius: 15px; margin: 0 5px;" onclick="showPage('profile')">
                            <div style="font-size: 22px; margin-bottom: 4px;">👤</div>
                            <div style="font-size: 12px; font-weight: 500;">我的</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>