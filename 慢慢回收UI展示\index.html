<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>慢慢回收 - 专业的废品回收信息服务平台</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>🌿</text></svg>">
    <style>
        /* 基础样式重置 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            color: #333;
            line-height: 1.6;
        }

        /* 主容器 */
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        /* 项目头部 */
        .project-header {
            background: linear-gradient(135deg, #52C41A, #73D13D);
            color: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(82, 196, 26, 0.3);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 30px;
        }

        .logo-section {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .logo {
            font-size: 4em;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            width: 80px;
            height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .brand-info h1 {
            font-size: 2.5em;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .brand-info p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .project-stats {
            display: flex;
            gap: 30px;
        }

        .stat-item {
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }

        .stat-number {
            display: block;
            font-size: 2.5em;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 0.9em;
            opacity: 0.9;
        }

        /* 导航菜单 */
        .demo-nav {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-bottom: 30px;
            flex-wrap: wrap;
            background: white;
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .nav-btn {
            padding: 15px 25px;
            border: 2px solid #52C41A;
            background: white;
            color: #52C41A;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .nav-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #52C41A, #73D13D);
            transition: left 0.3s ease;
            z-index: -1;
        }

        .nav-btn:hover::before,
        .nav-btn.active::before {
            left: 0;
        }

        .nav-btn:hover,
        .nav-btn.active {
            color: white;
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(82, 196, 26, 0.3);
        }

        /* 页面容器 */
        .pages-container {
            display: flex;
            justify-content: center;
            min-height: 600px;
        }

        .page {
            display: none;
            width: 100%;
            justify-content: center;
            align-items: flex-start;
        }

        .page.active {
            display: flex;
        }

        /* 手机模拟器 */
        .phone-mockup {
            width: 375px;
            height: 812px;
            background: white;
            border-radius: 30px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
            overflow: hidden;
            position: relative;
            border: 8px solid #333;
            transform: scale(0.9);
            transition: all 0.3s ease;
        }

        .phone-mockup:hover {
            transform: scale(0.92);
            box-shadow: 0 25px 80px rgba(0, 0, 0, 0.25);
        }

        /* 页面头部样式 */
        .phone-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            background: white;
            border-bottom: 1px solid #f0f0f0;
            font-size: 14px;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .location {
            font-weight: 600;
            color: #333;
        }

        .header-actions {
            display: flex;
            gap: 15px;
        }

        .search-btn, .filter-btn {
            color: #52C41A;
            cursor: pointer;
            padding: 5px 10px;
            border-radius: 15px;
            transition: all 0.3s ease;
        }

        .search-btn:hover, .filter-btn:hover {
            background: #f6ffed;
        }

        .page-header {
            padding: 15px 20px;
            background: linear-gradient(135deg, #52C41A, #73D13D);
            color: white;
            display: flex;
            align-items: center;
            gap: 15px;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .page-header h2 {
            font-size: 18px;
            font-weight: 600;
            flex: 1;
            text-align: center;
        }

        .back-btn {
            color: white;
            cursor: pointer;
            font-size: 16px;
            padding: 5px 10px;
            border-radius: 15px;
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .date {
            font-size: 14px;
            opacity: 0.9;
        }

        /* 动画效果 */
        @keyframes mapMove {
            0% { background-position: 0 0; }
            100% { background-position: 20px 20px; }
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-15px); }
            60% { transform: translateY(-8px); }
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <!-- 项目介绍头部 -->
        <header class="project-header">
            <div class="header-content">
                <div class="logo-section">
                    <div class="logo">🌿</div>
                    <div class="brand-info">
                        <h1>慢慢回收</h1>
                        <p>专业的废品回收信息服务平台</p>
                    </div>
                </div>
                <div class="project-stats">
                    <div class="stat-item">
                        <span class="stat-number">12</span>
                        <span class="stat-label">功能模块</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">5</span>
                        <span class="stat-label">核心页面</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">1</span>
                        <span class="stat-label">AI核心功能</span>
                    </div>
                </div>
            </div>
        </header>

        <!-- 导航菜单 -->
        <nav class="demo-nav">
            <button class="nav-btn active" onclick="showPage('home')" data-page="home">
                🏠 首页查价
            </button>
            <button class="nav-btn" onclick="showPage('category')" data-page="category">
                📂 分类查询
            </button>
            <button class="nav-btn" onclick="showPage('ai-route')" data-page="ai-route">
                🤖 AI路线规划
            </button>
            <button class="nav-btn" onclick="showPage('market')" data-page="market">
                📈 价格行情
            </button>
            <button class="nav-btn" onclick="showPage('profile')" data-page="profile">
                👤 个人中心
            </button>
        </nav>

        <!-- 页面容器 -->
        <div class="pages-container">
            <!-- 首页 -->
            <div id="home" class="page active">
                <div class="phone-mockup">
                    <!-- 手机头部 -->
                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 15px 20px; background: linear-gradient(135deg, #52C41A, #73D13D); color: white; font-size: 14px;">
                        <span style="font-weight: 600;">📍 北京市朝阳区</span>
                        <div style="display: flex; gap: 15px;">
                            <span style="color: white; cursor: pointer; padding: 5px 10px; border-radius: 15px; transition: all 0.3s ease; background: rgba(255,255,255,0.2);" onclick="showToast('扫码查价功能')">� 扫码</span>
                            <span style="color: white; cursor: pointer; padding: 5px 10px; border-radius: 15px; transition: all 0.3s ease; background: rgba(255,255,255,0.2);" onclick="showToast('消息通知')">🔔</span>
                        </div>
                    </div>

                    <!-- 快速查价搜索框 -->
                    <div style="padding: 20px; background: linear-gradient(135deg, #52C41A, #73D13D);">
                        <div style="position: relative;">
                            <input type="text" placeholder="🔍 输入产品名称快速查价..." style="width: 100%; padding: 15px 50px 15px 20px; border: none; border-radius: 25px; background: white; font-size: 16px; outline: none; box-shadow: 0 4px 15px rgba(0,0,0,0.1);" onclick="showToast('搜索功能')">
                            <div style="position: absolute; right: 15px; top: 50%; transform: translateY(-50%); background: #52C41A; color: white; width: 35px; height: 35px; border-radius: 50%; display: flex; align-items: center; justify-content: center; cursor: pointer; font-size: 16px;" onclick="showToast('语音搜索')">🎤</div>
                        </div>
                        <div style="display: flex; gap: 10px; margin-top: 15px; flex-wrap: wrap;">
                            <span style="background: rgba(255,255,255,0.2); color: white; padding: 6px 12px; border-radius: 15px; font-size: 12px; cursor: pointer;" onclick="showToast('搜索iPhone')">iPhone</span>
                            <span style="background: rgba(255,255,255,0.2); color: white; padding: 6px 12px; border-radius: 15px; font-size: 12px; cursor: pointer;" onclick="showToast('搜索笔记本')">笔记本</span>
                            <span style="background: rgba(255,255,255,0.2); color: white; padding: 6px 12px; border-radius: 15px; font-size: 12px; cursor: pointer;" onclick="showToast('搜索显卡')">显卡</span>
                            <span style="background: rgba(255,255,255,0.2); color: white; padding: 6px 12px; border-radius: 15px; font-size: 12px; cursor: pointer;" onclick="showToast('搜索废铜')">废铜</span>
                        </div>
                    </div>

                    <!-- 今日行情卡片 -->
                    <div style="margin: 20px; background: white; border-radius: 15px; padding: 20px; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                            <h3 style="font-size: 18px; font-weight: 600; color: #333;">� 今日行情</h3>
                            <span style="font-size: 12px; color: #666;">2024-01-15 更新</span>
                        </div>
                        <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 12px;">
                            <div style="background: #f6ffed; border: 1px solid #b7eb8f; border-radius: 10px; padding: 12px; cursor: pointer;" onclick="showToast('查看iPhone详细价格')">
                                <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px;">
                                    <span style="font-size: 20px;">📱</span>
                                    <span style="font-size: 14px; font-weight: 600;">iPhone 13</span>
                                    <span style="font-size: 12px; color: #ff4d4f; background: #fff2f0; padding: 2px 6px; border-radius: 8px;">📈 +5%</span>
                                </div>
                                <div style="font-size: 16px; font-weight: 700; color: #52C41A;">¥3000-4500</div>
                                <div style="font-size: 12px; color: #666;">今日涨幅较大</div>
                            </div>
                            <div style="background: #f6ffed; border: 1px solid #b7eb8f; border-radius: 10px; padding: 12px; cursor: pointer;" onclick="showToast('查看笔记本详细价格')">
                                <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px;">
                                    <span style="font-size: 20px;">💻</span>
                                    <span style="font-size: 14px; font-weight: 600;">笔记本</span>
                                    <span style="font-size: 12px; color: #52C41A; background: #f6ffed; padding: 2px 6px; border-radius: 8px;">📉 -2%</span>
                                </div>
                                <div style="font-size: 16px; font-weight: 700; color: #52C41A;">¥800-3000</div>
                                <div style="font-size: 12px; color: #666;">价格小幅下跌</div>
                            </div>
                            <div style="background: #f6ffed; border: 1px solid #b7eb8f; border-radius: 10px; padding: 12px; cursor: pointer;" onclick="showToast('查看废铜详细价格')">
                                <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px;">
                                    <span style="font-size: 20px;">🔩</span>
                                    <span style="font-size: 14px; font-weight: 600;">废铜</span>
                                    <span style="font-size: 12px; color: #ff4d4f; background: #fff2f0; padding: 2px 6px; border-radius: 8px;">📈 +3%</span>
                                </div>
                                <div style="font-size: 16px; font-weight: 700; color: #52C41A;">¥45-50/kg</div>
                                <div style="font-size: 12px; color: #666;">金属价格上涨</div>
                            </div>
                            <div style="background: #f6ffed; border: 1px solid #b7eb8f; border-radius: 10px; padding: 12px; cursor: pointer;" onclick="showToast('查看显卡详细价格')">
                                <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px;">
                                    <span style="font-size: 20px;">🎮</span>
                                    <span style="font-size: 14px; font-weight: 600;">显卡</span>
                                    <span style="font-size: 12px; color: #ff4d4f; background: #fff2f0; padding: 2px 6px; border-radius: 8px;">📈 +8%</span>
                                </div>
                                <div style="font-size: 16px; font-weight: 700; color: #52C41A;">¥500-2500</div>
                                <div style="font-size: 12px; color: #666;">需求旺盛</div>
                            </div>
                        </div>
                        <div style="text-align: center; margin-top: 15px;">
                            <span style="color: #52C41A; font-size: 14px; cursor: pointer; padding: 8px 16px; border: 1px solid #52C41A; border-radius: 20px;" onclick="showPage('market')">查看更多行情 →</span>
                        </div>
                    </div>

                    <!-- 快捷功能区 -->
                    <div style="display: flex; padding: 0 20px 20px; gap: 15px;">
                        <div style="flex: 1; background: white; border-radius: 15px; padding: 20px 15px; text-align: center; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08); cursor: pointer; transition: all 0.3s ease; border: 2px solid #52C41A;" onclick="showPage('category')" onmouseover="this.style.transform='translateY(-5px)'; this.style.background='#f6ffed'" onmouseout="this.style.transform='translateY(0)'; this.style.background='white'">
                            <div style="font-size: 28px; margin-bottom: 10px;">�</div>
                            <div style="font-size: 14px; font-weight: 600; color: #52C41A;">快速查价</div>
                        </div>
                        <div style="flex: 1; background: white; border-radius: 15px; padding: 20px 15px; text-align: center; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08); cursor: pointer; transition: all 0.3s ease;" onclick="showPage('ai-route')" onmouseover="this.style.transform='translateY(-5px)'; this.style.background='#f6ffed'" onmouseout="this.style.transform='translateY(0)'; this.style.background='white'">
                            <div style="font-size: 28px; margin-bottom: 10px;">🤖</div>
                            <div style="font-size: 14px; font-weight: 600;">AI路线</div>
                        </div>
                        <div style="flex: 1; background: white; border-radius: 15px; padding: 20px 15px; text-align: center; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08); cursor: pointer; transition: all 0.3s ease;" onclick="showToast('拍照识别功能')" onmouseover="this.style.transform='translateY(-5px)'; this.style.background='#f6ffed'" onmouseout="this.style.transform='translateY(0)'; this.style.background='white'">
                            <div style="font-size: 28px; margin-bottom: 10px;">�</div>
                            <div style="font-size: 14px; font-weight: 600;">拍照识别</div>
                        </div>
                    </div>

                    <div style="padding: 20px; padding-bottom: 100px; background: white;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                            <h3 style="font-size: 18px; color: #333; font-weight: 600;">� 热门产品</h3>
                            <span style="color: #52C41A; font-size: 14px; cursor: pointer;" onclick="showPage('category')">查看全部 →</span>
                        </div>

                        <div style="background: white; border-radius: 15px; padding: 20px; margin-bottom: 15px; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08); border: 2px solid #f0f0f0; cursor: pointer; transition: all 0.3s ease;" onclick="showToast('查看iPhone 13 Pro详细价格')" onmouseover="this.style.transform='translateY(-3px)'; this.style.borderColor='#52C41A'" onmouseout="this.style.transform='translateY(0)'; this.style.borderColor='#f0f0f0'">
                            <div style="display: flex; align-items: center; gap: 15px;">
                                <div style="width: 60px; height: 60px; background: #f6ffed; border-radius: 12px; display: flex; align-items: center; justify-content: center; font-size: 28px;">📱</div>
                                <div style="flex: 1;">
                                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                        <span style="font-weight: 700; font-size: 16px; color: #333;">iPhone 13 Pro</span>
                                        <span style="font-size: 12px; color: #ff4d4f; background: #fff2f0; padding: 4px 8px; border-radius: 12px; font-weight: 600;">📈 涨价</span>
                                    </div>
                                    <div style="font-size: 18px; font-weight: 700; color: #52C41A; margin-bottom: 8px;">¥3000-4500</div>
                                    <div style="display: flex; justify-content: space-between; font-size: 12px; color: #666;">
                                        <span>128GB起 | 成色8成新以上</span>
                                        <span>今日查询 1,234 次</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div style="background: white; border-radius: 15px; padding: 20px; margin-bottom: 15px; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08); border: 2px solid #f0f0f0; cursor: pointer; transition: all 0.3s ease;" onclick="showToast('查看MacBook详细价格')" onmouseover="this.style.transform='translateY(-3px)'; this.style.borderColor='#52C41A'" onmouseout="this.style.transform='translateY(0)'; this.style.borderColor='#f0f0f0'">
                            <div style="display: flex; align-items: center; gap: 15px;">
                                <div style="width: 60px; height: 60px; background: #f6ffed; border-radius: 12px; display: flex; align-items: center; justify-content: center; font-size: 28px;">�</div>
                                <div style="flex: 1;">
                                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                        <span style="font-weight: 700; font-size: 16px; color: #333;">MacBook Pro</span>
                                        <span style="font-size: 12px; color: #52C41A; background: #f6ffed; padding: 4px 8px; border-radius: 12px; font-weight: 600;">📉 降价</span>
                                    </div>
                                    <div style="font-size: 18px; font-weight: 700; color: #52C41A; margin-bottom: 8px;">¥4000-8000</div>
                                    <div style="display: flex; justify-content: space-between; font-size: 12px; color: #666;">
                                        <span>13寸/15寸 | 功能正常</span>
                                        <span>今日查询 856 次</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div style="background: white; border-radius: 15px; padding: 20px; margin-bottom: 15px; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08); border: 2px solid #f0f0f0; cursor: pointer; transition: all 0.3s ease;" onclick="showToast('查看显卡详细价格')" onmouseover="this.style.transform='translateY(-3px)'; this.style.borderColor='#52C41A'" onmouseout="this.style.transform='translateY(0)'; this.style.borderColor='#f0f0f0'">
                            <div style="display: flex; align-items: center; gap: 15px;">
                                <div style="width: 60px; height: 60px; background: #f6ffed; border-radius: 12px; display: flex; align-items: center; justify-content: center; font-size: 28px;">🎮</div>
                                <div style="flex: 1;">
                                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                        <span style="font-weight: 700; font-size: 16px; color: #333;">RTX 3080 显卡</span>
                                        <span style="font-size: 12px; color: #ff4d4f; background: #fff2f0; padding: 4px 8px; border-radius: 12px; font-weight: 600;">🔥 热门</span>
                                    </div>
                                    <div style="font-size: 18px; font-weight: 700; color: #52C41A; margin-bottom: 8px;">¥2000-3500</div>
                                    <div style="display: flex; justify-content: space-between; font-size: 12px; color: #666;">
                                        <span>10GB显存 | 无修无拆</span>
                                        <span>今日查询 2,156 次</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div style="text-align: center; margin-top: 20px;">
                            <div style="color: #52C41A; font-size: 14px; cursor: pointer; padding: 12px 24px; border: 2px solid #52C41A; border-radius: 25px; background: #f6ffed; font-weight: 600;" onclick="showPage('category')">查看更多产品价格 →</div>
                        </div>
                    </div>

                    <div style="position: absolute; bottom: 0; left: 0; right: 0; background: white; border-top: 1px solid #f0f0f0; display: flex; padding: 10px 0; backdrop-filter: blur(10px);">
                        <div style="flex: 1; text-align: center; padding: 10px; cursor: pointer; color: #52C41A; background: #f6ffed; border-radius: 15px; margin: 0 5px;" onclick="showPage('home')">
                            <div style="font-size: 22px; margin-bottom: 4px;">🏠</div>
                            <div style="font-size: 12px; font-weight: 500;">首页</div>
                        </div>
                        <div style="flex: 1; text-align: center; padding: 10px; cursor: pointer; border-radius: 15px; margin: 0 5px;" onclick="showPage('category')">
                            <div style="font-size: 22px; margin-bottom: 4px;">📂</div>
                            <div style="font-size: 12px; font-weight: 500;">分类</div>
                        </div>
                        <div style="flex: 1; text-align: center; padding: 10px; cursor: pointer; border-radius: 15px; margin: 0 5px;" onclick="showPage('market')">
                            <div style="font-size: 22px; margin-bottom: 4px;">📈</div>
                            <div style="font-size: 12px; font-weight: 500;">行情</div>
                        </div>
                        <div style="flex: 1; text-align: center; padding: 10px; cursor: pointer; border-radius: 15px; margin: 0 5px;" onclick="showPage('profile')">
                            <div style="font-size: 22px; margin-bottom: 4px;">👤</div>
                            <div style="font-size: 12px; font-weight: 500;">我的</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>