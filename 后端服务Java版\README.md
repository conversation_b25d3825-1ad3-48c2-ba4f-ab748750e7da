# 🌿 慢慢回收小程序后端API服务 (Java版)

## 📋 项目简介

基于Spring Boot + MyBatis Plus构建的慢慢回收小程序后端API服务，提供完整的废品回收价格查询、站点管理、用户认证等功能。

### 🎯 核心功能
- 💰 **价格查询系统** - 废品回收价格查询与展示
- 🗺️ **站点地图服务** - 基于地理位置的附近站点查询
- 🔍 **搜索功能** - 产品和站点的关键词搜索
- 👤 **用户认证体系** - 微信小程序登录与JWT认证
- 📱 **用户管理** - 用户信息、地址、收藏管理

### 🛠️ 技术栈
- **框架**: Spring Boot 2.7.14
- **ORM**: MyBatis Plus 3.5.3.1
- **数据库**: MySQL 8.0
- **缓存**: Redis 6.0
- **认证**: JWT + Spring Security
- **文档**: Swagger 3.0
- **监控**: Druid + Actuator
- **工具**: Hutool + Lombok

## 🚀 快速开始

### 1. 环境要求

```bash
# Java环境
Java 8+
Maven 3.6+

# 数据库
MySQL 8.0+
Redis 6.0+
```

### 2. 项目构建

```bash
# 克隆项目
git clone https://github.com/your-org/manman-recycle-java.git
cd manman-recycle-java

# 编译项目
mvn clean compile

# 运行测试
mvn test

# 打包项目
mvn clean package -DskipTests
```

### 3. 配置文件

编辑 `src/main/resources/application.yml`:

```yaml
# 数据库配置
spring:
  datasource:
    url: **************************************?useUnicode=true&characterEncoding=utf8&serverTimezone=GMT%2B8
    username: root
    password: your_password

# Redis配置
  redis:
    host: localhost
    port: 6379
    password: your_redis_password

# 应用配置
app:
  jwt:
    secret: your-jwt-secret-key
  wechat:
    app-id: your_wechat_app_id
    app-secret: your_wechat_app_secret
```

### 4. 数据库初始化

```bash
# 创建数据库
mysql -u root -p -e "CREATE DATABASE recycle_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

# 导入表结构
mysql -u root -p recycle_db < ../数据库设计/简化版数据表设计.sql

# 导入示例数据
mysql -u root -p recycle_db < ../数据库设计/简化版示例数据.sql
```

### 5. 启动应用

```bash
# 开发环境启动
mvn spring-boot:run

# 或者运行JAR包
java -jar target/manman-recycle-api-1.0.0.jar

# 指定环境
java -jar target/manman-recycle-api-1.0.0.jar --spring.profiles.active=prod
```

### 6. 验证启动

访问以下地址验证服务启动：

- 🏠 **健康检查**: http://localhost:8080/api/actuator/health
- 📖 **API文档**: http://localhost:8080/api/swagger-ui/
- 📊 **数据库监控**: http://localhost:8080/api/druid/
- 🔍 **应用监控**: http://localhost:8080/api/actuator/

## 📁 项目结构

```
src/main/java/com/manmanrecycle/
├── ManmanRecycleApplication.java     # 启动类
├── common/                           # 公共模块
│   ├── result/                       # 统一响应结果
│   ├── exception/                    # 异常处理
│   └── config/                       # 配置类
├── controller/                       # 控制器层
│   ├── AuthController.java          # 认证控制器
│   ├── UserController.java          # 用户控制器
│   ├── StationController.java       # 站点控制器
│   ├── ProductController.java       # 产品控制器
│   └── PriceController.java         # 价格控制器
├── service/                          # 服务层
│   ├── AuthService.java             # 认证服务
│   ├── UserService.java             # 用户服务
│   ├── StationService.java          # 站点服务
│   ├── ProductService.java          # 产品服务
│   └── PriceService.java            # 价格服务
├── mapper/                           # 数据访问层
│   ├── UserMapper.java              # 用户Mapper
│   ├── StationMapper.java           # 站点Mapper
│   ├── ProductMapper.java           # 产品Mapper
│   └── PriceMapper.java             # 价格Mapper
├── entity/                           # 实体类
│   ├── User.java                    # 用户实体
│   ├── Station.java                 # 站点实体
│   ├── Product.java                 # 产品实体
│   └── Price.java                   # 价格实体
├── dto/                              # 数据传输对象
│   ├── WechatLoginDTO.java          # 微信登录DTO
│   └── RefreshTokenDTO.java         # 刷新Token DTO
├── vo/                               # 视图对象
│   ├── LoginVO.java                 # 登录响应VO
│   ├── StationVO.java               # 站点VO
│   └── PriceVO.java                 # 价格VO
└── util/                             # 工具类
    ├── JwtUtil.java                 # JWT工具
    ├── RedisUtil.java               # Redis工具
    └── GeoUtil.java                 # 地理位置工具
```

## 🔌 API接口

### 认证模块
```http
POST /api/auth/wechat-login     # 微信登录
POST /api/auth/refresh-token    # 刷新Token
POST /api/auth/logout           # 用户登出
GET  /api/auth/verify           # 验证Token
```

### 用户模块
```http
GET  /api/user/profile          # 获取用户信息
PUT  /api/user/profile          # 更新用户信息
GET  /api/user/addresses        # 获取用户地址
POST /api/user/addresses        # 添加用户地址
GET  /api/user/favorites        # 获取用户收藏
POST /api/user/favorites        # 添加收藏
```

### 站点模块
```http
GET  /api/stations/nearby       # 获取附近站点
GET  /api/stations/search       # 搜索站点
GET  /api/stations/{id}         # 获取站点详情
POST /api/stations/{id}/reviews # 提交站点评价
```

### 价格模块
```http
GET  /api/prices/realtime       # 获取实时价格
GET  /api/prices/trends         # 获取价格趋势
GET  /api/prices/compare        # 价格比较
POST /api/prices/alerts         # 创建价格预警
```

## 🔧 配置说明

### 数据库配置
```yaml
spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **************************************
    username: root
    password: 123456
    druid:
      initial-size: 5
      min-idle: 5
      max-active: 20
      max-wait: 60000
```

### MyBatis Plus配置
```yaml
mybatis-plus:
  type-aliases-package: com.manmanrecycle.entity
  mapper-locations: classpath*:mapper/*.xml
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
```

### Redis配置
```yaml
spring:
  redis:
    host: localhost
    port: 6379
    password: 
    database: 0
    timeout: 3000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0
```

## 🧪 测试

### 单元测试
```bash
# 运行所有测试
mvn test

# 运行指定测试类
mvn test -Dtest=AuthServiceTest

# 生成测试报告
mvn test jacoco:report
```

### 集成测试
```bash
# 运行集成测试
mvn verify

# 使用测试配置
mvn test -Dspring.profiles.active=test
```

### API测试
```bash
# 使用curl测试
curl -X POST http://localhost:8080/api/auth/wechat-login \
  -H "Content-Type: application/json" \
  -d '{"code":"test_code_123","nickname":"测试用户"}'

# 使用Postman
# 导入项目根目录下的 postman_collection.json
```

## 📊 监控

### 应用监控
- **健康检查**: `/actuator/health`
- **应用信息**: `/actuator/info`
- **性能指标**: `/actuator/metrics`
- **环境信息**: `/actuator/env`

### 数据库监控
- **Druid监控**: `/druid/index.html`
- **SQL监控**: `/druid/sql.html`
- **连接池监控**: `/druid/datasource.html`

### 日志监控
```bash
# 查看应用日志
tail -f logs/manman-recycle.log

# 查看错误日志
grep "ERROR" logs/manman-recycle.log

# 实时监控日志
tail -f logs/manman-recycle.log | grep "价格"
```

## 🚀 部署

### Docker部署
```dockerfile
# Dockerfile
FROM openjdk:8-jre-alpine
VOLUME /tmp
COPY target/manman-recycle-api-1.0.0.jar app.jar
ENTRYPOINT ["java","-jar","/app.jar"]
```

```bash
# 构建镜像
docker build -t manman-recycle-api .

# 运行容器
docker run -d -p 8080:8080 \
  -e SPRING_PROFILES_ACTIVE=prod \
  -e DB_HOST=mysql-server \
  -e REDIS_HOST=redis-server \
  --name recycle-api \
  manman-recycle-api
```

### Docker Compose部署
```yaml
version: '3.8'
services:
  app:
    build: .
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - DB_HOST=mysql
      - REDIS_HOST=redis
    depends_on:
      - mysql
      - redis

  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: root123
      MYSQL_DATABASE: recycle_db
    volumes:
      - mysql_data:/var/lib/mysql

  redis:
    image: redis:6.0-alpine
    volumes:
      - redis_data:/data

volumes:
  mysql_data:
  redis_data:
```

### 生产环境部署
```bash
# 使用systemd管理服务
sudo cp manman-recycle.service /etc/systemd/system/
sudo systemctl enable manman-recycle
sudo systemctl start manman-recycle

# 查看服务状态
sudo systemctl status manman-recycle

# 查看服务日志
sudo journalctl -u manman-recycle -f
```

## 🔒 安全

### 认证授权
- JWT Token认证
- 微信小程序登录
- 接口权限控制
- 用户状态验证

### 数据安全
- SQL注入防护
- XSS攻击防护
- 敏感信息加密
- 接口限流保护

### 配置安全
```yaml
# 生产环境安全配置
app:
  jwt:
    secret: ${JWT_SECRET:your-production-secret}
  wechat:
    app-secret: ${WECHAT_APP_SECRET:your-secret}

spring:
  datasource:
    password: ${DB_PASSWORD:your-db-password}
  redis:
    password: ${REDIS_PASSWORD:your-redis-password}
```

## 📈 性能优化

### 数据库优化
- 合理的索引设计
- 分页查询优化
- 连接池配置优化
- 慢SQL监控

### 缓存优化
- Redis缓存热点数据
- 查询结果缓存
- 分布式缓存
- 缓存更新策略

### 接口优化
- 响应数据压缩
- 异步处理
- 批量操作
- 接口限流

## 🤝 贡献指南

### 开发规范
- 遵循阿里巴巴Java开发手册
- 使用统一的代码格式化配置
- 编写完整的单元测试
- 添加详细的接口文档

### 提交规范
```bash
# 功能开发
git commit -m "feat: 添加价格预警功能"

# 问题修复
git commit -m "fix: 修复微信登录异常"

# 文档更新
git commit -m "docs: 更新API文档"
```

## 📞 支持

### 问题反馈
- GitHub Issues: [项目Issues](https://github.com/your-org/manman-recycle-java/issues)
- 邮箱: <EMAIL>

### 技术支持
- 开发文档: [Wiki](https://github.com/your-org/manman-recycle-java/wiki)
- API文档: http://localhost:8080/api/swagger-ui/
- 常见问题: [FAQ](./FAQ.md)

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

---

**🌿 慢慢回收 - 让废品回收更简单、更环保！**
