.songItemBox {
    display: flex;
    align-items: center;
    height: 30px;
   background-color: rgb(255,255,255);
}
.songItemBox:nth-child(odd) {
    background: rgb(247,247,247) !important;
}

.songItemBox:nth-child(-n + 3) {
    height: 70px ;
}
.songItemBox .songItemRankCount {
    padding: 6px 10px 6px 25px;
    line-height: 18px;
    text-align: left;
    width: 70px;
    color: #999;
}

.songItemBox .songItemImg {
    padding: 6px 10px;
    line-height: 18px;
    text-align: left;
}

.songItemBox .songItemInfo {
    display: flex;
    justify-content: space-between;
}
.songItemBox .songItemInfo .songItemName {
    font-size: 15px;
    cursor: pointer;
    margin-right: 8px;
}
.songItemBox .songItemInfo .songItemName:hover {
    color: #d31111;
}
.songItemBox .songItemInfo .songItemName:active {
    color: #d31111;
}
.songItemBox .songItemInfo .songItemButton {
    width: 17px;
    height: 17px;
    margin-left: 8px;
    cursor: pointer;
    position: relative;
    top: 2px;
    background: url(../../../../static/images/sprite_icon2.png);
    background-position: 0 -700px;
}
.songItemBox .songItemInfo>a {
    display: inline-block;
    width: 190px;
}
.songItemBox .songItemDuration {
    width: 91px;
}