import React, { memo } from 'react'
import propTypes from 'prop-types'
import { getSizeImage } from '../../../../utils/formatUtils'
import './style.css'
function SingerItem(props: any) {
  // props/state
  const { coverP<PERSON>, singer } = props

  const picUrl = (coverPic && getSizeImage(coverPic, 130)) || 'https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fimg3.doubanio.com%2Fview%2Fgroup_topic%2Fl%2Fpublic%2Fp459379030.jpg&refer=http%3A%2F%2Fimg3.doubanio.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=jpeg?sec=1645618070&t=f1d8339204b86fde1c6959c779fcb504'
  return (
    <div className="singerItemBox">
      <div className="singerItemImage">
        <img src={picUrl} alt="" />
        <span className="singerItemImageCover"></span>
      </div>
      <p className="singerItemInfo">
        <span>{singer}</span>
        <i className="singerItemIcon"></i>
      </p>
    </div>
  )
}

SingerItem.propTypes = {
  coverPic: propTypes.string.isRequired,
  singer: propTypes.string.isRequired,
}

export default memo(SingerItem)
