export function handleSongsCategory(data: any) {
    // 1.获取所有的类别
    const category = data.categories;
  
    // 2.创建类别数据结构
    const categoryData = Object.entries(category).map(([key, value]) => {
      return {
        name: value,
        subs: []as any
      }
    })
  
    // 3.将subs添加到对应的类别中
    for (let item of data.sub) {
      categoryData[item.category].subs.push(item);
    }
  
    return categoryData;
}

const parseExp = /\[([0-9]{2}):([0-9]{2})\.([0-9]{2,3})\]/
export function parseLyric(lyrics: any) {
  if(!lyrics) return
  const lineStrings = lyrics.split('\n')
  const lyricList = []
  for (const line of lineStrings) {
    if (line) {
      const result: any = parseExp.exec(line)
      if(!result) continue
      const time1 = result[1] * 60 * 1000
      const time2 = result[2] * 1000
      const time3 = result[3].length > 2 ? result[3] * 1 : result[3] * 1000
      // 当前歌曲播放的总时长(毫秒)
      const totalTime = time1 + time2 + time3
      const content = line.replace(parseExp, '').trim()
      const lineObj = {totalTime, content};
      lyricList.push(lineObj)
    }
  }
  return lyricList
}

export function formatDate(time: any, fmt: string) {
  let date = new Date(time)

  if (/(y+)/.test(fmt)) {
    fmt = fmt.replace(
      RegExp.$1,
      (date.getFullYear() + '').substr(4 - RegExp.$1.length)
    )
  }
  let o: any = {
    'M+': date.getMonth() + 1,
    'd+': date.getDate(),
    'h+': date.getHours(),
    'm+': date.getMinutes(),
    's+': date.getSeconds(),
  }
  for (let k in o) {
    if (new RegExp(`(${k})`).test(fmt)) {
      let str = o[k] + ''
      fmt = fmt.replace(
        RegExp.$1,
        RegExp.$1.length === 1 ? str : padLeftZero(str)
      )
    }
  }
  return fmt
}
function padLeftZero(str: any) {
  return ('00' + str).substr(str.length)
}

export function formatMonthDay(time: any) {
  return formatDate(time, 'MM月dd日')
}

export function formatMinuteSecond(time: any) {
  return formatDate(time, 'mm:ss')
}



export function generateSingerAlpha() {
  var alphabets = ["-1"];
  var start = 'A'.charCodeAt(0);
  var last = 'Z'.charCodeAt(0);
  for (var i = start; i <= last; ++i) {
      alphabets.push(String.fromCharCode(i));
  }

  alphabets.push("0");

  return alphabets;
}

export const singerAlphas = generateSingerAlpha();


export function getPlayUrl(id: any) {
  return `https://music.163.com/song/media/outer/url?id=${id}.mp3`
}
