package com.c2brecycle.service;

import com.c2brecycle.entity.Category;

import java.util.List;

/**
 * 分类服务接口
 * 
 * <AUTHOR> Recycle Team
 * @since 2024-01-15
 */
public interface CategoryService {

    /**
     * 获取所有分类
     * 
     * @return 分类列表
     */
    List<Category> getAllCategories();

    /**
     * 根据ID获取分类
     * 
     * @param categoryId 分类ID
     * @return 分类信息
     */
    Category getCategoryById(Long categoryId);
}
