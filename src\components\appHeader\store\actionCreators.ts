import { MusicDetail } from '../../../models/music';
import { getSearchSongData } from '../../../request/header';
import * as actionTypes from './actionTypes';

// type getSearchSongDataResType = {
//   code: number;
//   result: {
//     hasMore: boolean;
//     songs: Array<MusicDetail>;
//   }
// }

// 搜索歌曲Action
const changeSearchSongListAction = (searchSongList: Array<MusicDetail>) => ({ 
  type: actionTypes.CHANGE_SEARCH_SONG_LIST,
  searchSongList,
})
// 改变焦点状态
export const changeFocusStateAction = (focusState: boolean) => ({
  type: actionTypes.CHANGE_FOCUS_STATE,
  focusState
})

// 搜索歌曲network
export const getSearchSongListAction = (searchStr: string) => {
  return (dispatch: Function) => {
    getSearchSongData(searchStr).then((res: any) => {
      const songList = res.result && res.result.songs
      dispatch(changeSearchSongListAction(songList));
    });
  };
};
