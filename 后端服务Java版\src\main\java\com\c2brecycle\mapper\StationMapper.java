package com.c2brecycle.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.c2brecycle.entity.Station;
import com.c2brecycle.vo.StationVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.util.List;

/**
 * 站点Mapper接口
 * 
 * <AUTHOR> Recycle Team
 * @since 2024-01-15
 */
@Mapper
public interface StationMapper extends BaseMapper<Station> {

    /**
     * 查询附近站点
     * 
     * @param page 分页参数
     * @param lng 经度
     * @param lat 纬度
     * @param radius 半径(km)
     * @return 站点列表
     */
    IPage<StationVO> selectNearbyStations(Page<StationVO> page, 
                                         @Param("lng") BigDecimal lng, 
                                         @Param("lat") BigDecimal lat, 
                                         @Param("radius") Integer radius);

    /**
     * 搜索站点
     * 
     * @param page 分页参数
     * @param keyword 关键词
     * @param city 城市
     * @return 站点列表
     */
    IPage<StationVO> searchStations(Page<StationVO> page, 
                                   @Param("keyword") String keyword, 
                                   @Param("city") String city);

    /**
     * 获取热门站点
     * 
     * @param city 城市
     * @param limit 数量限制
     * @return 热门站点列表
     */
    @Select("SELECT * FROM stations WHERE status = 1 AND deleted = 0 " +
            "AND (#{city} IS NULL OR city = #{city}) " +
            "ORDER BY rating DESC, review_count DESC " +
            "LIMIT #{limit}")
    List<StationVO> selectHotStations(@Param("city") String city, @Param("limit") Integer limit);
}
