package com.c2brecycle.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.c2brecycle.dto.AddressDTO;
import com.c2brecycle.dto.FavoriteDTO;
import com.c2brecycle.dto.UpdateUserDTO;
import com.c2brecycle.entity.UserAddress;
import com.c2brecycle.vo.FavoriteVO;
import com.c2brecycle.vo.UserProfileVO;

import java.util.List;

/**
 * 用户服务接口
 * 
 * <AUTHOR> Recycle Team
 * @since 2024-01-15
 */
public interface UserService {

    /**
     * 获取用户信息
     * 
     * @param userId 用户ID
     * @return 用户信息
     */
    UserProfileVO getUserProfile(Long userId);

    /**
     * 更新用户信息
     * 
     * @param userId 用户ID
     * @param updateDTO 更新参数
     */
    void updateUserProfile(Long userId, UpdateUserDTO updateDTO);

    /**
     * 获取用户地址列表
     * 
     * @param userId 用户ID
     * @return 地址列表
     */
    List<UserAddress> getUserAddresses(Long userId);

    /**
     * 添加用户地址
     * 
     * @param userId 用户ID
     * @param addressDTO 地址信息
     */
    void addUserAddress(Long userId, AddressDTO addressDTO);

    /**
     * 更新用户地址
     * 
     * @param userId 用户ID
     * @param addressId 地址ID
     * @param addressDTO 地址信息
     */
    void updateUserAddress(Long userId, Long addressId, AddressDTO addressDTO);

    /**
     * 删除用户地址
     * 
     * @param userId 用户ID
     * @param addressId 地址ID
     */
    void deleteUserAddress(Long userId, Long addressId);

    /**
     * 获取用户收藏
     * 
     * @param userId 用户ID
     * @param type 收藏类型
     * @param page 页码
     * @param size 每页数量
     * @return 收藏列表
     */
    IPage<FavoriteVO> getUserFavorites(Long userId, Integer type, Integer page, Integer size);

    /**
     * 添加收藏
     * 
     * @param userId 用户ID
     * @param favoriteDTO 收藏参数
     */
    void addFavorite(Long userId, FavoriteDTO favoriteDTO);

    /**
     * 取消收藏
     * 
     * @param userId 用户ID
     * @param favoriteId 收藏ID
     */
    void removeFavorite(Long userId, Long favoriteId);
}
