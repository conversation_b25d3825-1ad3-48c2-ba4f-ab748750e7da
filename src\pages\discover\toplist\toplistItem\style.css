.toplistItemBox h3{
    font-weight: bold;
    padding: 0 10px 6px 15px;
    font-size: 14px;
    font-family: "宋体";
    color: #000;
}

.toplistItemBox .toplistItemInfo {
    display: flex;
    padding: 10px 0 10px 20px;
    height: 62px;
    text-decoration: none;
    cursor: pointer;
}
.toplistItemBox .toplistItemInfo:hover {
    background-color: #f4f2f2;
}
.toplistItemBox .toplistItemBg {
    background-color: #e6e6e6;
}
.toplistItemBox .toplistItemInfo .toplistItemInfoRight {
    margin-left: 10px;
}
.toplistItemBox .toplistItemInfo .toplistItemInfoRight .toplistItemInfoTitle {
    width: 150px;
    overflow: hidden;
    margin-top: 2px;
    margin-bottom: 8px;
    color: #000;
}
.toplistItemBox .toplistItemInfo .toplistItemInfoRight .toplistItemInfoUpdate {
    color: #999;
}